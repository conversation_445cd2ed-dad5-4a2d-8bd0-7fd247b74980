package cn.iocoder.yudao.module.sms.controller.admin.ttfinalinvoice.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - TT尾款发票分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TtFinalInvoicePageReqVO extends PageParam {

    @Schema(description = "状态")
    private String state;

    @Schema(description = "PARENTID")
    private Long parentid;

    @Schema(description = "发票类型")
    private String invoiceType;

    @Schema(description = "记录编号")
    private String ttFinalInvoiceNo;

    @Schema(description = "外贸合同")
    private String contractTradeId;

    @Schema(description = "发票编号")
    private String invoiceNumber;

    @Schema(description = "海外代理公司")
    private String agencyCompany;

    @Schema(description = "客户")
    private String buyerId;

    @Schema(description = "产品来源")
    private String productCode;

    @Schema(description = "产品大类")
    private String productBigCode;

    @Schema(description = "采购产品名称")
    private String productFormName;

    @Schema(description = "采购产品")
    private String productFormCode;

    @Schema(description = "合同金额")
    private BigDecimal amount;

    @Schema(description = "价格条款")
    private String bargain;

    @Schema(description = "开票日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] invoiceDate;

    @Schema(description = "装货港")
    private String shippingPort;

    @Schema(description = "卸货港")
    private String destinationPort;

    @Schema(description = "预付款金额")
    private BigDecimal prepaymentAmount;

    @Schema(description = "尾款金额")
    private BigDecimal finalPaymentAmount;

    @Schema(description = "银行名称")
    private String bankName;

    @Schema(description = "银行代码")
    private String bankCode;

    @Schema(description = "业务部门名称", example = "芋艿")
    private String deptName;

    @Schema(description = "业务部门")
    private String deptCode;

    @Schema(description = "业务员名称", example = "张三")
    private String salesmanName;

    @Schema(description = "业务员")
    private String salesmanCode;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "预留字段1 ")
    private String preText1;

    @Schema(description = "预留字段2 ")
    private String preText2;

    @Schema(description = "预留字段3 ")
    private String preText3;

    @Schema(description = "预留字段4 ")
    private String preText4;

    @Schema(description = "预留字段5 ")
    private String preText5;

    @Schema(description = "预留字段6 ")
    private String preText6;

    @Schema(description = "预留字段7 ")
    private String preText7;

    @Schema(description = "预留字段8 ")
    private String preText8;

    @Schema(description = "预留字段9 ")
    private String preText9;

    @Schema(description = "预留字段10 ")
    private String preText10;

    @Schema(description = "预留字段11 ")
    private BigDecimal preNum11;

    @Schema(description = "预留字段12 ")
    private BigDecimal preNum12;

    @Schema(description = "预留字段13 ")
    private BigDecimal preNum13;

    @Schema(description = "预留字段14 ")
    private BigDecimal preNum14;

    @Schema(description = "预留字段15 ")
    private BigDecimal preNum15;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "重量")
    private BigDecimal orderWeight;

    @Schema(description = "附件")
    private String annex;
}
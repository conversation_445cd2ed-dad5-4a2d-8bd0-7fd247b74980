package cn.iocoder.yudao.module.sms.controller.admin.adjustmentmaster;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.sms.controller.admin.adjustmentmaster.vo.*;
import cn.iocoder.yudao.module.sms.dal.dataobject.adjustmentmaster.AdjustmentMasterDO;
import cn.iocoder.yudao.module.sms.dal.dataobject.adjustmentmaster.AdjustmentDetailDO;
import cn.iocoder.yudao.module.sms.service.adjustmentmaster.AdjustmentMasterService;

@Tag(name = "管理后台 - 销售调整原因主档")
@RestController
@RequestMapping("/sms/adjustment-master")
@Validated
public class AdjustmentMasterController {

    @Resource
    private AdjustmentMasterService adjustmentMasterService;

    @PostMapping("/create")
    @Operation(summary = "创建销售调整原因主档")
    @PreAuthorize("@ss.hasPermission('sms:adjustment-master:create')")
    public CommonResult<Long> createAdjustmentMaster(@Valid @RequestBody AdjustmentMasterSaveReqVO createReqVO) {
        return success(adjustmentMasterService.createAdjustmentMaster(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新销售调整原因主档")
    @PreAuthorize("@ss.hasPermission('sms:adjustment-master:update')")
    public CommonResult<Boolean> updateAdjustmentMaster(@Valid @RequestBody AdjustmentMasterSaveReqVO updateReqVO) {
        adjustmentMasterService.updateAdjustmentMaster(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除销售调整原因主档")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sms:adjustment-master:delete')")
    public CommonResult<Boolean> deleteAdjustmentMaster(@RequestParam("id") Long id) {
        adjustmentMasterService.deleteAdjustmentMaster(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得销售调整原因主档")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sms:adjustment-master:query')")
    public CommonResult<AdjustmentMasterRespVO> getAdjustmentMaster(@RequestParam("id") Long id) {
        AdjustmentMasterDO adjustmentMaster = adjustmentMasterService.getAdjustmentMaster(id);
        return success(BeanUtils.toBean(adjustmentMaster, AdjustmentMasterRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得销售调整原因主档分页")
    @PreAuthorize("@ss.hasPermission('sms:adjustment-master:query')")
    public CommonResult<PageResult<AdjustmentMasterRespVO>> getAdjustmentMasterPage(@Valid AdjustmentMasterPageReqVO pageReqVO) {
        PageResult<AdjustmentMasterDO> pageResult = adjustmentMasterService.getAdjustmentMasterPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AdjustmentMasterRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出销售调整原因主档 Excel")
    @PreAuthorize("@ss.hasPermission('sms:adjustment-master:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAdjustmentMasterExcel(@Valid AdjustmentMasterPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AdjustmentMasterDO> list = adjustmentMasterService.getAdjustmentMasterPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "销售调整原因主档.xls", "数据", AdjustmentMasterRespVO.class,
                        BeanUtils.toBean(list, AdjustmentMasterRespVO.class));
    }

    // ==================== 子表（销售调整原因明细档） ====================

    @GetMapping("/adjustment-detail/page")
    @Operation(summary = "获得销售调整原因明细档分页")
    @Parameter(name = "parentId", description = "主表ID")
    @PreAuthorize("@ss.hasPermission('sms:adjustment-master:query')")
    public CommonResult<PageResult<AdjustmentDetailDO>> getAdjustmentDetailPage(PageParam pageReqVO,
                                                                                        @RequestParam("parentId") Long parentId) {
        return success(adjustmentMasterService.getAdjustmentDetailPage(pageReqVO, parentId));
    }

    @PostMapping("/adjustment-detail/create")
    @Operation(summary = "创建销售调整原因明细档")
    @PreAuthorize("@ss.hasPermission('sms:adjustment-master:create')")
    public CommonResult<Long> createAdjustmentDetail(@Valid @RequestBody AdjustmentDetailDO adjustmentDetail) {
        return success(adjustmentMasterService.createAdjustmentDetail(adjustmentDetail));
    }

    @PutMapping("/adjustment-detail/update")
    @Operation(summary = "更新销售调整原因明细档")
    @PreAuthorize("@ss.hasPermission('sms:adjustment-master:update')")
    public CommonResult<Boolean> updateAdjustmentDetail(@Valid @RequestBody AdjustmentDetailDO adjustmentDetail) {
        adjustmentMasterService.updateAdjustmentDetail(adjustmentDetail);
        return success(true);
    }

    @DeleteMapping("/adjustment-detail/delete")
    @Parameter(name = "id", description = "编号", required = true)
    @Operation(summary = "删除销售调整原因明细档")
    @PreAuthorize("@ss.hasPermission('sms:adjustment-master:delete')")
    public CommonResult<Boolean> deleteAdjustmentDetail(@RequestParam("id") Long id) {
        adjustmentMasterService.deleteAdjustmentDetail(id);
        return success(true);
    }

	@GetMapping("/adjustment-detail/get")
	@Operation(summary = "获得销售调整原因明细档")
	@Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sms:adjustment-master:query')")
	public CommonResult<AdjustmentDetailDO> getAdjustmentDetail(@RequestParam("id") Long id) {
	    return success(adjustmentMasterService.getAdjustmentDetail(id));
	}

}
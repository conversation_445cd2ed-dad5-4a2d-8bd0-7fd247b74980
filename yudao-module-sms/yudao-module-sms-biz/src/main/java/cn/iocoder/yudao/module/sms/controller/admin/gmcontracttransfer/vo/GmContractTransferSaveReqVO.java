package cn.iocoder.yudao.module.sms.controller.admin.gmcontracttransfer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 外贸合同交接新增/修改 Request VO")
@Data
public class GmContractTransferSaveReqVO {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "合同交接编号")
    private String contractTransferNo;

    @Schema(description = "外贸合同", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "外贸合同不能为空")
    private String contractTradeNo;

    @Schema(description = "外贸合同ID")
    private String contractTradeId;

    @Schema(description = "合同数量")
    private String contractNum;

    @Schema(description = "合同单价")
    private String contractPrice;

    @Schema(description = "合同金额")
    private String contractAmount;

    @Schema(description = "收款金额")
    private String collectAmount;

    @Schema(description = "到款时间")
    private String accountTime;

    @Schema(description = "客户")
    private String custName;

    @Schema(description = "接收人")
    private String recipient;

    @Schema(description = "下至何月资源")
    private String resourceMonth;

    @Schema(description = "出口国家")
    private String desCountry;

    @Schema(description = "发运港口")
    private String desPort;

    @Schema(description = "运输方式")
    private String transWay;

    @Schema(description = "备货日期")
    private String stockupTime;

    @Schema(description = "最晚装期")
    private String deliveryTimeTrand;

    @Schema(description = "排产方式")
    private String scheduleProduct;

    @Schema(description = "贸易方")
    private String trader;

    @Schema(description = "交接意见")
    private String connnectAdvice;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "双签附件")
    private String fileListSq;

    @Schema(description = "合同附件")
    private String fileListContract;

    @Schema(description = "付款附件")
    private String fileListAnnex;

}

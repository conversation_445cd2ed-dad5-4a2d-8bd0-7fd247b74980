package cn.iocoder.yudao.module.sms.controller.admin.adjustmentmaster.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 销售调整原因主档 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AdjustmentMasterRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "6121")
    @ExcelProperty("ID")
    private Long id;

    @Schema(description = "项目号")
    @ExcelProperty("项目号")
    private String project;

    @Schema(description = "公司别", example = "15839")
    @ExcelProperty("公司别")
    private String compId;

    @Schema(description = "原因代码")
    @ExcelProperty("原因代码")
    private String adjustReasonNo;

    @Schema(description = "原因说明", example = "不好")
    @ExcelProperty("原因说明")
    private String adjustReason;

    @Schema(description = "附件")
    @ExcelProperty("附件")
    private String annex;

    @Schema(description = "最后维护人员")
    @ExcelProperty("最后维护人员")
    private String lastEditBy;

    @Schema(description = "创建者")
    @ExcelProperty("创建者")
    private String creator;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新者")
    @ExcelProperty("更新者")
    private String updater;

    @Schema(description = "更新时间")
    @ExcelProperty("更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "最后修改日期")
    @ExcelProperty("最后修改日期")
    private String lastEditDate;

    @Schema(description = "创建者姓名")
    @ExcelProperty("创建者姓名")
    private String createEmpNo;

    @Schema(description = "更新者姓名")
    @ExcelProperty("更新者姓名")
    private String updateEmpNo;

    @Schema(description = "最后维护人员姓名", example = "王五")
    @ExcelProperty("最后维护人员姓名")
    private String lastEditByName;
}
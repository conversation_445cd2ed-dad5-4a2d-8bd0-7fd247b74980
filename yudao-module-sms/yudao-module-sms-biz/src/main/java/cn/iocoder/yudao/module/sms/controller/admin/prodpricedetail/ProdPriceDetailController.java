package cn.iocoder.yudao.module.sms.controller.admin.prodpricedetail;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.sms.controller.admin.prodpricedetail.vo.*;
import cn.iocoder.yudao.module.sms.dal.dataobject.prodpricedetail.ProdPriceDetailDO;
import cn.iocoder.yudao.module.sms.service.prodpricedetail.ProdPriceDetailService;

@Tag(name = "管理后台 - 产品定价明细")
@RestController
@RequestMapping("/sms/prod-price-detail")
@Validated
public class ProdPriceDetailController {

    @Resource
    private ProdPriceDetailService prodPriceDetailService;

    @PostMapping("/create")
    @Operation(summary = "创建产品定价明细")
//    @PreAuthorize("@ss.hasPermission('sms:prod-price-detail:create')")
    public CommonResult<Long> createProdPriceDetail(@Valid @RequestBody ProdPriceDetailSaveReqVO createReqVO) {
        return success(prodPriceDetailService.createProdPriceDetail(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新产品定价明细")
//    @PreAuthorize("@ss.hasPermission('sms:prod-price-detail:update')")
    public CommonResult<Boolean> updateProdPriceDetail(@Valid @RequestBody ProdPriceDetailSaveReqVO updateReqVO) {
        prodPriceDetailService.updateProdPriceDetail(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除产品定价明细")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('sms:prod-price-detail:delete')")
    public CommonResult<Boolean> deleteProdPriceDetail(@RequestParam("id") Long id) {
        prodPriceDetailService.deleteProdPriceDetail(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得产品定价明细")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('sms:prod-price-detail:query')")
    public CommonResult<ProdPriceDetailRespVO> getProdPriceDetail(@RequestParam("id") Long id) {
        ProdPriceDetailDO prodPriceDetail = prodPriceDetailService.getProdPriceDetail(id);
        return success(BeanUtils.toBean(prodPriceDetail, ProdPriceDetailRespVO.class));
    }

    @GetMapping("/allpage")
    @Operation(summary = "获得产品定价明细分页")
//    @PreAuthorize("@ss.hasPermission('sms:prod-price-detail:query')")
    public CommonResult<PageResult<ProdPriceDetailRespVO>> getAllProdPriceDetailPage(@Valid ProdPriceDetailPageReqVO pageReqVO) {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        PageResult<ProdPriceDetailDO> pageResult = prodPriceDetailService.getProdPriceDetailPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ProdPriceDetailRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得产品定价明细分页")
//    @PreAuthorize("@ss.hasPermission('sms:prod-price-detail:query')")
    public CommonResult<PageResult<ProdPriceDetailRespVO>> getProdPriceDetailPage(@Valid ProdPriceDetailPageReqVO pageReqVO) {
        PageResult<ProdPriceDetailDO> pageResult = prodPriceDetailService.getProdPriceDetailPage2(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ProdPriceDetailRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出产品定价明细 Excel")
//    @PreAuthorize("@ss.hasPermission('sms:prod-price-detail:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportProdPriceDetailExcel(@Valid ProdPriceDetailPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ProdPriceDetailDO> list = prodPriceDetailService.getProdPriceDetailPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "产品定价明细.xls", "数据", ProdPriceDetailRespVO.class,
                        BeanUtils.toBean(list, ProdPriceDetailRespVO.class));
    }

}

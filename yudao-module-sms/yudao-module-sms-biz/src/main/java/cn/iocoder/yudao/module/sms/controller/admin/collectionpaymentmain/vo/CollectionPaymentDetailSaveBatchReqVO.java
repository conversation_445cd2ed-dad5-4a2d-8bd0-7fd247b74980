package cn.iocoder.yudao.module.sms.controller.admin.collectionpaymentmain.vo;

import cn.iocoder.yudao.module.sms.dal.dataobject.collectionpaymentmain.CollectionPaymentDetailDO;
import cn.iocoder.yudao.module.sms.dal.dataobject.transportexpense.TransportExpenseOffsetDetailDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 销售配货装车主档新增/修改 Request VO")
@Data
public class CollectionPaymentDetailSaveBatchReqVO {
    private Long parentid;
    private String type;
    private List<CollectionPaymentDetailDO> items;
}
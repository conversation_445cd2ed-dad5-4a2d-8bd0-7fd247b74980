package cn.iocoder.yudao.module.sms.controller.admin.contractinquiry;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.sms.controller.admin.contractinquiry.vo.*;
import cn.iocoder.yudao.module.sms.dal.dataobject.contractinquiry.ContractInquiryDO;
import cn.iocoder.yudao.module.sms.service.contractinquiry.ContractInquiryService;

@Tag(name = "管理后台 - 询单管理")
@RestController
@RequestMapping("/sms/contract-inquiry")
@Validated
public class ContractInquiryController {

    @Resource
    private ContractInquiryService contractInquiryService;

    @PostMapping("/create")
    @Operation(summary = "创建询单管理")
    @PreAuthorize("@ss.hasPermission('sms:contract-inquiry:create')")
    public CommonResult<Long> createContractInquiry(@Valid @RequestBody ContractInquirySaveReqVO createReqVO) {
        return success(contractInquiryService.createContractInquiry(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新询单管理")
    @PreAuthorize("@ss.hasPermission('sms:contract-inquiry:update')")
    public CommonResult<Boolean> updateContractInquiry(@Valid @RequestBody ContractInquirySaveReqVO updateReqVO) {
        contractInquiryService.updateContractInquiry(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除询单管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sms:contract-inquiry:delete')")
    public CommonResult<Boolean> deleteContractInquiry(@RequestParam("id") Long id) {
        contractInquiryService.deleteContractInquiry(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得询单管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sms:contract-inquiry:query')")
    public CommonResult<ContractInquiryRespVO> getContractInquiry(@RequestParam("id") Long id) {
        ContractInquiryDO contractInquiry = contractInquiryService.getContractInquiry(id);
        return success(BeanUtils.toBean(contractInquiry, ContractInquiryRespVO.class));
    }

    @GetMapping("/getbyNo")
    @Operation(summary = "获得询单管理")
    @Parameter(name = "id", description = "编号", required = true, example = "WMXD20250905000008")
    @PreAuthorize("@ss.hasPermission('sms:contract-inquiry:query')")
    public CommonResult<ContractInquiryRespVO> getContractInquiry(@RequestParam("id") String inquiryOrderNo) {
        ContractInquiryPageReqVO pageReqVO = new ContractInquiryPageReqVO();
        pageReqVO.setInquiryOrderNo(inquiryOrderNo);
        PageResult<ContractInquiryDO> pageResult = contractInquiryService.getContractInquiryPage(pageReqVO);
        if (pageResult.getList().size() > 0){
            ContractInquiryDO contractInquiry = pageResult.getList().get(0);
            return success(BeanUtils.toBean(contractInquiry, ContractInquiryRespVO.class));
        } else {
            return success(null);
        }

    }


    @GetMapping("/page")
    @Operation(summary = "获得询单管理分页")
    @PreAuthorize("@ss.hasPermission('sms:contract-inquiry:query')")
    public CommonResult<PageResult<ContractInquiryRespVO>> getContractInquiryPage(@Valid ContractInquiryPageReqVO pageReqVO) {
        PageResult<ContractInquiryDO> pageResult = contractInquiryService.getContractInquiryPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ContractInquiryRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出询单管理 Excel")
    @PreAuthorize("@ss.hasPermission('sms:contract-inquiry:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportContractInquiryExcel(@Valid ContractInquiryPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ContractInquiryDO> list = contractInquiryService.getContractInquiryPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "询单管理.xls", "数据", ContractInquiryRespVO.class,
                        BeanUtils.toBean(list, ContractInquiryRespVO.class));
    }

    @GetMapping("/effect")
    @Operation(summary = "生效")
    @Parameter(name = "id", description = "编号", required = true)
    public void effectiveContractInquiry(@RequestParam("id") Long id) {
        contractInquiryService.effectiveContractInquiry(id);
    }

    @GetMapping("/invalid")
    @Operation(summary = "取消生效")
    @Parameter(name = "id", description = "编号", required = true)
    public void invalidContractInquiry(@RequestParam("id") Long id) {
        contractInquiryService.invalidContractInquiry(id);
    }


}

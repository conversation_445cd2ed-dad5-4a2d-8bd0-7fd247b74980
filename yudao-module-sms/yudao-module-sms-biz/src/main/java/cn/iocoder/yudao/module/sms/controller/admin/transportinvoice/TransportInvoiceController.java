package cn.iocoder.yudao.module.sms.controller.admin.transportinvoice;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.sms.controller.admin.transportinvoice.vo.*;
import cn.iocoder.yudao.module.sms.dal.dataobject.transportinvoice.TransportInvoiceDO;
import cn.iocoder.yudao.module.sms.service.transportinvoice.TransportInvoiceService;

@Tag(name = "管理后台 - 运杂费发票管理")
@RestController
@RequestMapping("/sms/transport-invoice")
@Validated
public class TransportInvoiceController {

    @Resource
    private TransportInvoiceService transportInvoiceService;

    @PostMapping("/create")
    @Operation(summary = "创建运杂费发票管理")
    @PreAuthorize("@ss.hasPermission('sms:transport-invoice:create')")
    public CommonResult<Long> createTransportInvoice(@Valid @RequestBody TransportInvoiceSaveReqVO createReqVO) {
        return success(transportInvoiceService.createTransportInvoice(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新运杂费发票管理")
    @PreAuthorize("@ss.hasPermission('sms:transport-invoice:update')")
    public CommonResult<Boolean> updateTransportInvoice(@Valid @RequestBody TransportInvoiceSaveReqVO updateReqVO) {
        transportInvoiceService.updateTransportInvoice(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除运杂费发票管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sms:transport-invoice:delete')")
    public CommonResult<Boolean> deleteTransportInvoice(@RequestParam("id") Long id) {
        transportInvoiceService.deleteTransportInvoice(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得运杂费发票管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sms:transport-invoice:query')")
    public CommonResult<TransportInvoiceRespVO> getTransportInvoice(@RequestParam("id") Long id) {
        TransportInvoiceDO transportInvoice = transportInvoiceService.getTransportInvoice(id);
        return success(BeanUtils.toBean(transportInvoice, TransportInvoiceRespVO.class));
    }

    @GetMapping("/auditTransportInvoice")
    @Operation(summary = "发起审批")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sms:transport-invoice:query')")
    public CommonResult<Boolean> auditTransportInvoice(@RequestParam("id") Long id) {
        transportInvoiceService.auditTransportInvoice(id);
        return success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "获得运杂费发票管理分页")
    @PreAuthorize("@ss.hasPermission('sms:transport-invoice:query')")
    public CommonResult<PageResult<TransportInvoiceRespVO>> getTransportInvoicePage(@Valid TransportInvoicePageReqVO pageReqVO) {
        PageResult<TransportInvoiceDO> pageResult = transportInvoiceService.getTransportInvoicePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TransportInvoiceRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出运杂费发票管理 Excel")
    @PreAuthorize("@ss.hasPermission('sms:transport-invoice:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTransportInvoiceExcel(@Valid TransportInvoicePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TransportInvoiceDO> list = transportInvoiceService.getTransportInvoicePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "运杂费发票管理.xls", "数据", TransportInvoiceRespVO.class,
                        BeanUtils.toBean(list, TransportInvoiceRespVO.class));
    }

}
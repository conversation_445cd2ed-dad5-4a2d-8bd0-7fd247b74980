package cn.iocoder.yudao.module.sms.controller.admin.shippingdetail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 配船明细表明细项次档 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ShippingDetailItemsRespVO {
    @Schema(description = "出货日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("出货日期")
    private String shippingDate;

    @Schema(description = "车号")
    @ExcelProperty("车号")
    private String truckNumber;

    @Schema(description = "装车作业单")
    @ExcelProperty("装车作业单")
    private String loadingOrderNumber;

    @Schema(description = "通知单号")
    @ExcelProperty("通知单号")
    private String notificationNumber;

    @Schema(description = "订单项次号")
    @ExcelProperty("订单项次号")
    private String orderItemNumber;

    @Schema(description = "销售渠道")
    @ExcelProperty("销售渠道")
    private String salesChannel;

    @Schema(description = "外销合同号")
    @ExcelProperty("外销合同号")
    private String exportContractNumber;

    @Schema(description = "产品来源")
    @ExcelProperty("产品来源")
    private String productCode;

    @Schema(description = "产品大类")
    @ExcelProperty("产品大类")
    private String productBigCode;

    @Schema(description = "产品形态")
    @ExcelProperty("产品形态")
    private String productFormCode;

    @Schema(description = "产品形态名称", example = "芋艿")
    @ExcelProperty("产品形态名称")
    private String productFormName;

    @Schema(description = "钢卷编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("钢卷编号")
    private String coilNumber;

    @Schema(description = "材质")
    @ExcelProperty("材质")
    private String materialGrade;

    @Schema(description = "规格")
    @ExcelProperty("规格")
    private String specification;

    @Schema(description = "锌花种类", example = "2")
    @ExcelProperty("锌花种类")
    private String spangleType;

    @Schema(description = "锌层")
    @ExcelProperty("锌层")
    private BigDecimal zincCoatingWeight;

    @Schema(description = "数量")
    @ExcelProperty("数量")
    private Integer quantity;

    @Schema(description = "重量")
    @ExcelProperty("重量")
    private BigDecimal weight;

    @Schema(description = "到站")
    @ExcelProperty("到站")
    private String destinationStation;

}
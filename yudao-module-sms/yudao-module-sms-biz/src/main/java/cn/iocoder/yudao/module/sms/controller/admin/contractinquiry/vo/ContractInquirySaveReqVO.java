package cn.iocoder.yudao.module.sms.controller.admin.contractinquiry.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 询单管理新增/修改 Request VO")
@Data
public class ContractInquirySaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "询单编号")
//    @NotEmpty(message = "询单编号不能为空")
    private String inquiryOrderNo;

    @Schema(description = "合同编号")
//    @NotEmpty(message = "合同编号不能为空")
    private String contractNo;

    @Schema(description = "客户名称")
    private String custName;

    @Schema(description = "客户地址")
    private String custAddress;

    @Schema(description = "来源")
    private String source;

    @Schema(description = "大类")
    private String prodClass;

    @Schema(description = "形态")
    private String prodType;

    @Schema(description = "名称")
    private String prodName;

    @Schema(description = "计划采购量")
    private String scheduleNum;

    @Schema(description = "资源发放ID")
    private String resourceId;

    @Schema(description = "资源申请ID")
    private String resourceApplyId;

    @Schema(description = "资源总量")
    private String resourceNum;

    @Schema(description = "出口折算基价FOB")
    private String fobPrice;

    @Schema(description = "备案底价ID")
    private String recordPriceId;

    @Schema(description = "备案底价")
    private String recordPrice;

    @Schema(description = "基价价格条件")
    private String priceCondition;

    @Schema(description = "商品材质")
    private String prodTexture;

    @Schema(description = "下至何月资源")
    private String resourceMonth;

    @Schema(description = "是否投保")
    private String insure;

    @Schema(description = "目的国")
    private String desCountry;

    @Schema(description = "目的港")
    private String desPort;

    @Schema(description = "国内时点价格")
    private String homeTimelyPrice;

    @Schema(description = "合同价")
    private String contractPrice;

    @Schema(description = "合同价价格条款")
    private String contractClause;

    @Schema(description = "佣金")
    private String commission;

    @Schema(description = "海运费")
    private String oceanFreight;

    @Schema(description = "可运输量")
    private String transNum;

    @Schema(description = "部门")
    private String dept;

    @Schema(description = "操作人")
    private String operater;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "合同简述")
    private String contractDescribe;

    @Schema(description = "附件")
    private String annex;

    @Schema(description = "汇率")
    private String rate;

    @Schema(description = "出厂含税")
    private String exportPriceTax;

    @Schema(description = "出厂不含税")
    private String exportPrice;

    @Schema(description = "价差不含税")
    private String differPrice;

    @Schema(description = "毛利2")
    private String grossProfit2;

    @Schema(description = "毛利4")
    private String grossProfit4;

    @Schema(description = "成本差")
    private String cosyGap;

}

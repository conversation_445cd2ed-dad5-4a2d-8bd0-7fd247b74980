package cn.iocoder.yudao.module.sms;

import cn.iocoder.yudao.module.bpm.api.task.BpmProcessInstanceApi;
import cn.iocoder.yudao.module.pms.api.commerce.trade.WmsTradeApi;
import cn.iocoder.yudao.module.pms.api.sms.stockLoc.StockLocApi;
import cn.iocoder.yudao.module.pms.api.sms.trade.TradeApi;
import cn.iocoder.yudao.module.system.api.dict.DictDataApi;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * 项目的启动类
 * <AUTHOR>
 */
@SpringBootApplication
@EnableFeignClients(clients = {BpmProcessInstanceApi.class, DictDataApi.class, TradeApi.class, WmsTradeApi.class, StockLocApi.class})
public class SmsServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(SmsServerApplication.class, args);
    }

}

package cn.iocoder.yudao.module.sms.controller.admin.shippingdetail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 配船明细新增/修改 Request VO")
@Data
public class ShippingDetailItemRespVO {

    /**
     * 出货日期
     */
    private String shippingDate;
    /**
     * 车号
     */
    private String truckNumber;
    /**
     * 装车作业单
     */
    private String loadingOrderNumber;

}
package cn.iocoder.yudao.module.sms.controller.admin.gmcontracttransfer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 外贸合同交接 Response VO")
@Data
@ExcelIgnoreUnannotated
public class GmContractTransferRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("ID")
    private Long id;

    @Schema(description = "合同交接编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("合同交接编号")
    private String contractTransferNo;

    @Schema(description = "外贸合同", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("外贸合同")
    private String contractTradeNo;

    @Schema(description = "合同数量")
    @ExcelProperty("合同数量")
    private String contractNum;

    @Schema(description = "合同单价")
    @ExcelProperty("合同单价")
    private String contractPrice;

    @Schema(description = "合同金额")
    @ExcelProperty("合同金额")
    private String contractAmount;

    @Schema(description = "收款金额")
    @ExcelProperty("收款金额")
    private String collectAmount;

    @Schema(description = "到款时间")
    @ExcelProperty("到款时间")
    private String accountTime;

    @Schema(description = "客户")
    @ExcelProperty("客户")
    private String custName;

    @Schema(description = "接收人")
    @ExcelProperty("接收人")
    private String recipient;

    @Schema(description = "下至何月资源")
    @ExcelProperty("下至何月资源")
    private String resourceMonth;

    @Schema(description = "出口国家")
    @ExcelProperty("出口国家")
    private String desCountry;

    @Schema(description = "发运港口")
    @ExcelProperty("发运港口")
    private String desPort;

    @Schema(description = "运输方式")
    @ExcelProperty("运输方式")
    private String transWay;

    @Schema(description = "备货日期")
    @ExcelProperty("备货日期")
    private String stockupTime;

    @Schema(description = "最晚装期")
    @ExcelProperty("最晚装期")
    private String deliveryTimeTrand;

    @Schema(description = "排产方式")
    @ExcelProperty("排产方式")
    private String scheduleProduct;

    @Schema(description = "贸易方")
    @ExcelProperty("贸易方")
    private String trader;

    @Schema(description = "付款附件已审核")
    private String annexFileCheck;

    @Schema(description = "新增时间")
    @ExcelProperty("新增时间")
    private LocalDateTime createTime;

    @Schema(description = "状态")
    private String state;

    @Schema(description = "创建者姓名")
    private String createEmpNo;

    @Schema(description = "更新者者姓名")
    private String updateEmpNo;

    @Schema(description = "流程实例的编号")
    private String processInstanceId;

}

package cn.iocoder.yudao.module.sms.controller.admin.transportexpense;

import cn.iocoder.yudao.module.sms.controller.admin.loadwgt.vo.LoadExpenseDetailSaveBatchReqVO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.sms.controller.admin.transportexpense.vo.*;
import cn.iocoder.yudao.module.sms.dal.dataobject.transportexpense.TransportExpenseDO;
import cn.iocoder.yudao.module.sms.dal.dataobject.transportexpense.TransportExpenseInvoiceDetailDO;
import cn.iocoder.yudao.module.sms.dal.dataobject.transportexpense.TransportExpenseOffsetDetailDO;
import cn.iocoder.yudao.module.sms.service.transportexpense.TransportExpenseService;

@Tag(name = "管理后台 - 运杂费报支管理")
@RestController
@RequestMapping("/sms/transport-expense")
@Validated
public class TransportExpenseController {

    @Resource
    private TransportExpenseService transportExpenseService;

    @PostMapping("/create")
    @Operation(summary = "创建运杂费报支管理")
    @PreAuthorize("@ss.hasPermission('sms:transport-expense:create')")
    public CommonResult<Long> createTransportExpense(@Valid @RequestBody TransportExpenseSaveReqVO createReqVO) {
        return success(transportExpenseService.createTransportExpense(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新运杂费报支管理")
    @PreAuthorize("@ss.hasPermission('sms:transport-expense:update')")
    public CommonResult<Boolean> updateTransportExpense(@Valid @RequestBody TransportExpenseSaveReqVO updateReqVO) {
        transportExpenseService.updateTransportExpense(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除运杂费报支管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sms:transport-expense:delete')")
    public CommonResult<Boolean> deleteTransportExpense(@RequestParam("id") Long id) {
        transportExpenseService.deleteTransportExpense(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得运杂费报支管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sms:transport-expense:query')")
    public CommonResult<TransportExpenseRespVO> getTransportExpense(@RequestParam("id") Long id) {
        TransportExpenseDO transportExpense = transportExpenseService.getTransportExpense(id);
        return success(BeanUtils.toBean(transportExpense, TransportExpenseRespVO.class));
    }

    @GetMapping("/pushNCTransportExpense")
    @Operation(summary = "获得发票明细")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sms:transport-expense:query')")
    public CommonResult<Boolean> pushNCTransportExpense(@RequestParam("id") Long id) {
        transportExpenseService.pushNCTransportExpense(id);
        return success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "获得运杂费报支管理分页")
    @PreAuthorize("@ss.hasPermission('sms:transport-expense:query')")
    public CommonResult<PageResult<TransportExpenseRespVO>> getTransportExpensePage(@Valid TransportExpensePageReqVO pageReqVO) {
        PageResult<TransportExpenseDO> pageResult = transportExpenseService.getTransportExpensePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TransportExpenseRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出运杂费报支管理 Excel")
    @PreAuthorize("@ss.hasPermission('sms:transport-expense:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTransportExpenseExcel(@Valid TransportExpensePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TransportExpenseDO> list = transportExpenseService.getTransportExpensePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "运杂费报支管理.xls", "数据", TransportExpenseRespVO.class,
                        BeanUtils.toBean(list, TransportExpenseRespVO.class));
    }

    // ==================== 子表（发票明细） ====================

    @GetMapping("/transport-expense-invoice-detail/page")
    @Operation(summary = "获得发票明细分页")
    @Parameter(name = "parentid", description = "PARENTID")
    @PreAuthorize("@ss.hasPermission('sms:transport-expense:query')")
    public CommonResult<PageResult<TransportExpenseInvoiceDetailDO>> getTransportExpenseInvoiceDetailPage(PageParam pageReqVO,
                                                                                        @RequestParam("parentid") Long parentid) {
        return success(transportExpenseService.getTransportExpenseInvoiceDetailPage(pageReqVO, parentid));
    }

    @PostMapping("/transport-expense-invoice-detail/create")
    @Operation(summary = "创建发票明细")
    @PreAuthorize("@ss.hasPermission('sms:transport-expense:create')")
    public CommonResult<Long> createTransportExpenseInvoiceDetail(@Valid @RequestBody TransportExpenseInvoiceDetailDO transportExpenseInvoiceDetail) {
        return success(transportExpenseService.createTransportExpenseInvoiceDetail(transportExpenseInvoiceDetail));
    }

    @PutMapping("/transport-expense-invoice-detail/update")
    @Operation(summary = "更新发票明细")
    @PreAuthorize("@ss.hasPermission('sms:transport-expense:update')")
    public CommonResult<Boolean> updateTransportExpenseInvoiceDetail(@Valid @RequestBody TransportExpenseInvoiceDetailDO transportExpenseInvoiceDetail) {
        transportExpenseService.updateTransportExpenseInvoiceDetail(transportExpenseInvoiceDetail);
        return success(true);
    }

    @DeleteMapping("/transport-expense-invoice-detail/delete")
    @Parameter(name = "id", description = "编号", required = true)
    @Operation(summary = "删除发票明细")
    @PreAuthorize("@ss.hasPermission('sms:transport-expense:delete')")
    public CommonResult<Boolean> deleteTransportExpenseInvoiceDetail(@RequestParam("id") Long id) {
        transportExpenseService.deleteTransportExpenseInvoiceDetail(id);
        return success(true);
    }

	@GetMapping("/transport-expense-invoice-detail/get")
	@Operation(summary = "获得发票明细")
	@Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sms:transport-expense:query')")
	public CommonResult<TransportExpenseInvoiceDetailDO> getTransportExpenseInvoiceDetail(@RequestParam("id") Long id) {
	    return success(transportExpenseService.getTransportExpenseInvoiceDetail(id));
	}

    @PostMapping("/transport-expense-invoice-detail/batchCreateInvoice")
    @Operation(summary = "批量创建发票明细")
    @PreAuthorize("@ss.hasPermission('sms:transport-expense:create')")
    public CommonResult<Boolean> batchCreateInvoice(@Valid @RequestBody TransportExpenseInvoiceDetailBatchSaveVO createReqVO) {
        transportExpenseService.batchCreateInvoice(createReqVO);
        return success(true);
    }

    // ==================== 子表（冲抵暂估明细） ====================

    @GetMapping("/transport-expense-offset-detail/page")
    @Operation(summary = "获得冲抵暂估明细分页")
    @Parameter(name = "parentid", description = "PARENTID")
    @PreAuthorize("@ss.hasPermission('sms:transport-expense:query')")
    public CommonResult<PageResult<TransportExpenseOffsetDetailDO>> getTransportExpenseOffsetDetailPage(PageParam pageReqVO,
                                                                                        @RequestParam("parentid") Long parentid) {
        return success(transportExpenseService.getTransportExpenseOffsetDetailPage(pageReqVO, parentid));
    }

    @PostMapping("/transport-expense-offset-detail/create")
    @Operation(summary = "创建冲抵暂估明细")
    @PreAuthorize("@ss.hasPermission('sms:transport-expense:create')")
    public CommonResult<Long> createTransportExpenseOffsetDetail(@Valid @RequestBody TransportExpenseOffsetDetailDO transportExpenseOffsetDetail) {
        return success(transportExpenseService.createTransportExpenseOffsetDetail(transportExpenseOffsetDetail));
    }

    @PutMapping("/transport-expense-offset-detail/update")
    @Operation(summary = "更新冲抵暂估明细")
    @PreAuthorize("@ss.hasPermission('sms:transport-expense:update')")
    public CommonResult<Boolean> updateTransportExpenseOffsetDetail(@Valid @RequestBody TransportExpenseOffsetDetailDO transportExpenseOffsetDetail) {
        transportExpenseService.updateTransportExpenseOffsetDetail(transportExpenseOffsetDetail);
        return success(true);
    }

    @DeleteMapping("/transport-expense-offset-detail/delete")
    @Parameter(name = "id", description = "编号", required = true)
    @Operation(summary = "删除冲抵暂估明细")
    @PreAuthorize("@ss.hasPermission('sms:transport-expense:delete')")
    public CommonResult<Boolean> deleteTransportExpenseOffsetDetail(@RequestParam("id") Long id) {
        transportExpenseService.deleteTransportExpenseOffsetDetail(id);
        return success(true);
    }

	@GetMapping("/transport-expense-offset-detail/get")
	@Operation(summary = "获得冲抵暂估明细")
	@Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sms:transport-expense:query')")
	public CommonResult<TransportExpenseOffsetDetailDO> getTransportExpenseOffsetDetail(@RequestParam("id") Long id) {
	    return success(transportExpenseService.getTransportExpenseOffsetDetail(id));
	}

    @PostMapping("/transport-expense-offset-detail/batchOperateOffsetDetail")
    @Operation(summary = "批量操作")
    @PreAuthorize("@ss.hasPermission('sms:transport-expense:create')")
    public CommonResult<Boolean> batchOperateOffsetDetail(@Valid @RequestBody OffsetDetailSaveBatchReqVO reqVO) {
        transportExpenseService.batchOperateOffsetDetail(reqVO);
        return success(true);
    }
}
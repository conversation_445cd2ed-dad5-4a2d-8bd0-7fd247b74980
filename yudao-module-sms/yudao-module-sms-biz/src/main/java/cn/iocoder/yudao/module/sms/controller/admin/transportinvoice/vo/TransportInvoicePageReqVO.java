package cn.iocoder.yudao.module.sms.controller.admin.transportinvoice.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 运杂费发票管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TransportInvoicePageReqVO extends PageParam {

    @Schema(description = "数据类别")
    private String appid;

    @Schema(description = "流程编号")
    private String processInstanceId;

    @Schema(description = "运杂费单号")
    private String invoiceNo;

    @Schema(description = "销售订单号")
    private String orderNo;

    @Schema(description = "运杂费类别", example = "1")
    private String expenseType;

    @Schema(description = "发票号码")
    private String invoiceNumber;

    @Schema(description = "发票日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] invoiceDate;

    @Schema(description = "承运商编码")
    private String carrierCode;

    @Schema(description = "发票/收据金额（未税）")
    private BigDecimal amountExcludingTax;

    @Schema(description = "营业额")
    private BigDecimal turnover;

    @Schema(description = "币别")
    private String currency;

    @Schema(description = "报支凭单")
    private String reimbursementVoucher;

    @Schema(description = "状态", example = "1")
    private String status;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "新增时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "创建者姓名")
    private String createEmpNo;

    @Schema(description = "更新者姓名")
    private String updateEmpNo;

    @Schema(description = "预留字段1 ")
    private String preText1;

    @Schema(description = "预留字段2 ")
    private String preText2;

    @Schema(description = "预留字段3 ")
    private String preText3;

    @Schema(description = "预留字段4 ")
    private String preText4;

    @Schema(description = "预留字段5 ")
    private String preText5;

    @Schema(description = "预留字段6 ")
    private String preText6;

    @Schema(description = "预留字段7 ")
    private String preText7;

    @Schema(description = "预留字段8 ")
    private String preText8;

    @Schema(description = "预留字段9 ")
    private String preText9;

    @Schema(description = "预留字段10 ")
    private String preText10;

    @Schema(description = "预留字段11 ")
    private BigDecimal preNum11;

    @Schema(description = "预留字段12 ")
    private BigDecimal preNum12;

    @Schema(description = "预留字段13 ")
    private BigDecimal preNum13;

    @Schema(description = "预留字段14 ")
    private BigDecimal preNum14;

    @Schema(description = "预留字段15 ")
    private BigDecimal preNum15;

    @Schema(description = "创建人编号")
    private String creator;

    @Schema(description = "修改人编号")
    private String updater;

    @Schema(description = "修改时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] updateTime;
}
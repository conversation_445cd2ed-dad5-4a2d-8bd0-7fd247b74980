package cn.iocoder.yudao.module.sms.controller.admin.transportexpense.vo;

import cn.iocoder.yudao.module.sms.dal.dataobject.transportexpense.TransportExpenseInvoiceDetailDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 运杂费报支管理新增/修改 Request VO")
@Data
public class TransportExpenseInvoiceDetailBatchSaveVO {

    private Long parentid;

    private List<TransportExpenseInvoiceDetailDO> items;

}
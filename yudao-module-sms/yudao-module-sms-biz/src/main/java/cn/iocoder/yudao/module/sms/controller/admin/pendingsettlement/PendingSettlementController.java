package cn.iocoder.yudao.module.sms.controller.admin.pendingsettlement;

import cn.iocoder.yudao.module.sms.controller.admin.common.SmsCommonFuncController;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;
import java.util.function.Supplier;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.sms.controller.admin.pendingsettlement.vo.*;
import cn.iocoder.yudao.module.sms.dal.dataobject.pendingsettlement.PendingSettlementDO;
import cn.iocoder.yudao.module.sms.service.pendingsettlement.PendingSettlementService;

@Tag(name = "管理后台 - 待结算资料档")
@RestController
@RequestMapping("/sms/pending-settlement")
@Validated
public class PendingSettlementController {

    @Resource
    private PendingSettlementService pendingSettlementService;
    @Resource
    private SmsCommonFuncController smsCommonFuncController;

    @PostMapping("/create")
    @Operation(summary = "创建待结算资料档")
    @PreAuthorize("@ss.hasPermission('sms:pending-settlement:create')")
    public CommonResult<Long> createPendingSettlement(@Valid @RequestBody PendingSettlementSaveReqVO createReqVO) {
        return success(pendingSettlementService.createPendingSettlement(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新待结算资料档")
    @PreAuthorize("@ss.hasPermission('sms:pending-settlement:update')")
    public CommonResult<Boolean> updatePendingSettlement(@Valid @RequestBody PendingSettlementSaveReqVO updateReqVO) {
        pendingSettlementService.updatePendingSettlement(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除待结算资料档")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sms:pending-settlement:delete')")
    public CommonResult<Boolean> deletePendingSettlement(@RequestParam("id") Long id) {
        pendingSettlementService.deletePendingSettlement(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得待结算资料档")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sms:pending-settlement:query')")
    public CommonResult<PendingSettlementRespVO> getPendingSettlement(@RequestParam("id") Long id) {
        PendingSettlementDO pendingSettlement = pendingSettlementService.getPendingSettlement(id);
        return success(BeanUtils.toBean(pendingSettlement, PendingSettlementRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得待结算资料档分页")
    @PreAuthorize("@ss.hasPermission('sms:pending-settlement:query')")
    public CommonResult<PageResult<PendingSettlementRespVO>> getPendingSettlementPage(@Valid PendingSettlementPageReqVO pageReqVO) {
        PageResult<PendingSettlementDO> pageResult = pendingSettlementService.getPendingSettlementPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PendingSettlementRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出待结算资料档 Excel")
    @PreAuthorize("@ss.hasPermission('sms:pending-settlement:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportPendingSettlementExcel(@Valid PendingSettlementPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<PendingSettlementDO> list = pendingSettlementService.getPendingSettlementPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "待结算资料档.xls", "数据", PendingSettlementRespVO.class,
                        BeanUtils.toBean(list, PendingSettlementRespVO.class));
    }

    @PostMapping("/toSettlement")
    @Operation(summary = "待结算转结算")
    public void toSettlement(@Valid @RequestBody ToSettlementReqVO toSettlementReqVO){
        Long id = toSettlementReqVO.getIds().get(0);
        PendingSettlementDO pendingSettlement = pendingSettlementService.getPendingSettlement(id);
        Supplier<String> func = ()->{
            pendingSettlementService.toSettlement(toSettlementReqVO.getIds());
            return null;
        };
        smsCommonFuncController.lockForSms(func,"CUST_NO_"+pendingSettlement.getCustNo());
    }

}
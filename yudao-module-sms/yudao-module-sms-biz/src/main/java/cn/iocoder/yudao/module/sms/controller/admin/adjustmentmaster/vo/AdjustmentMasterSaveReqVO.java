package cn.iocoder.yudao.module.sms.controller.admin.adjustmentmaster.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 销售调整原因主档新增/修改 Request VO")
@Data
public class AdjustmentMasterSaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "6121")
    private Long id;

    @Schema(description = "项目号")
    private String project;

    @Schema(description = "公司别", example = "15839")
    private String compId;

    @Schema(description = "原因代码")
    private String adjustReasonNo;

    @Schema(description = "原因说明", example = "不好")
    private String adjustReason;

    @Schema(description = "附件")
    private String annex;

    @Schema(description = "最后维护人员")
    private String lastEditBy;

    @Schema(description = "创建者")
    private String creator;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新者")
    private String updater;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "最后修改日期")
    private String lastEditDate;

    @Schema(description = "创建者姓名")
    private String createEmpNo;

    @Schema(description = "更新者姓名")
    private String updateEmpNo;

    @Schema(description = "最后维护人员姓名", example = "王五")
    private String lastEditByName;
}
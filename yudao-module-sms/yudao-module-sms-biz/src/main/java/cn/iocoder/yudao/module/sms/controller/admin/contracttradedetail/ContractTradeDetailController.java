package cn.iocoder.yudao.module.sms.controller.admin.contracttradedetail;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.sms.controller.admin.contracttradedetail.vo.*;
import cn.iocoder.yudao.module.sms.dal.dataobject.contracttradedetail.ContractTradeDetailDO;
import cn.iocoder.yudao.module.sms.service.contracttradedetail.ContractTradeDetailService;

@Tag(name = "管理后台 - 外贸订单项次档")
@RestController
@RequestMapping("/sms/contract-trade-detail")
@Validated
public class ContractTradeDetailController {

    @Resource
    private ContractTradeDetailService contractTradeDetailService;

    @PostMapping("/create")
    @Operation(summary = "创建外贸订单项次档")
    @PreAuthorize("@ss.hasPermission('sms:contract-trade-detail:create')")
    public CommonResult<Long> createContractTradeDetail(@Valid @RequestBody ContractTradeDetailSaveReqVO createReqVO) {
        return success(contractTradeDetailService.createContractTradeDetail(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新外贸订单项次档")
    @PreAuthorize("@ss.hasPermission('sms:contract-trade-detail:update')")
    public CommonResult<Boolean> updateContractTradeDetail(@Valid @RequestBody ContractTradeDetailSaveReqVO updateReqVO) {
        contractTradeDetailService.updateContractTradeDetail(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除外贸订单项次档")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sms:contract-trade-detail:delete')")
    public CommonResult<Boolean> deleteContractTradeDetail(@RequestParam("id") Long id) {
        contractTradeDetailService.deleteContractTradeDetail(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得外贸订单项次档")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sms:contract-trade-detail:query')")
    public CommonResult<ContractTradeDetailRespVO> getContractTradeDetail(@RequestParam("id") Long id) {
        ContractTradeDetailDO contractTradeDetail = contractTradeDetailService.getContractTradeDetail(id);
        return success(BeanUtils.toBean(contractTradeDetail, ContractTradeDetailRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得外贸订单项次档分页")
    @PreAuthorize("@ss.hasPermission('sms:contract-trade-detail:query')")
    public CommonResult<PageResult<ContractTradeDetailRespVO>> getContractTradeDetailPage(@Valid ContractTradeDetailPageReqVO pageReqVO) {
        PageResult<ContractTradeDetailDO> pageResult = contractTradeDetailService.getContractTradeDetailPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ContractTradeDetailRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出外贸订单项次档 Excel")
    @PreAuthorize("@ss.hasPermission('sms:contract-trade-detail:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportContractTradeDetailExcel(@Valid ContractTradeDetailPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ContractTradeDetailDO> list = contractTradeDetailService.getContractTradeDetailPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "外贸订单项次档.xls", "数据", ContractTradeDetailRespVO.class,
                        BeanUtils.toBean(list, ContractTradeDetailRespVO.class));
    }

}

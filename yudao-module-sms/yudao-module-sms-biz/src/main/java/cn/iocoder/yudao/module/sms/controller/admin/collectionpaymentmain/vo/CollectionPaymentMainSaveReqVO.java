package cn.iocoder.yudao.module.sms.controller.admin.collectionpaymentmain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import cn.iocoder.yudao.module.sms.dal.dataobject.collectionpaymentmain.CollectionPaymentDetailDO;

@Schema(description = "管理后台 - 代收代付主档新增/修改 Request VO")
@Data
public class CollectionPaymentMainSaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "6822")
    private Long id;

    @Schema(description = "数据类别")
    private String appid;

    @Schema(description = "流程编号")
    private String processInstanceId;

    @Schema(description = "代收代付单号")
    private String collectionPaymentNo;

    @Schema(description = "报支单号")
    private String reimbursementNo;

    @Schema(description = "承运商")
    private String carrierCode;

    @Schema(description = "申请状态", example = "1")
    private String applicationStatus;

    @Schema(description = "预计付款日期")
    private String estimatedPaymentDate;

    @Schema(description = "创建部门")
    private String createDept;

    @Schema(description = "创建部门名称", example = "赵六")
    private String createDeptName;

    @Schema(description = "预留字段1 ")
    private String preText1;

    @Schema(description = "预留字段2 ")
    private String preText2;

    @Schema(description = "预留字段3 ")
    private String preText3;

    @Schema(description = "预留字段4 ")
    private String preText4;

    @Schema(description = "预留字段5 ")
    private String preText5;

    @Schema(description = "预留字段6 ")
    private String preText6;

    @Schema(description = "预留字段7 ")
    private String preText7;

    @Schema(description = "预留字段8 ")
    private String preText8;

    @Schema(description = "预留字段9 ")
    private String preText9;

    @Schema(description = "预留字段10 ")
    private String preText10;

    @Schema(description = "预留字段11 ")
    private BigDecimal preNum11;

    @Schema(description = "预留字段12 ")
    private BigDecimal preNum12;

    @Schema(description = "预留字段13 ")
    private BigDecimal preNum13;

    @Schema(description = "预留字段14 ")
    private BigDecimal preNum14;

    @Schema(description = "预留字段15 ")
    private BigDecimal preNum15;

    @Schema(description = "创建者")
    private String creator;

    @Schema(description = "更新者")
    private String updater;

    @Schema(description = "新增时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建者姓名")
    private String createEmpNo;

    @Schema(description = "更新者姓名")
    private String updateEmpNo;

}
package cn.iocoder.yudao.module.sms.controller.admin.ttfinalinvoice;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.sms.controller.admin.ttfinalinvoice.vo.*;
import cn.iocoder.yudao.module.sms.dal.dataobject.ttfinalinvoice.TtFinalInvoiceDO;
import cn.iocoder.yudao.module.sms.dal.dataobject.ttfinalinvoice.TtFinalInvoiceDetailDO;
import cn.iocoder.yudao.module.sms.service.ttfinalinvoice.TtFinalInvoiceService;

@Tag(name = "管理后台 - TT尾款发票")
@RestController
@RequestMapping("/sms/tt-final-invoice")
@Validated
public class TtFinalInvoiceController {

    @Resource
    private TtFinalInvoiceService ttFinalInvoiceService;

    @PostMapping("/create")
    @Operation(summary = "创建TT尾款发票")
    @PreAuthorize("@ss.hasPermission('sms:tt-final-invoice:create')")
    public CommonResult<Long> createTtFinalInvoice(@Valid @RequestBody TtFinalInvoiceSaveReqVO createReqVO) {
        return success(ttFinalInvoiceService.createTtFinalInvoice(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新TT尾款发票")
    @PreAuthorize("@ss.hasPermission('sms:tt-final-invoice:update')")
    public CommonResult<Boolean> updateTtFinalInvoice(@Valid @RequestBody TtFinalInvoiceSaveReqVO updateReqVO) {
        ttFinalInvoiceService.updateTtFinalInvoice(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除TT尾款发票")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sms:tt-final-invoice:delete')")
    public CommonResult<Boolean> deleteTtFinalInvoice(@RequestParam("id") Long id) {
        ttFinalInvoiceService.deleteTtFinalInvoice(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得TT尾款发票")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sms:tt-final-invoice:query')")
    public CommonResult<TtFinalInvoiceRespVO> getTtFinalInvoice(@RequestParam("id") Long id) {
        TtFinalInvoiceDO ttFinalInvoice = ttFinalInvoiceService.getTtFinalInvoice(id);
        return success(BeanUtils.toBean(ttFinalInvoice, TtFinalInvoiceRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得TT尾款发票分页")
    @PreAuthorize("@ss.hasPermission('sms:tt-final-invoice:query')")
    public CommonResult<PageResult<TtFinalInvoiceRespVO>> getTtFinalInvoicePage(@Valid TtFinalInvoicePageReqVO pageReqVO) {
        PageResult<TtFinalInvoiceDO> pageResult = ttFinalInvoiceService.getTtFinalInvoicePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TtFinalInvoiceRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出TT尾款发票 Excel")
    @PreAuthorize("@ss.hasPermission('sms:tt-final-invoice:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTtFinalInvoiceExcel(@Valid TtFinalInvoicePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TtFinalInvoiceDO> list = ttFinalInvoiceService.getTtFinalInvoicePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "TT尾款发票.xls", "数据", TtFinalInvoiceRespVO.class,
                        BeanUtils.toBean(list, TtFinalInvoiceRespVO.class));
    }

    // ==================== 子表（TT尾款发票明细） ====================

    @GetMapping("/tt-final-invoice-detail/list-by-parentid")
    @Operation(summary = "获得TT尾款发票明细列表")
    @Parameter(name = "parentid", description = "PARENTID")
    @PreAuthorize("@ss.hasPermission('sms:tt-final-invoice:query')")
    public CommonResult<List<TtFinalInvoiceDetailDO>> getTtFinalInvoiceDetailListByParentid(@RequestParam("parentid") Long parentid) {
        return success(ttFinalInvoiceService.getTtFinalInvoiceDetailListByParentid(parentid));
    }

}
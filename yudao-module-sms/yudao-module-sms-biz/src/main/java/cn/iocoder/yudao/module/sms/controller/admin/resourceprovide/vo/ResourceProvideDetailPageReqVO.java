package cn.iocoder.yudao.module.sms.controller.admin.resourceprovide.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 产品资源明细发放分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ResourceProvideDetailPageReqVO extends PageParam {

    @Schema(description = "资源发放 编号")
    private String resoureProvideId;

    @Schema(description = "来源")
    private String source;

    @Schema(description = "大类")
    private String prodClass;

    @Schema(description = "产品编号")
    private String prodNo;

    @Schema(description = "审批状态")
    private String state;

    @Schema(description = "当前时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime resourceTime;

}

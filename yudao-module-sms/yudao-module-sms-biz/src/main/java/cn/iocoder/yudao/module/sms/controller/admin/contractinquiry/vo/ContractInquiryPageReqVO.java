package cn.iocoder.yudao.module.sms.controller.admin.contractinquiry.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 询单管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ContractInquiryPageReqVO extends PageParam {

    @Schema(description = "询单编号")
    private String inquiryOrderNo;

    @Schema(description = "合同编号")
    private String contractNo;

    @Schema(description = "客户名称", example = "王五")
    private String custName;

    @Schema(description = "来源")
    private String source;

    @Schema(description = "大类")
    private String prodClass;

    @Schema(description = "形态", example = "2")
    private String prodType;

    @Schema(description = "名称", example = "李四")
    private String prodName;

    @Schema(description = "计划采购量")
    private String scheduleNum;

    @Schema(description = "资源总量")
    private String resourceNum;

    @Schema(description = "出口折算基价FOB", example = "27460")
    private String fobPrice;

    @Schema(description = "备案底价", example = "4104")
    private String recordPrice;

    @Schema(description = "基价价格条件")
    private String priceCondition;

    @Schema(description = "商品材质")
    private String prodTexture;

    @Schema(description = "下至何月资源")
    private String resourceMonth;

    @Schema(description = "是否投保")
    private String insure;

    @Schema(description = "目的国")
    private String desCountry;

    @Schema(description = "目的港")
    private String desPort;

    @Schema(description = "国内时点价格", example = "25653")
    private String homeTimelyPrice;

    @Schema(description = "合同价", example = "11931")
    private String contractPrice;

    @Schema(description = "合同价价格条款")
    private String contractClause;

    @Schema(description = "佣金")
    private String commission;

    @Schema(description = "海运费")
    private String oceanFreight;

    @Schema(description = "可运输量")
    private String transNum;

    @Schema(description = "部门")
    private String dept;

    @Schema(description = "操作人")
    private String operater;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "合同简述")
    private String contractDescribe;

    @Schema(description = "附件")
    private String annex;

    @Schema(description = "汇率")
    private String rate;

    @Schema(description = "出厂含税")
    private String exportPriceTax;

    @Schema(description = "出厂不含税", example = "30279")
    private String exportPrice;

    @Schema(description = "价差不含税", example = "7779")
    private String differPrice;

    @Schema(description = "毛利2")
    private String grossProfit2;

    @Schema(description = "毛利4")
    private String grossProfit4;

    @Schema(description = "新增时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "状态")
    private String state;

    @Schema(description = "已挑选")
    private String selected;
}

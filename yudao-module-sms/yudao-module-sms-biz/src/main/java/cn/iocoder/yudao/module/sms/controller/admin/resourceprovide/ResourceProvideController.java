package cn.iocoder.yudao.module.sms.controller.admin.resourceprovide;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.sms.controller.admin.resourceprovide.vo.*;
import cn.iocoder.yudao.module.sms.dal.dataobject.resourceprovide.ResourceProvideDO;
import cn.iocoder.yudao.module.sms.dal.dataobject.resourceprovide.ResourceProvideDetailDO;
import cn.iocoder.yudao.module.sms.service.resourceprovide.ResourceProvideService;

@Tag(name = "管理后台 - 产品资源发放")
@RestController
@RequestMapping("/sms/resource-provide")
@Validated
public class ResourceProvideController {

    @Resource
    private ResourceProvideService resourceProvideService;

    @PostMapping("/create")
    @Operation(summary = "创建产品资源发放")
    @PreAuthorize("@ss.hasPermission('sms:resource-provide:create')")
    public CommonResult<Long> createResourceProvide(@Valid @RequestBody ResourceProvideSaveReqVO createReqVO) {
        return success(resourceProvideService.createResourceProvide(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新产品资源发放")
    @PreAuthorize("@ss.hasPermission('sms:resource-provide:update')")
    public CommonResult<Boolean> updateResourceProvide(@Valid @RequestBody ResourceProvideSaveReqVO updateReqVO) {
        resourceProvideService.updateResourceProvide(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除产品资源发放")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sms:resource-provide:delete')")
    public CommonResult<Boolean> deleteResourceProvide(@RequestParam("id") Long id) {
        resourceProvideService.deleteResourceProvide(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得产品资源发放")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sms:resource-provide:query')")
    public CommonResult<ResourceProvideRespVO> getResourceProvide(@RequestParam("id") Long id) {
        ResourceProvideDO resourceProvide = resourceProvideService.getResourceProvide(id);
        return success(BeanUtils.toBean(resourceProvide, ResourceProvideRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得产品资源发放分页")
    @PreAuthorize("@ss.hasPermission('sms:resource-provide:query')")
    public CommonResult<PageResult<ResourceProvideRespVO>> getResourceProvidePage(@Valid ResourceProvidePageReqVO pageReqVO) {
        PageResult<ResourceProvideDO> pageResult = resourceProvideService.getResourceProvidePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ResourceProvideRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出产品资源发放 Excel")
    @PreAuthorize("@ss.hasPermission('sms:resource-provide:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportResourceProvideExcel(@Valid ResourceProvidePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ResourceProvideDO> list = resourceProvideService.getResourceProvidePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "产品资源发放.xls", "数据", ResourceProvideRespVO.class,
                        BeanUtils.toBean(list, ResourceProvideRespVO.class));
    }

    @GetMapping("/effect")
    @Operation(summary = "生效")
    @Parameter(name = "id", description = "编号", required = true)
    public void effectiveResourceProvide(@RequestParam("id") Long id) {
        resourceProvideService.effectiveResourceProvide(id);
    }

    @GetMapping("/invalid")
    @Operation(summary = "取消生效")
    @Parameter(name = "id", description = "编号", required = true)
    public void invalidResourceProvide(@RequestParam("id") Long id) {
        resourceProvideService.invalidResourceProvide(id);
    }

    // ==================== 子表（产品资源发放明细） ====================

    @GetMapping("/resource-provide-detail/page")
    @Operation(summary = "获得产品资源发放明细分页")
    @Parameter(name = "resoureProvideId", description = "资源发放 编号")
    @PreAuthorize("@ss.hasPermission('sms:resource-provide:query')")
    public CommonResult<PageResult<ResourceProvideDetailDO>> getResourceProvideDetailPage(PageParam pageReqVO,
                                                                                        @RequestParam("resoureProvideId") String resoureProvideId) {
        return success(resourceProvideService.getResourceProvideDetailPage(pageReqVO, resoureProvideId));
    }

    @GetMapping("/resource-provide-detail/page2")
    @Operation(summary = "获得产品资源发放明细分页")
    @Parameter(name = "resoureProvideId", description = "资源发放 编号")
    @PreAuthorize("@ss.hasPermission('sms:resource-provide:query')")
    public CommonResult<PageResult<ResourceProvideDetailDO>> getResourceProvideDetailPage2(@Valid ResourceProvideDetailPageReqVO pageReqVO) {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        return success(resourceProvideService.getResourceProvideDetailPage2(pageReqVO));
    }

    @PostMapping("/resource-provide-detail/create")
    @Operation(summary = "创建产品资源发放明细")
    @PreAuthorize("@ss.hasPermission('sms:resource-provide:create')")
    public CommonResult<Long> createResourceProvideDetail(@Valid @RequestBody ResourceProvideDetailDO resourceProvideDetail) {
        return success(resourceProvideService.createResourceProvideDetail(resourceProvideDetail));
    }

    @PutMapping("/resource-provide-detail/update")
    @Operation(summary = "更新产品资源发放明细")
    @PreAuthorize("@ss.hasPermission('sms:resource-provide:update')")
    public CommonResult<Boolean> updateResourceProvideDetail(@Valid @RequestBody ResourceProvideDetailDO resourceProvideDetail) {
        resourceProvideService.updateResourceProvideDetail(resourceProvideDetail);
        return success(true);
    }

    @DeleteMapping("/resource-provide-detail/delete")
    @Parameter(name = "id", description = "编号", required = true)
    @Operation(summary = "删除产品资源发放明细")
    @PreAuthorize("@ss.hasPermission('sms:resource-provide:delete')")
    public CommonResult<Boolean> deleteResourceProvideDetail(@RequestParam("id") Long id) {
        resourceProvideService.deleteResourceProvideDetail(id);
        return success(true);
    }

	@GetMapping("/resource-provide-detail/get")
	@Operation(summary = "获得产品资源发放明细")
	@Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sms:resource-provide:query')")
	public CommonResult<ResourceProvideDetailDO> getResourceProvideDetail(@RequestParam("id") Long id) {
	    return success(resourceProvideService.getResourceProvideDetail(id));
	}

    @GetMapping("/resource-provide-detail/getLast")
    @Operation(summary = "获得当前资源池明细的最后一笔发放资源")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<ResourceProvideDetailDO> getLastWeekProvviceDetail(@RequestParam("id") Long id) {
        return success(resourceProvideService.getResourceProvideDetail2(id));
    }

}

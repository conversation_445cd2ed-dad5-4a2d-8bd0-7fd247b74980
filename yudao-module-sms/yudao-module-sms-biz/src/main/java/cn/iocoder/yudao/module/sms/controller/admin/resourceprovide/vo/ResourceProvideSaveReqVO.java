package cn.iocoder.yudao.module.sms.controller.admin.resourceprovide.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import cn.iocoder.yudao.module.sms.dal.dataobject.resourceprovide.ResourceProvideDetailDO;

@Schema(description = "管理后台 - 产品资源发放新增/修改 Request VO")
@Data
public class ResourceProvideSaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "资源发放 编号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String resoureProvideId;

    @Schema(description = "批次号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "批次号不能为空")
    private String batchNo;

    @Schema(description = "部门", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "部门不能为空")
    private String department;

    @Schema(description = "操作人", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "操作人不能为空")
    private String operater;

    @Schema(description = "生效时间开始时间")
    private LocalDateTime effectStartTime;

    @Schema(description = "生效结束时间")
    private LocalDateTime effectEndTime;

    @Schema(description = "备注")
    private String remark;

}

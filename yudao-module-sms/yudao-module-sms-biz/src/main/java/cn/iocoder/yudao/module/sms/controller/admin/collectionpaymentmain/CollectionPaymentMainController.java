package cn.iocoder.yudao.module.sms.controller.admin.collectionpaymentmain;

import cn.iocoder.yudao.module.sms.controller.admin.transportexpense.vo.OffsetDetailSaveBatchReqVO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.sms.controller.admin.collectionpaymentmain.vo.*;
import cn.iocoder.yudao.module.sms.dal.dataobject.collectionpaymentmain.CollectionPaymentMainDO;
import cn.iocoder.yudao.module.sms.dal.dataobject.collectionpaymentmain.CollectionPaymentDetailDO;
import cn.iocoder.yudao.module.sms.service.collectionpaymentmain.CollectionPaymentMainService;

@Tag(name = "管理后台 - 代收代付主档")
@RestController
@RequestMapping("/sms/collection-payment-main")
@Validated
public class CollectionPaymentMainController {

    @Resource
    private CollectionPaymentMainService collectionPaymentMainService;

    @PostMapping("/create")
    @Operation(summary = "创建代收代付主档")
    @PreAuthorize("@ss.hasPermission('sms:collection-payment-main:create')")
    public CommonResult<Long> createCollectionPaymentMain(@Valid @RequestBody CollectionPaymentMainSaveReqVO createReqVO) {
        return success(collectionPaymentMainService.createCollectionPaymentMain(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新代收代付主档")
    @PreAuthorize("@ss.hasPermission('sms:collection-payment-main:update')")
    public CommonResult<Boolean> updateCollectionPaymentMain(@Valid @RequestBody CollectionPaymentMainSaveReqVO updateReqVO) {
        collectionPaymentMainService.updateCollectionPaymentMain(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除代收代付主档")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sms:collection-payment-main:delete')")
    public CommonResult<Boolean> deleteCollectionPaymentMain(@RequestParam("id") Long id) {
        collectionPaymentMainService.deleteCollectionPaymentMain(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得代收代付主档")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sms:collection-payment-main:query')")
    public CommonResult<CollectionPaymentMainRespVO> getCollectionPaymentMain(@RequestParam("id") Long id) {
        CollectionPaymentMainDO collectionPaymentMain = collectionPaymentMainService.getCollectionPaymentMain(id);
        return success(BeanUtils.toBean(collectionPaymentMain, CollectionPaymentMainRespVO.class));
    }

    @GetMapping("/auditCollectionPayment")
    @Operation(summary = "发起审批")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sms:collection-payment-main:query')")
    public CommonResult<Boolean> auditCollectionPayment(@RequestParam("id") Long id) {
        collectionPaymentMainService.auditCollectionPayment(id);
        return success(true);
    }

    @GetMapping("/collectionPaymentPushNC")
    @Operation(summary = "发起审批")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sms:collection-payment-main:query')")
    public CommonResult<Boolean> collectionPaymentPushNC(@RequestParam("id") Long id) {
        collectionPaymentMainService.collectionPaymentPushNC(id);
        return success(true);
    }
    @GetMapping("/cancelAuditCollectionPayment")
    @Operation(summary = "取消审批")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sms:collection-payment-main:query')")
    public CommonResult<Boolean> cancelAuditCollectionPayment(@RequestParam("id") Long id) {
        collectionPaymentMainService.cancelAuditCollectionPayment(id);
        return success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "获得代收代付主档分页")
    @PreAuthorize("@ss.hasPermission('sms:collection-payment-main:query')")
    public CommonResult<PageResult<CollectionPaymentMainRespVO>> getCollectionPaymentMainPage(@Valid CollectionPaymentMainPageReqVO pageReqVO) {
        PageResult<CollectionPaymentMainDO> pageResult = collectionPaymentMainService.getCollectionPaymentMainPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CollectionPaymentMainRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出代收代付主档 Excel")
    @PreAuthorize("@ss.hasPermission('sms:collection-payment-main:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCollectionPaymentMainExcel(@Valid CollectionPaymentMainPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<CollectionPaymentMainDO> list = collectionPaymentMainService.getCollectionPaymentMainPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "代收代付主档.xls", "数据", CollectionPaymentMainRespVO.class,
                        BeanUtils.toBean(list, CollectionPaymentMainRespVO.class));
    }

    // ==================== 子表（代收代付明细档） ====================

    @GetMapping("/collection-payment-detail/page")
    @Operation(summary = "获得代收代付明细档分页")
    @Parameter(name = "parentid", description = "PARENTID")
    @PreAuthorize("@ss.hasPermission('sms:collection-payment-main:query')")
    public CommonResult<PageResult<CollectionPaymentDetailDO>> getCollectionPaymentDetailPage(PageParam pageReqVO,
                                                                                        @RequestParam("parentid") Long parentid) {
        return success(collectionPaymentMainService.getCollectionPaymentDetailPage(pageReqVO, parentid));
    }

    @PostMapping("/collection-payment-detail/create")
    @Operation(summary = "创建代收代付明细档")
    @PreAuthorize("@ss.hasPermission('sms:collection-payment-main:create')")
    public CommonResult<Long> createCollectionPaymentDetail(@Valid @RequestBody CollectionPaymentDetailDO collectionPaymentDetail) {
        return success(collectionPaymentMainService.createCollectionPaymentDetail(collectionPaymentDetail));
    }

    @PutMapping("/collection-payment-detail/update")
    @Operation(summary = "更新代收代付明细档")
    @PreAuthorize("@ss.hasPermission('sms:collection-payment-main:update')")
    public CommonResult<Boolean> updateCollectionPaymentDetail(@Valid @RequestBody CollectionPaymentDetailDO collectionPaymentDetail) {
        collectionPaymentMainService.updateCollectionPaymentDetail(collectionPaymentDetail);
        return success(true);
    }

    @DeleteMapping("/collection-payment-detail/delete")
    @Parameter(name = "id", description = "编号", required = true)
    @Operation(summary = "删除代收代付明细档")
    @PreAuthorize("@ss.hasPermission('sms:collection-payment-main:delete')")
    public CommonResult<Boolean> deleteCollectionPaymentDetail(@RequestParam("id") Long id) {
        collectionPaymentMainService.deleteCollectionPaymentDetail(id);
        return success(true);
    }

	@GetMapping("/collection-payment-detail/get")
	@Operation(summary = "获得代收代付明细档")
	@Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sms:collection-payment-main:query')")
	public CommonResult<CollectionPaymentDetailDO> getCollectionPaymentDetail(@RequestParam("id") Long id) {
	    return success(collectionPaymentMainService.getCollectionPaymentDetail(id));
	}

    @PostMapping("/collection-payment-detail/batchOperateDetail")
    @Operation(summary = "批量操作")
    @PreAuthorize("@ss.hasPermission('sms:collection-payment-main:create')")
    public CommonResult<Boolean> batchOperateDetail(@Valid @RequestBody CollectionPaymentDetailSaveBatchReqVO reqVO) {
        collectionPaymentMainService.batchOperateDetail(reqVO);
        return success(true);
    }
}
package cn.iocoder.yudao.module.sms.controller.admin.saleadjust.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 销售调整资料档分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SaleAdjustPageReqVO extends PageParam {

    @Schema(description = "项目号")
    private String project;

    @Schema(description = "公司别")
    private String compId;

    @Schema(description = "销售调整单号")
    private String saleadjno;

    @Schema(description = "资料类别")
    private String settType;

    @Schema(description = "来源单号")
    private String fromNo;

    @Schema(description = "客户编号")
    private String custNo;

    @Schema(description = "订单编号")
    private String orderNo;

    @Schema(description = "结算单号")
    private String revenueNo;

    @Schema(description = "调整原因")
    private String adjustReason;

    @Schema(description = "调整金额")
    private BigDecimal adjustAmt;

    @Schema(description = "增值税率")
    private BigDecimal taxrate;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "附件")
    private String annex;

    @Schema(description = "建立人员")
    private String createBy;

    @Schema(description = "建立日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] createDate;

    @Schema(description = "修改人员")
    private String updateBy;

    @Schema(description = "修改日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] updateDate;

    @Schema(description = "生效人员")
    private String effectBy;

    @Schema(description = "生效日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] effectDate;

    @Schema(description = "创建者")
    private String creator;

    @Schema(description = "更新者")
    private String updater;

    @Schema(description = "新增时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "修改时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] updateTime;

    @Schema(description = "订单项次号")
    private String orderItemNo;

    @Schema(description = "创建者姓名")
    private String createEmpNo;

    @Schema(description = "更新者姓名")
    private String updateEmpNo;

    @Schema(description = "生效人员姓名", example = "赵六")
    private String effectByName;

    @Schema(description = "贸易标志")
    private String tradeFlag;

    private String appid;
}
package cn.iocoder.yudao.module.sms.controller.admin.orderiteminvoice;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.sms.controller.admin.orderiteminvoice.vo.*;
import cn.iocoder.yudao.module.sms.dal.dataobject.orderiteminvoice.OrderItemInvoiceDO;
import cn.iocoder.yudao.module.sms.service.orderiteminvoice.OrderItemInvoiceService;

@Tag(name = "管理后台 - 提货单备份_销售订单项次档")
@RestController
@RequestMapping("/sms/order-item-invoice")
@Validated
public class OrderItemInvoiceController {

    @Resource
    private OrderItemInvoiceService orderItemInvoiceService;

    @PostMapping("/create")
    @Operation(summary = "创建提货单备份_销售订单项次档")
    @PreAuthorize("@ss.hasPermission('sms:order-item-invoice:create')")
    public CommonResult<Long> createOrderItemInvoice(@Valid @RequestBody OrderItemInvoiceSaveReqVO createReqVO) {
        return success(orderItemInvoiceService.createOrderItemInvoice(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新提货单备份_销售订单项次档")
    @PreAuthorize("@ss.hasPermission('sms:order-item-invoice:update')")
    public CommonResult<Boolean> updateOrderItemInvoice(@Valid @RequestBody OrderItemInvoiceSaveReqVO updateReqVO) {
        orderItemInvoiceService.updateOrderItemInvoice(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除提货单备份_销售订单项次档")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sms:order-item-invoice:delete')")
    public CommonResult<Boolean> deleteOrderItemInvoice(@RequestParam("id") Long id) {
        orderItemInvoiceService.deleteOrderItemInvoice(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得提货单备份_销售订单项次档")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sms:order-item-invoice:query')")
    public CommonResult<OrderItemInvoiceRespVO> getOrderItemInvoice(@RequestParam("id") Long id) {
        OrderItemInvoiceDO orderItemInvoice = orderItemInvoiceService.getOrderItemInvoice(id);
        return success(BeanUtils.toBean(orderItemInvoice, OrderItemInvoiceRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得提货单备份_销售订单项次档分页")
    @PreAuthorize("@ss.hasPermission('sms:order-item-invoice:query')")
    public CommonResult<PageResult<OrderItemInvoiceRespVO>> getOrderItemInvoicePage(@Valid OrderItemInvoicePageReqVO pageReqVO) {
        PageResult<OrderItemInvoiceDO> pageResult = orderItemInvoiceService.getOrderItemInvoicePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, OrderItemInvoiceRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出提货单备份_销售订单项次档 Excel")
    @PreAuthorize("@ss.hasPermission('sms:order-item-invoice:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportOrderItemInvoiceExcel(@Valid OrderItemInvoicePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<OrderItemInvoiceDO> list = orderItemInvoiceService.getOrderItemInvoicePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "提货单备份_销售订单项次档.xls", "数据", OrderItemInvoiceRespVO.class,
                        BeanUtils.toBean(list, OrderItemInvoiceRespVO.class));
    }

}
package cn.iocoder.yudao.module.sms.controller.admin.shippingdetail;

import cn.iocoder.yudao.module.sms.controller.admin.transportexpense.vo.TransportExpenseInvoiceDetailBatchSaveVO;
import cn.iocoder.yudao.module.sms.dal.dataobject.contracttrade.ContractTradeDO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.sms.controller.admin.shippingdetail.vo.*;
import cn.iocoder.yudao.module.sms.dal.dataobject.shippingdetail.ShippingDetailDO;
import cn.iocoder.yudao.module.sms.dal.dataobject.shippingdetail.ShippingDetailItemsDO;
import cn.iocoder.yudao.module.sms.service.shippingdetail.ShippingDetailService;

@Tag(name = "管理后台 - 配船明细")
@RestController
@RequestMapping("/sms/shipping-detail")
@Validated
public class ShippingDetailController {

    @Resource
    private ShippingDetailService shippingDetailService;

    @PostMapping("/create")
    @Operation(summary = "创建配船明细")
    @PreAuthorize("@ss.hasPermission('sms:shipping-detail:create')")
    public CommonResult<Long> createShippingDetail(@Valid @RequestBody ShippingDetailSaveReqVO createReqVO) {
        return success(shippingDetailService.createShippingDetail(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新配船明细")
    @PreAuthorize("@ss.hasPermission('sms:shipping-detail:update')")
    public CommonResult<Boolean> updateShippingDetail(@Valid @RequestBody ShippingDetailSaveReqVO updateReqVO) {
        shippingDetailService.updateShippingDetail(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除配船明细")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sms:shipping-detail:delete')")
    public CommonResult<Boolean> deleteShippingDetail(@RequestParam("id") Long id) {
        shippingDetailService.deleteShippingDetail(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得配船明细")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sms:shipping-detail:query')")
    public CommonResult<ShippingDetailRespVO> getShippingDetail(@RequestParam("id") Long id) {
        ShippingDetailDO shippingDetail = shippingDetailService.getShippingDetail(id);
        return success(BeanUtils.toBean(shippingDetail, ShippingDetailRespVO.class));
    }

    @GetMapping("/handleAudit")
    @Operation(summary = "发起配船审批")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sms:shipping-detail:query')")
    public CommonResult<Boolean> handleAudit(@RequestParam("id") Long id) {
        shippingDetailService.handleAudit(id);
        return success(true);
    }

    @GetMapping("/cancelAudit")
    @Operation(summary = "取消审批")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sms:shipping-detail:query')")
    public CommonResult<Boolean> cancelAudit(@RequestParam("id") Long id) {
        shippingDetailService.cancelAudit(id);
        return success(true);
    }

    @GetMapping("/customsDeclaration")
    @Operation(summary = "报关")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sms:shipping-detail:query')")
    public CommonResult<Boolean> customsDeclaration(@RequestParam("id") Long id) {
        shippingDetailService.customsDeclaration(id);
        return success(true);
    }

    @GetMapping("/writeoff")
    @Operation(summary = "销账")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sms:shipping-detail:query')")
    public CommonResult<Boolean> writeoff(@RequestParam("id") Long id) {
        shippingDetailService.writeoff(id);
        return success(true);
    }

    @GetMapping("/antiwriteoff")
    @Operation(summary = "反销账")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sms:shipping-detail:query')")
    public CommonResult<Boolean> antiwriteoff(@RequestParam("id") Long id) {
        shippingDetailService.antiwriteoff(id);
        return success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "获得配船明细分页")
    @PreAuthorize("@ss.hasPermission('sms:shipping-detail:query')")
    public CommonResult<PageResult<ShippingDetailRespVO>> getShippingDetailPage(@Valid ShippingDetailPageReqVO pageReqVO) {
        PageResult<ShippingDetailDO> pageResult = shippingDetailService.getShippingDetailPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ShippingDetailRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出配船明细 Excel")
    @PreAuthorize("@ss.hasPermission('sms:shipping-detail:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportShippingDetailExcel(@Valid ShippingDetailPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ShippingDetailDO> list = shippingDetailService.getShippingDetailPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "配船明细.xls", "数据", ShippingDetailRespVO.class,
                        BeanUtils.toBean(list, ShippingDetailRespVO.class));
    }

    @GetMapping("/tradeSelectPage")
    @Operation(summary = "挑选外贸合同查询分页")
    @PreAuthorize("@ss.hasPermission('sms:shipping-detail:query')")
    public CommonResult<PageResult<ContractTradeDO>> tradeSelectPage(@Valid ShippingTradeSelectPageReqVO pageReqVO) {
        PageResult<ContractTradeDO> pageResult = shippingDetailService.tradeSelectPage(pageReqVO);
        return success(pageResult);
    }

    // ==================== 子表（配船明细表明细项次档） ====================

    @GetMapping("/shipping-detail-items/selectItemByTrade")
    @Operation(summary = "挑选外贸合同明细分页")
    @PreAuthorize("@ss.hasPermission('sms:shipping-detail:query')")
    public CommonResult<PageResult<ShippingDetailItemsDO>> selectItemByTrade(@Valid ShippingItemTradeSelectPageReqVO pageReqVO) {
        PageResult<ShippingDetailItemsDO> pageResult = shippingDetailService.selectItemByTrade(pageReqVO);
        return success(pageResult);
    }

    @PostMapping("/shipping-detail-items/batchCreateShippingItems")
    @Operation(summary = "批量创建配船明细")
    @PreAuthorize("@ss.hasPermission('sms:transport-expense:create')")
    public CommonResult<Boolean> batchCreateShippingItems(@Valid @RequestBody ShippingItemBatchSaveVO createReqVO) {
        shippingDetailService.batchCreateShippingItems(createReqVO);
        return success(true);
    }

    @GetMapping("/shipping-detail-items/page")
    @Operation(summary = "获得配船明细表明细项次档分页")
    @Parameter(name = "parentid", description = "PARENTID")
    @PreAuthorize("@ss.hasPermission('sms:shipping-detail:query')")
    public CommonResult<PageResult<ShippingDetailItemsDO>> getShippingDetailItemsPage(PageParam pageReqVO,
                                                                                        @RequestParam("parentid") Long parentid) {
        return success(shippingDetailService.getShippingDetailItemsPage(pageReqVO, parentid));
    }

    @PostMapping("/shipping-detail-items/create")
    @Operation(summary = "创建配船明细表明细项次档")
    @PreAuthorize("@ss.hasPermission('sms:shipping-detail:create')")
    public CommonResult<Long> createShippingDetailItems(@Valid @RequestBody ShippingDetailItemsDO shippingDetailItems) {
        return success(shippingDetailService.createShippingDetailItems(shippingDetailItems));
    }

    @PutMapping("/shipping-detail-items/update")
    @Operation(summary = "更新配船明细表明细项次档")
    @PreAuthorize("@ss.hasPermission('sms:shipping-detail:update')")
    public CommonResult<Boolean> updateShippingDetailItems(@Valid @RequestBody ShippingDetailItemsDO shippingDetailItems) {
        shippingDetailService.updateShippingDetailItems(shippingDetailItems);
        return success(true);
    }

    @DeleteMapping("/shipping-detail-items/delete")
    @Parameter(name = "id", description = "编号", required = true)
    @Operation(summary = "删除配船明细表明细项次档")
    @PreAuthorize("@ss.hasPermission('sms:shipping-detail:delete')")
    public CommonResult<Boolean> deleteShippingDetailItems(@RequestParam("id") Long id) {
        shippingDetailService.deleteShippingDetailItems(id);
        return success(true);
    }

	@GetMapping("/shipping-detail-items/get")
	@Operation(summary = "获得配船明细表明细项次档")
	@Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sms:shipping-detail:query')")
	public CommonResult<ShippingDetailItemsDO> getShippingDetailItems(@RequestParam("id") Long id) {
	    return success(shippingDetailService.getShippingDetailItems(id));
	}

    @GetMapping("/shipping-detail-items/groupbyItems")
    @Operation(summary = "获得配船明细表明细项次档汇总")
    @Parameter(name = "parentid", description = "parentid", required = true)
    @PreAuthorize("@ss.hasPermission('sms:shipping-detail:query')")
    public CommonResult<List<ShippingDetailItemsDO>> groupbyItems(@RequestParam("parentid") Long parentid,
                                                                  @RequestParam("contractTradeId") String contractTradeId) {
        return success(shippingDetailService.groupbyItems(parentid, contractTradeId));
    }

    @GetMapping("/shipping-detail-items/export-excel")
    @Operation(summary = "导出配船明细项次 Excel")
    @PreAuthorize("@ss.hasPermission('sms:shipping-detail:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportShippingDetailItemExcel(@RequestParam("parentid") Long parentid,
                                              HttpServletResponse response) throws IOException {
        PageParam pageParam = new PageParam();
        pageParam.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ShippingDetailItemsDO> list = shippingDetailService.getShippingDetailItemsPage(pageParam, parentid).getList();
        // 导出 Excel
        ExcelUtils.write(response, "配船明细.xls", "数据", ShippingDetailItemsRespVO.class,
                BeanUtils.toBean(list, ShippingDetailItemsRespVO.class));
    }
}
package cn.iocoder.yudao.module.sms.controller.admin.gmcontracttransfer;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.sms.controller.admin.gmcontracttransfer.vo.*;
import cn.iocoder.yudao.module.sms.dal.dataobject.gmcontracttransfer.GmContractTransferDO;
import cn.iocoder.yudao.module.sms.service.gmcontracttransfer.GmContractTransferService;

@Tag(name = "管理后台 - 外贸合同交接")
@RestController
@RequestMapping("/sms/gm-contract-transfer")
@Validated
public class GmContractTransferController {

    @Resource
    private GmContractTransferService gmContractTransferService;

    @PostMapping("/create")
    @Operation(summary = "创建外贸合同交接")
    @PreAuthorize("@ss.hasPermission('sms:gm-contract-transfer:create')")
    public CommonResult<Long> createGmContractTransfer(@Valid @RequestBody GmContractTransferSaveReqVO createReqVO) {
        return success(gmContractTransferService.createGmContractTransfer(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新外贸合同交接")
    @PreAuthorize("@ss.hasPermission('sms:gm-contract-transfer:update')")
    public CommonResult<Boolean> updateGmContractTransfer(@Valid @RequestBody GmContractTransferSaveReqVO updateReqVO) {
        gmContractTransferService.updateGmContractTransfer(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除外贸合同交接")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sms:gm-contract-transfer:delete')")
    public CommonResult<Boolean> deleteGmContractTransfer(@RequestParam("id") Long id) {
        gmContractTransferService.deleteGmContractTransfer(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得外贸合同交接")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sms:gm-contract-transfer:query')")
    public CommonResult<GmContractTransferRespVO> getGmContractTransfer(@RequestParam("id") Long id) {
        GmContractTransferDO gmContractTransfer = gmContractTransferService.getGmContractTransfer(id);
        return success(BeanUtils.toBean(gmContractTransfer, GmContractTransferRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得外贸合同交接分页")
    @PreAuthorize("@ss.hasPermission('sms:gm-contract-transfer:query')")
    public CommonResult<PageResult<GmContractTransferRespVO>> getGmContractTransferPage(@Valid GmContractTransferPageReqVO pageReqVO) {
        PageResult<GmContractTransferDO> pageResult = gmContractTransferService.getGmContractTransferPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, GmContractTransferRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出外贸合同交接 Excel")
    @PreAuthorize("@ss.hasPermission('sms:gm-contract-transfer:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportGmContractTransferExcel(@Valid GmContractTransferPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<GmContractTransferDO> list = gmContractTransferService.getGmContractTransferPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "外贸合同交接.xls", "数据", GmContractTransferRespVO.class,
                        BeanUtils.toBean(list, GmContractTransferRespVO.class));
    }

    @GetMapping("/effect")
    @Operation(summary = "生效")
    @Parameter(name = "id", description = "编号", required = true)
    public void effectiveContractTransfer(@RequestParam("id") Long id) {
        gmContractTransferService.effectiveContractTransfer(id);
    }

}

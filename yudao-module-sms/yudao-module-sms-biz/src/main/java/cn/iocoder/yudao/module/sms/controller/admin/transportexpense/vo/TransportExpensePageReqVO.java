package cn.iocoder.yudao.module.sms.controller.admin.transportexpense.vo;

import lombok.*;

import java.time.LocalDate;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 运杂费报支管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TransportExpensePageReqVO extends PageParam {

    @Schema(description = "申请单号")
    private String applicationNo;

    @Schema(description = "报支单号")
    private String reimbursementNo;

    @Schema(description = "承运商代码")
    private String carrierCode;

    @Schema(description = "总金额")
    private BigDecimal totalAmount;

    @Schema(description = "总暂估金额")
    private BigDecimal totalEstimatedAmount;

    @Schema(description = "总本次冲抵金额")
    private BigDecimal totalOffsetAmount;

    @Schema(description = "数据类别")
    private String appid;

    @Schema(description = "币别")
    private String currency;

    @Schema(description = "运杂费类别", example = "1")
    private String expenseType;

    @Schema(description = "付款方式")
    private String paymentMethod;

    @Schema(description = "预计付款日期")
    private String[] estimatedPaymentDate;

    @Schema(description = "报支人")
    private String applicant;

    @Schema(description = "状态", example = "2")
    private String status;

    @Schema(description = "备注")
    private String remarks;

    @Schema(description = "预留字段1 ")
    private String preText1;

    @Schema(description = "预留字段2 ")
    private String preText2;

    @Schema(description = "预留字段3 ")
    private String preText3;

    @Schema(description = "预留字段4 ")
    private String preText4;

    @Schema(description = "预留字段5 ")
    private String preText5;

    @Schema(description = "预留字段6 ")
    private String preText6;

    @Schema(description = "预留字段7 ")
    private String preText7;

    @Schema(description = "预留字段8 ")
    private String preText8;

    @Schema(description = "预留字段9 ")
    private String preText9;

    @Schema(description = "预留字段10 ")
    private String preText10;

    @Schema(description = "预留字段11 ")
    private BigDecimal preNum11;

    @Schema(description = "预留字段12 ")
    private BigDecimal preNum12;

    @Schema(description = "预留字段13 ")
    private BigDecimal preNum13;

    @Schema(description = "预留字段14 ")
    private BigDecimal preNum14;

    @Schema(description = "预留字段15 ")
    private BigDecimal preNum15;

    @Schema(description = "创建者")
    private String creator;

    @Schema(description = "更新者")
    private String updater;

    @Schema(description = "新增时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "修改时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] updateTime;

    @Schema(description = "创建者姓名")
    private String createEmpNo;

    @Schema(description = "更新者姓名")
    private String updateEmpNo;

}
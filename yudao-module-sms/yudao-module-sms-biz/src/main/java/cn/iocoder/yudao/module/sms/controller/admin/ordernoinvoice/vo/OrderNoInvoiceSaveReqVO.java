package cn.iocoder.yudao.module.sms.controller.admin.ordernoinvoice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 提货单备份_销售订单主档新增/修改 Request VO")
@Data
public class OrderNoInvoiceSaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "30734")
    private Long id;

    @Schema(description = "对应销售订单主档表ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "25307")
    @NotNull(message = "对应销售订单主档表ID不能为空")
    private Long mainid;

    @Schema(description = "提货单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "提货单号不能为空")
    private String displistno;

    @Schema(description = "项目号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "项目号不能为空")
    private String project;

    @Schema(description = "公司别", requiredMode = Schema.RequiredMode.REQUIRED, example = "2527")
    @NotEmpty(message = "公司别不能为空")
    private String compId;

    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "订单编号不能为空")
    private String orderNo;

    @Schema(description = "订单状态", example = "2")
    private String orderStatus;

    @Schema(description = "订单类型", example = "1")
    private String orderType;

    @Schema(description = "客户编号")
    private String custNo;

    @Schema(description = "销售月份")
    private String saleMonth;

    @Schema(description = "订单日期")
    private String orderDate;

    @Schema(description = "销售方式")
    private String saleWay;

    @Schema(description = "订货方式")
    private String orderWay;

    @Schema(description = "收款方式")
    private String collWay;

    @Schema(description = "币别")
    private String crcyUnit;

    @Schema(description = "销售人员")
    private String saleBy;

    @Schema(description = "增值税率")
    private BigDecimal taxrate;

    @Schema(description = "预定交期")
    private String planDeliveryDate;

    @Schema(description = "建立日期")
    private String createDate;

    @Schema(description = "建立人员")
    private String createBy;

    @Schema(description = "修改日期")
    private String updateDate;

    @Schema(description = "修改人员")
    private String updateBy;

    @Schema(description = "附件")
    private String annex;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "订单变更版本")
    private String orderVer;

    @Schema(description = "运输方式")
    private String transportWay;

    @Schema(description = "预留字段1 ")
    private String preText1;

    @Schema(description = "预留字段2 ")
    private String preText2;

    @Schema(description = "预留字段3 ")
    private String preText3;

    @Schema(description = "预留字段4 ")
    private String preText4;

    @Schema(description = "预留字段5 ")
    private String preText5;

    @Schema(description = "预留字段6 ")
    private String preText6;

    @Schema(description = "预留字段7 ")
    private String preText7;

    @Schema(description = "预留字段8 ")
    private String preText8;

    @Schema(description = "预留字段9 ")
    private String preText9;

    @Schema(description = "预留字段10 ")
    private String preText10;

    @Schema(description = "预留字段11 ")
    private BigDecimal preNum11;

    @Schema(description = "预留字段12 ")
    private BigDecimal preNum12;

    @Schema(description = "预留字段13 ")
    private BigDecimal preNum13;

    @Schema(description = "预留字段14 ")
    private BigDecimal preNum14;

    @Schema(description = "预留字段15 ")
    private BigDecimal preNum15;

    @Schema(description = "工作流状态", example = "2")
    private String status;

    @Schema(description = "销售类型", example = "2")
    private String saleType;

    @Schema(description = "创建者姓名")
    private String createEmpNo;

    @Schema(description = "更新者者姓名")
    private String updateEmpNo;

    @Schema(description = "建立人员姓名", example = "赵六")
    private String createByName;

    @Schema(description = "修改人员姓名", example = "赵六")
    private String updateByName;

}
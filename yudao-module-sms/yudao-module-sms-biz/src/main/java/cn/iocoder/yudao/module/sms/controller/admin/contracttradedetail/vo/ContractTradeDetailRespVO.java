package cn.iocoder.yudao.module.sms.controller.admin.contracttradedetail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 外贸订单项次档 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ContractTradeDetailRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("ID")
    private Long id;

    @Schema(description = "外贸订单主档ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("外贸订单主档ID")
    private String contractTradeId;

    @Schema(description = "外贸订单项次档ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("外贸订单项次档ID")
    private String contractTradeDetailId;

    @Schema(description = "产品规范")
    @ExcelProperty("产品规范")
    private String standard;

    @Schema(description = "MES产品规范")
    @ExcelProperty("MES产品规范")
    private String standardMes;

    @Schema(description = "牌号")
    @ExcelProperty("牌号")
    private String shopSign;

    @Schema(description = "LOT")
    @ExcelProperty("LOT")
    private String lot;

    @Schema(description = "厚度（mm）")
    @ExcelProperty("厚度（mm）")
    private String thickness;

    @Schema(description = "宽度（mm）")
    @ExcelProperty("宽度（mm）")
    private String width;

    @Schema(description = "长度（mm）")
    @ExcelProperty("长度（mm）")
    private String length;

    @Schema(description = "尺寸信息")
    @ExcelProperty("尺寸信息")
    private String sizeInfo;

    @Schema(description = "规格")
    @ExcelProperty("规格")
    private String specification;

    @Schema(description = "切边要求")
    @ExcelProperty("切边要求")
    private String scrapEdgeRequest;

    @Schema(description = "交货宽度上限")
    @ExcelProperty("交货宽度上限")
    private String widthBoundStart;

    @Schema(description = "交货宽度下限")
    @ExcelProperty("交货宽度下限")
    private String widthBoundEnd;

    @Schema(description = "交货长度上限")
    @ExcelProperty("交货长度上限")
    private String lengthBoundStart;

    @Schema(description = "交货长度下限")
    @ExcelProperty("交货长度下限")
    private String lengthBoundEnd;

    @Schema(description = "出货管制方式")
    @ExcelProperty("出货管制方式")
    private String controlType;

    @Schema(description = "订购数量")
    @ExcelProperty("订购数量")
    private String orderCount;

    @Schema(description = "订购重量（MT）")
    @ExcelProperty("订购重量（MT）")
    private String orderWeight;

    @Schema(description = "特准文号")
    @ExcelProperty("特准文号")
    private String specialProof;

    @Schema(description = "结算人民币单价")
    @ExcelProperty("结算人民币单价")
    private String ctSettlementPrice;

    @Schema(description = "销售单价")
    @ExcelProperty("销售单价")
    private String sellPrice;

    @Schema(description = "销售加权平均价")
    @ExcelProperty("销售加权平均价")
    private String sellAveragePrice;

    @Schema(description = "销售金额")
    @ExcelProperty("销售金额")
    private String sellAmount;

    @Schema(description = "报关单价")
    @ExcelProperty("报关单价")
    private String clearancePrice;

    @Schema(description = "报关加权平均价")
    @ExcelProperty("报关加权平均价")
    private String clearanceAveragePrice;

    @Schema(description = "报关金额")
    @ExcelProperty("报关金额")
    private String clearanceAmount;

    @Schema(description = "含税单价")
    @ExcelProperty("含税单价")
    private String salesCompanyPriceTax;

    @Schema(description = "不含税单价")
    @ExcelProperty("不含税单价")
    private String salesCompanyPriceNotax;

    @Schema(description = "出厂价格（含税）")
    @ExcelProperty("出厂价格（含税）")
    private String cnyPriceTax;

    @Schema(description = "出厂价格（不含税）")
    @ExcelProperty("出厂价格（不含税）")
    private String cnyPriceNotax;

    @Schema(description = "出口价格折算基价FOB")
    @ExcelProperty("出口价格折算基价FOB")
    private String fobPrice;

    @Schema(description = "备案底价")
    @ExcelProperty("备案底价")
    private String recordPrice;

    @Schema(description = "市场时点价格")
    @ExcelProperty("市场时点价格")
    private String bazaarPrice;

    @Schema(description = "价差")
    @ExcelProperty("价差")
    private String costSingle;

    @Schema(description = "毛利2")
    @ExcelProperty("毛利2")
    private String costMake;

    @Schema(description = "毛利4")
    @ExcelProperty("毛利4")
    private String costComplete;

    @Schema(description = "佣金")
    @ExcelProperty("佣金")
    private String ctBrokerage;

    @Schema(description = "成本差")
    @ExcelProperty("成本差")
    private String costVariance;

    @Schema(description = "备案日期")
    @ExcelProperty("备案日期")
    private LocalDateTime recordTime;

    @Schema(description = "备注")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "新增时间")
    @ExcelProperty("新增时间")
    private LocalDateTime createTime;

    @Schema(description = "创建者姓名")
    @ExcelProperty("创建者姓名")
    private String createEmpNo;

    @Schema(description = "更新者姓名")
    @ExcelProperty("更新者姓名")
    private String updateEmpNo;

}

package cn.iocoder.yudao.module.sms.controller.admin.ttfinalinvoice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import java.math.BigDecimal;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - TT尾款发票 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TtFinalInvoiceRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("ID")
    private Long id;

    @Schema(description = "PARENTID")
    @ExcelProperty("PARENTID")
    private Long parentid;

    @Schema(description = "发票类型")
    @ExcelProperty("发票类型")
    private String invoiceType;

    @Schema(description = "记录编号")
    @ExcelProperty("记录编号")
    private String ttFinalInvoiceNo;

    @Schema(description = "状态")
    @ExcelProperty("状态")
    private String state;

    @Schema(description = "外贸合同", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("外贸合同")
    private String contractTradeId;

    @Schema(description = "发票编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("发票编号")
    private String invoiceNumber;

    @Schema(description = "海外代理公司")
    @ExcelProperty("海外代理公司")
    private String agencyCompany;

    @Schema(description = "客户")
    @ExcelProperty("客户")
    private String buyerId;

    @Schema(description = "产品来源")
    @ExcelProperty("产品来源")
    private String productCode;

    @Schema(description = "产品大类")
    @ExcelProperty("产品大类")
    private String productBigCode;

    @Schema(description = "采购产品名称")
    @ExcelProperty("采购产品名称")
    private String productFormName;

    @Schema(description = "采购产品")
    @ExcelProperty("采购产品")
    private String productFormCode;

    @Schema(description = "合同金额")
    @ExcelProperty("合同金额")
    private BigDecimal amount;

    @Schema(description = "价格条款")
    @ExcelProperty("价格条款")
    private String bargain;

    @Schema(description = "开票日期")
    @ExcelProperty("开票日期")
    private String invoiceDate;

    @Schema(description = "装货港")
    @ExcelProperty("装货港")
    private String shippingPort;

    @Schema(description = "卸货港")
    @ExcelProperty("卸货港")
    private String destinationPort;

    @Schema(description = "预付款金额")
    @ExcelProperty("预付款金额")
    private BigDecimal prepaymentAmount;

    @Schema(description = "尾款金额")
    @ExcelProperty("尾款金额")
    private BigDecimal finalPaymentAmount;

    @Schema(description = "银行名称")
    @ExcelProperty("银行名称")
    private String bankName;

    @Schema(description = "银行代码")
    @ExcelProperty("银行代码")
    private String bankCode;

    @Schema(description = "业务部门名称", example = "芋艿")
    @ExcelProperty("业务部门名称")
    private String deptName;

    @Schema(description = "业务部门")
    @ExcelProperty("业务部门")
    private String deptCode;

    @Schema(description = "业务员名称", example = "张三")
    @ExcelProperty("业务员名称")
    private String salesmanName;

    @Schema(description = "业务员")
    @ExcelProperty("业务员")
    private String salesmanCode;

    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "预留字段1 ")
    @ExcelProperty("预留字段1 ")
    private String preText1;

    @Schema(description = "预留字段2 ")
    @ExcelProperty("预留字段2 ")
    private String preText2;

    @Schema(description = "预留字段3 ")
    @ExcelProperty("预留字段3 ")
    private String preText3;

    @Schema(description = "预留字段4 ")
    @ExcelProperty("预留字段4 ")
    private String preText4;

    @Schema(description = "预留字段5 ")
    @ExcelProperty("预留字段5 ")
    private String preText5;

    @Schema(description = "预留字段6 ")
    @ExcelProperty("预留字段6 ")
    private String preText6;

    @Schema(description = "预留字段7 ")
    @ExcelProperty("预留字段7 ")
    private String preText7;

    @Schema(description = "预留字段8 ")
    @ExcelProperty("预留字段8 ")
    private String preText8;

    @Schema(description = "预留字段9 ")
    @ExcelProperty("预留字段9 ")
    private String preText9;

    @Schema(description = "预留字段10 ")
    @ExcelProperty("预留字段10 ")
    private String preText10;

    @Schema(description = "预留字段11 ")
    @ExcelProperty("预留字段11 ")
    private BigDecimal preNum11;

    @Schema(description = "预留字段12 ")
    @ExcelProperty("预留字段12 ")
    private BigDecimal preNum12;

    @Schema(description = "预留字段13 ")
    @ExcelProperty("预留字段13 ")
    private BigDecimal preNum13;

    @Schema(description = "预留字段14 ")
    @ExcelProperty("预留字段14 ")
    private BigDecimal preNum14;

    @Schema(description = "预留字段15 ")
    @ExcelProperty("预留字段15 ")
    private BigDecimal preNum15;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "重量")
    @ExcelProperty("重量")
    private BigDecimal orderWeight;

    @Schema(description = "附件")
    @ExcelProperty("附件")
    private String annex;
}
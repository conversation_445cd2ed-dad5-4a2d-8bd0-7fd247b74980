package cn.iocoder.yudao.module.sms.controller.admin.collectionpaymentmain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 代收代付主档 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CollectionPaymentMainRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "6822")
    @ExcelProperty("ID")
    private Long id;

    @Schema(description = "数据类别")
    @ExcelProperty("数据类别")
    private String appid;

    @Schema(description = "流程编号")
    @ExcelProperty("流程编号")
    private String processInstanceId;

    @Schema(description = "代收代付单号")
    @ExcelProperty("代收代付单号")
    private String collectionPaymentNo;

    @Schema(description = "报支单号")
    @ExcelProperty("报支单号")
    private String reimbursementNo;

    @Schema(description = "承运商")
    @ExcelProperty(value = "承运商", converter = DictConvert.class)
    @DictFormat("SMS_CARRIER") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private String carrierCode;

    @Schema(description = "申请状态", example = "1")
    @ExcelProperty(value = "申请状态", converter = DictConvert.class)
    @DictFormat("TRANSPORT_INVOICE_STATUS") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private String applicationStatus;

    @Schema(description = "预计付款日期")
    @ExcelProperty("预计付款日期")
    private String estimatedPaymentDate;

    @Schema(description = "创建部门")
    @ExcelProperty("创建部门")
    private String createDept;

    @Schema(description = "创建部门名称", example = "赵六")
    @ExcelProperty("创建部门名称")
    private String createDeptName;

    @Schema(description = "预留字段1 ")
    @ExcelProperty("预留字段1 ")
    private String preText1;

    @Schema(description = "预留字段2 ")
    @ExcelProperty("预留字段2 ")
    private String preText2;

    @Schema(description = "预留字段3 ")
    @ExcelProperty("预留字段3 ")
    private String preText3;

    @Schema(description = "预留字段4 ")
    @ExcelProperty("预留字段4 ")
    private String preText4;

    @Schema(description = "预留字段5 ")
    @ExcelProperty("预留字段5 ")
    private String preText5;

    @Schema(description = "预留字段6 ")
    @ExcelProperty("预留字段6 ")
    private String preText6;

    @Schema(description = "预留字段7 ")
    @ExcelProperty("预留字段7 ")
    private String preText7;

    @Schema(description = "预留字段8 ")
    @ExcelProperty("预留字段8 ")
    private String preText8;

    @Schema(description = "预留字段9 ")
    @ExcelProperty("预留字段9 ")
    private String preText9;

    @Schema(description = "预留字段10 ")
    @ExcelProperty("预留字段10 ")
    private String preText10;

    @Schema(description = "预留字段11 ")
    @ExcelProperty("预留字段11 ")
    private BigDecimal preNum11;

    @Schema(description = "预留字段12 ")
    @ExcelProperty("预留字段12 ")
    private BigDecimal preNum12;

    @Schema(description = "预留字段13 ")
    @ExcelProperty("预留字段13 ")
    private BigDecimal preNum13;

    @Schema(description = "预留字段14 ")
    @ExcelProperty("预留字段14 ")
    private BigDecimal preNum14;

    @Schema(description = "预留字段15 ")
    @ExcelProperty("预留字段15 ")
    private BigDecimal preNum15;

    @Schema(description = "创建者")
    @ExcelProperty("创建者")
    private String creator;

    @Schema(description = "更新者")
    @ExcelProperty("更新者")
    private String updater;

    @Schema(description = "新增时间")
    @ExcelProperty("新增时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    @ExcelProperty("修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建者姓名")
    @ExcelProperty("创建者姓名")
    private String createEmpNo;

    @Schema(description = "更新者姓名")
    @ExcelProperty("更新者姓名")
    private String updateEmpNo;

}
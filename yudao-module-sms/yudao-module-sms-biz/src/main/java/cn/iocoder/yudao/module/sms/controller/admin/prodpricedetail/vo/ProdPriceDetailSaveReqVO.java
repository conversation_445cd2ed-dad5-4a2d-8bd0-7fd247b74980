package cn.iocoder.yudao.module.sms.controller.admin.prodpricedetail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 产品定价明细新增/修改 Request VO")
@Data
public class ProdPriceDetailSaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String priceNo;

    @Schema(description = "来源")
    private String source;

    @Schema(description = "产品大类")
    private String prodClass;

    @Schema(description = "产品编号")
    private String prodNo;

    @Schema(description = "产品名称")
    private String prodName;

    @Schema(description = "材质")
    private String texture;

    @Schema(description = "目标市场")
    private String targetMarket;

    @Schema(description = "备案价格")
    private String filingPrice;

    @Schema(description = "出口价格(含税)")
    private String exportPriceTax;

    @Schema(description = "出口价格(不含税)")
    private String exportPrice;

    @Schema(description = "港杂费(含税)")
    private String portSurchargeTax;

    @Schema(description = "港杂费(不含税)")
    private String portSurcharge;

    @Schema(description = "铁路运费(含税)")
    private String railwayFreightTax;

    @Schema(description = "铁路运费(不含税)")
    private String railwayFreight;

    @Schema(description = "关税额")
    private String tariffAmt;

    @Schema(description = "成本差")
    private String cosyGap;

    @Schema(description = "国内时点价格")
    private String homeTimelyPrice;

    @Schema(description = "价差")
    private String differencrPrice;

    @Schema(description = "双扣成本")
    private String doubleDeducteCost;

    @Schema(description = "单扣成本")
    private String singleDeducteCost;

    @Schema(description = "制造成本")
    private String makeCost;

    @Schema(description = "完全成本")
    private String fullCost;

    @Schema(description = "毛利1")
    private String grossProfit1;

    @Schema(description = "毛利2")
    private String grossProfit2;

    @Schema(description = "毛利3")
    private String grossProfit3;

    @Schema(description = "毛利4")
    private String grossProfit4;

    @Schema(description = "加价")
    private String raisePrice;

    @Schema(description = "出口价格折算基价FOB")
    private String fobPrice;

    @Schema(description = "是否手动录入")
    private String manual;

}

package cn.iocoder.yudao.module.sms.controller.admin.gmcontracttransfer.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 外贸合同交接分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class GmContractTransferPageReqVO extends PageParam {

    @Schema(description = "合同交接编号")
    private String contractTransferNo;

    @Schema(description = "外贸合同")
    private String contractTradeNo;

    @Schema(description = "合同数量")
    private String contractNum;

    @Schema(description = "合同单价")
    private String contractPrice;

    @Schema(description = "合同金额")
    private String contractAmount;

    @Schema(description = "收款金额")
    private String collectAmount;

    @Schema(description = "到款时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] accountTime;

    @Schema(description = "客户")
    private String custName;

    @Schema(description = "接收人")
    private String recipient;

    @Schema(description = "下至何月资源")
    private String resourceMonth;

    @Schema(description = "出口国家")
    private String desCountry;

    @Schema(description = "发运港口")
    private String desPort;

    @Schema(description = "运输方式")
    private String transWay;

    @Schema(description = "备货日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] stockupTime;

    @Schema(description = "最晚装期")
    private String deliveryTimeTrand;

    @Schema(description = "排产方式")
    private String scheduleProduct;

    @Schema(description = "贸易方")
    private String trader;

    @Schema(description = "新增时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}

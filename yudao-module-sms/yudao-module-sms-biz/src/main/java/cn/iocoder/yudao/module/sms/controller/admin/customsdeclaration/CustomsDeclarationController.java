package cn.iocoder.yudao.module.sms.controller.admin.customsdeclaration;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.sms.controller.admin.customsdeclaration.vo.*;
import cn.iocoder.yudao.module.sms.dal.dataobject.customsdeclaration.CustomsDeclarationDO;
import cn.iocoder.yudao.module.sms.dal.dataobject.customsdeclaration.CustomsDeclarationDetailDO;
import cn.iocoder.yudao.module.sms.service.customsdeclaration.CustomsDeclarationService;

@Tag(name = "管理后台 - 报关单据")
@RestController
@RequestMapping("/sms/customs-declaration")
@Validated
public class CustomsDeclarationController {

    @Resource
    private CustomsDeclarationService customsDeclarationService;

    @PostMapping("/create")
    @Operation(summary = "创建报关单据")
    @PreAuthorize("@ss.hasPermission('sms:customs-declaration:create')")
    public CommonResult<Long> createCustomsDeclaration(@Valid @RequestBody CustomsDeclarationSaveReqVO createReqVO) {
        return success(customsDeclarationService.createCustomsDeclaration(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新报关单据")
    @PreAuthorize("@ss.hasPermission('sms:customs-declaration:update')")
    public CommonResult<Boolean> updateCustomsDeclaration(@Valid @RequestBody CustomsDeclarationSaveReqVO updateReqVO) {
        customsDeclarationService.updateCustomsDeclaration(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除报关单据")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sms:customs-declaration:delete')")
    public CommonResult<Boolean> deleteCustomsDeclaration(@RequestParam("id") Long id) {
        customsDeclarationService.deleteCustomsDeclaration(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得报关单据")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sms:customs-declaration:query')")
    public CommonResult<CustomsDeclarationRespVO> getCustomsDeclaration(@RequestParam("id") Long id) {
        CustomsDeclarationDO customsDeclaration = customsDeclarationService.getCustomsDeclaration(id);
        return success(BeanUtils.toBean(customsDeclaration, CustomsDeclarationRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得报关单据分页")
    @PreAuthorize("@ss.hasPermission('sms:customs-declaration:query')")
    public CommonResult<PageResult<CustomsDeclarationRespVO>> getCustomsDeclarationPage(@Valid CustomsDeclarationPageReqVO pageReqVO) {
        PageResult<CustomsDeclarationDO> pageResult = customsDeclarationService.getCustomsDeclarationPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CustomsDeclarationRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出报关单据 Excel")
    @PreAuthorize("@ss.hasPermission('sms:customs-declaration:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCustomsDeclarationExcel(@Valid CustomsDeclarationPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<CustomsDeclarationDO> list = customsDeclarationService.getCustomsDeclarationPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "报关单据.xls", "数据", CustomsDeclarationRespVO.class,
                        BeanUtils.toBean(list, CustomsDeclarationRespVO.class));
    }

    // ==================== 子表（报关单据明细） ====================

    @GetMapping("/customs-declaration-detail/list-by-parentid")
    @Operation(summary = "获得报关单据明细列表")
    @Parameter(name = "parentid", description = "报关单据主档ID")
    @PreAuthorize("@ss.hasPermission('sms:customs-declaration:query')")
    public CommonResult<List<CustomsDeclarationDetailDO>> getCustomsDeclarationDetailListByParentid(@RequestParam("parentid") Long parentid) {
        return success(customsDeclarationService.getCustomsDeclarationDetailListByParentid(parentid));
    }

}
package cn.iocoder.yudao.module.sms.controller.admin.custcreditlimit;

import cn.iocoder.yudao.module.sms.controller.admin.common.SmsCommonFuncController;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;
import java.util.function.Supplier;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;
import static cn.iocoder.yudao.module.sms.enums.ErrorCodeConstants.CUST_CREDIT_LIMIT_NOT_EXISTS;

import cn.iocoder.yudao.module.sms.controller.admin.custcreditlimit.vo.*;
import cn.iocoder.yudao.module.sms.dal.dataobject.custcreditlimit.CustCreditLimitDO;
import cn.iocoder.yudao.module.sms.service.custcreditlimit.CustCreditLimitService;

@Tag(name = "管理后台 - 客户授信额度维护")
@RestController
@RequestMapping("/sms/cust-credit-limit")
@Validated
public class CustCreditLimitController {

    @Resource
    private CustCreditLimitService custCreditLimitService;
    @Resource
    private SmsCommonFuncController smsCommonFuncController;

    @PostMapping("/create")
    @Operation(summary = "创建客户授信额度维护")
    @PreAuthorize("@ss.hasPermission('sms:cust-credit-limit:create')")
    public CommonResult<Long> createCustCreditLimit(@Valid @RequestBody CustCreditLimitSaveReqVO createReqVO) {
        return success(custCreditLimitService.createCustCreditLimit(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新客户授信额度维护")
    @PreAuthorize("@ss.hasPermission('sms:cust-credit-limit:update')")
    public CommonResult<Boolean> updateCustCreditLimit(@Valid @RequestBody CustCreditLimitSaveReqVO updateReqVO) {
        CustCreditLimitDO custCreditLimit = custCreditLimitService.getCustCreditLimit(updateReqVO.getId());
        if(custCreditLimit == null){
            throw exception(CUST_CREDIT_LIMIT_NOT_EXISTS);
        }
        Supplier<String> func = ()->{
            custCreditLimitService.updateCustCreditLimit(updateReqVO);
            return null;
        };
        smsCommonFuncController.lockForSms(func,"CUST_NO_"+custCreditLimit.getCustNo());
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除客户授信额度维护")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sms:cust-credit-limit:delete')")
    public CommonResult<Boolean> deleteCustCreditLimit(@RequestParam("id") Long id) {
        custCreditLimitService.deleteCustCreditLimit(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得客户授信额度维护")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sms:cust-credit-limit:query')")
    public CommonResult<CustCreditLimitRespVO> getCustCreditLimit(@RequestParam("id") Long id) {
        CustCreditLimitDO custCreditLimit = custCreditLimitService.getCustCreditLimit(id);
        return success(BeanUtils.toBean(custCreditLimit, CustCreditLimitRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得客户授信额度维护分页")
    @PreAuthorize("@ss.hasPermission('sms:cust-credit-limit:query')")
    public CommonResult<PageResult<CustCreditLimitRespVO>> getCustCreditLimitPage(@Valid CustCreditLimitPageReqVO pageReqVO) {
        PageResult<CustCreditLimitDO> pageResult = custCreditLimitService.getCustCreditLimitPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CustCreditLimitRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出客户授信额度维护 Excel")
    @PreAuthorize("@ss.hasPermission('sms:cust-credit-limit:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCustCreditLimitExcel(@Valid CustCreditLimitPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<CustCreditLimitDO> list = custCreditLimitService.getCustCreditLimitPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "客户授信额度维护.xls", "数据", CustCreditLimitRespVO.class,
                        BeanUtils.toBean(list, CustCreditLimitRespVO.class));
    }

    @GetMapping("/effective")
    @Operation(summary = "生效")
    @Parameter(name = "id", description = "编号", required = true)
    public void effective(@RequestParam("id") Long id) {
        custCreditLimitService.effective(id);
    }

    @GetMapping("/invalid")
    @Operation(summary = "取消生效")
    @Parameter(name = "id", description = "编号", required = true)
    public void invalid(@RequestParam("id") Long id) {
        custCreditLimitService.invalid(id);
    }

}
package cn.iocoder.yudao.module.sms.controller.admin.ordernoinvoice;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.sms.controller.admin.ordernoinvoice.vo.*;
import cn.iocoder.yudao.module.sms.dal.dataobject.ordernoinvoice.OrderNoInvoiceDO;
import cn.iocoder.yudao.module.sms.service.ordernoinvoice.OrderNoInvoiceService;

@Tag(name = "管理后台 - 提货单备份_销售订单主档")
@RestController
@RequestMapping("/sms/order-no-invoice")
@Validated
public class OrderNoInvoiceController {

    @Resource
    private OrderNoInvoiceService orderNoInvoiceService;

    @PostMapping("/create")
    @Operation(summary = "创建提货单备份_销售订单主档")
    @PreAuthorize("@ss.hasPermission('sms:order-no-invoice:create')")
    public CommonResult<Long> createOrderNoInvoice(@Valid @RequestBody OrderNoInvoiceSaveReqVO createReqVO) {
        return success(orderNoInvoiceService.createOrderNoInvoice(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新提货单备份_销售订单主档")
    @PreAuthorize("@ss.hasPermission('sms:order-no-invoice:update')")
    public CommonResult<Boolean> updateOrderNoInvoice(@Valid @RequestBody OrderNoInvoiceSaveReqVO updateReqVO) {
        orderNoInvoiceService.updateOrderNoInvoice(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除提货单备份_销售订单主档")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sms:order-no-invoice:delete')")
    public CommonResult<Boolean> deleteOrderNoInvoice(@RequestParam("id") Long id) {
        orderNoInvoiceService.deleteOrderNoInvoice(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得提货单备份_销售订单主档")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sms:order-no-invoice:query')")
    public CommonResult<OrderNoInvoiceRespVO> getOrderNoInvoice(@RequestParam("id") Long id) {
        OrderNoInvoiceDO orderNoInvoice = orderNoInvoiceService.getOrderNoInvoice(id);
        return success(BeanUtils.toBean(orderNoInvoice, OrderNoInvoiceRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得提货单备份_销售订单主档分页")
    @PreAuthorize("@ss.hasPermission('sms:order-no-invoice:query')")
    public CommonResult<PageResult<OrderNoInvoiceRespVO>> getOrderNoInvoicePage(@Valid OrderNoInvoicePageReqVO pageReqVO) {
        PageResult<OrderNoInvoiceDO> pageResult = orderNoInvoiceService.getOrderNoInvoicePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, OrderNoInvoiceRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出提货单备份_销售订单主档 Excel")
    @PreAuthorize("@ss.hasPermission('sms:order-no-invoice:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportOrderNoInvoiceExcel(@Valid OrderNoInvoicePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<OrderNoInvoiceDO> list = orderNoInvoiceService.getOrderNoInvoicePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "提货单备份_销售订单主档.xls", "数据", OrderNoInvoiceRespVO.class,
                        BeanUtils.toBean(list, OrderNoInvoiceRespVO.class));
    }

}
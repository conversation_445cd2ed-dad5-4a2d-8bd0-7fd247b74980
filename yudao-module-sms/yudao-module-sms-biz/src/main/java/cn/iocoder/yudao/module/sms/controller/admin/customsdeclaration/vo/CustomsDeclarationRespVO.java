package cn.iocoder.yudao.module.sms.controller.admin.customsdeclaration.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 报关单据 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CustomsDeclarationRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10709")
    @ExcelProperty("ID")
    private Long id;

    @Schema(description = "配船单id", example = "15262")
    @ExcelProperty("配船单id")
    private Long parentid;

    @Schema(description = "记录编号")
    @ExcelProperty("记录编号")
    private String customsDeclarationNo;

    @Schema(description = "状态")
    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat("TRANSPORT_INVOICE_STATUS") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private String state;

    @Schema(description = "外贸合同号", requiredMode = Schema.RequiredMode.REQUIRED, example = "12297")
    @ExcelProperty("外贸合同号")
    private String contractTradeId;

    @Schema(description = "发票编号")
    @ExcelProperty("发票编号")
    private String invoiceNumber;

    @Schema(description = "海外代理公司")
    @ExcelProperty("海外代理公司")
    private String agencyCompany;

    @Schema(description = "客户", example = "23673")
    @ExcelProperty("客户")
    private String buyerId;

    @Schema(description = "产品来源")
    @ExcelProperty("产品来源")
    private String productCode;

    @Schema(description = "产品大类")
    @ExcelProperty("产品大类")
    private String productBigCode;

    @Schema(description = "采购产品名称", example = "李四")
    @ExcelProperty("采购产品名称")
    private String productFormName;

    @Schema(description = "采购产品")
    @ExcelProperty("采购产品")
    private String productFormCode;

    @Schema(description = "合同金额")
    @ExcelProperty("合同金额")
    private BigDecimal amount;

    @Schema(description = "价格条款")
    @ExcelProperty("价格条款")
    private String bargain;

    @Schema(description = "开票日期")
    @ExcelProperty("开票日期")
    private String invoiceDate;

    @Schema(description = "装货港")
    @ExcelProperty("装货港")
    private String shippingPort;

    @Schema(description = "卸货港")
    @ExcelProperty("卸货港")
    private String destinationPort;

    @Schema(description = "业务部门名称", example = "张三")
    @ExcelProperty("业务部门名称")
    private String deptName;

    @Schema(description = "业务部门")
    @ExcelProperty("业务部门")
    private String deptCode;

    @Schema(description = "业务员名称", example = "赵六")
    @ExcelProperty("业务员名称")
    private String salesmanName;

    @Schema(description = "业务员")
    @ExcelProperty("业务员")
    private String salesmanCode;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "重量")
    @ExcelProperty("重量")
    private BigDecimal orderWeight;

    @Schema(description = "附件")
    @ExcelProperty("附件")
    private String annex;

    @Schema(description = "预留字段1 ")
    @ExcelProperty("预留字段1 ")
    private String preText1;

    @Schema(description = "预留字段2 ")
    @ExcelProperty("预留字段2 ")
    private String preText2;

    @Schema(description = "预留字段3 ")
    @ExcelProperty("预留字段3 ")
    private String preText3;

    @Schema(description = "预留字段4 ")
    @ExcelProperty("预留字段4 ")
    private String preText4;

    @Schema(description = "预留字段5 ")
    @ExcelProperty("预留字段5 ")
    private String preText5;

    @Schema(description = "预留字段6 ")
    @ExcelProperty("预留字段6 ")
    private String preText6;

    @Schema(description = "预留字段7 ")
    @ExcelProperty("预留字段7 ")
    private String preText7;

    @Schema(description = "预留字段8 ")
    @ExcelProperty("预留字段8 ")
    private String preText8;

    @Schema(description = "预留字段9 ")
    @ExcelProperty("预留字段9 ")
    private String preText9;

    @Schema(description = "预留字段10 ")
    @ExcelProperty("预留字段10 ")
    private String preText10;

    @Schema(description = "预留字段11 ")
    @ExcelProperty("预留字段11 ")
    private BigDecimal preNum11;

    @Schema(description = "预留字段12 ")
    @ExcelProperty("预留字段12 ")
    private BigDecimal preNum12;

    @Schema(description = "预留字段13 ")
    @ExcelProperty("预留字段13 ")
    private BigDecimal preNum13;

    @Schema(description = "预留字段14 ")
    @ExcelProperty("预留字段14 ")
    private BigDecimal preNum14;

    @Schema(description = "预留字段15 ")
    @ExcelProperty("预留字段15 ")
    private BigDecimal preNum15;

    @Schema(description = "创建者")
    @ExcelProperty("创建者")
    private String creator;

    @Schema(description = "更新者")
    @ExcelProperty("更新者")
    private String updater;

    @Schema(description = "新增时间")
    @ExcelProperty("新增时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    @ExcelProperty("修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建者姓名")
    @ExcelProperty("创建者姓名")
    private String createEmpNo;

    @Schema(description = "更新者姓名")
    @ExcelProperty("更新者姓名")
    private String updateEmpNo;

}
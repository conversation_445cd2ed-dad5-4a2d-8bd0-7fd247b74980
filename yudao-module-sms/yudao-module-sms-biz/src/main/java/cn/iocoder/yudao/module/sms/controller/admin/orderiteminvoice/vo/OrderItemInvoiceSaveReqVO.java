package cn.iocoder.yudao.module.sms.controller.admin.orderiteminvoice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 提货单备份_销售订单项次档新增/修改 Request VO")
@Data
public class OrderItemInvoiceSaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15610")
    private Long id;

    @Schema(description = "对应销售订单主档表ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "21041")
    @NotNull(message = "对应销售订单主档表ID不能为空")
    private Long mainid;

    @Schema(description = "提货单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "提货单号不能为空")
    private String displistno;

    @Schema(description = "PARENTID", requiredMode = Schema.RequiredMode.REQUIRED, example = "9169")
    @NotNull(message = "PARENTID不能为空")
    private Long parentid;

    @Schema(description = "项目号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "项目号不能为空")
    private String project;

    @Schema(description = "公司别", requiredMode = Schema.RequiredMode.REQUIRED, example = "32311")
    @NotEmpty(message = "公司别不能为空")
    private String compId;

    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "订单编号不能为空")
    private String orderNo;

    @Schema(description = "订单项次号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "订单项次号不能为空")
    private String orderItemNo;

    @Schema(description = "订单项次状态", example = "2")
    private String orderItemStatus;

    @Schema(description = "产品编号")
    private String prodSpctCode;

    @Schema(description = "订购数量")
    private String orderQty;

    @Schema(description = "订购重量")
    private BigDecimal orderWgt;

    @Schema(description = "订货与结算价格方式")
    private String orderPriceWay;

    @Schema(description = "订单最终单价", example = "17695")
    private BigDecimal orderUnitPrice;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "附件")
    private String annex;

    @Schema(description = "订单变更版本")
    private String orderVer;

    @Schema(description = "建立人员")
    private String createitemBy;

    @Schema(description = "建立日期")
    private String createitemDate;

    @Schema(description = "建立时间")
    private String createitemTime;

    @Schema(description = "修改人员")
    private String updateitemBy;

    @Schema(description = "修改日期")
    private String updateitemDate;

    @Schema(description = "修改时间")
    private String updateitemTime;

    @Schema(description = "确认人员")
    private String confirmitemBy;

    @Schema(description = "确认日期")
    private String confirmitemDate;

    @Schema(description = "确认时间")
    private String confirmitemTime;

    @Schema(description = "结案人员")
    private String endBy;

    @Schema(description = "结案日期")
    private String endDate;

    @Schema(description = "结案时间")
    private String endTime;

    @Schema(description = "预留字段1 ")
    private String preText1;

    @Schema(description = "预留字段2 ")
    private String preText2;

    @Schema(description = "预留字段3 ")
    private String preText3;

    @Schema(description = "预留字段4 ")
    private String preText4;

    @Schema(description = "预留字段5 ")
    private String preText5;

    @Schema(description = "预留字段6 ")
    private String preText6;

    @Schema(description = "预留字段7 ")
    private String preText7;

    @Schema(description = "预留字段8 ")
    private String preText8;

    @Schema(description = "预留字段9 ")
    private String preText9;

    @Schema(description = "预留字段10 ")
    private String preText10;

    @Schema(description = "预留字段11 ")
    private BigDecimal preNum11;

    @Schema(description = "预留字段12 ")
    private BigDecimal preNum12;

    @Schema(description = "预留字段13 ")
    private BigDecimal preNum13;

    @Schema(description = "预留字段14 ")
    private BigDecimal preNum14;

    @Schema(description = "预留字段15 ")
    private BigDecimal preNum15;

    @Schema(description = "计重方式")
    private String meaWay;

    @Schema(description = "订购数量单位")
    private String orderQtyUnit;

    @Schema(description = "订购重量单位")
    private String orderWgtUnit;

    @Schema(description = "计划发货数量")
    private BigDecimal plandeliqty;

    @Schema(description = "计划发货重量")
    private BigDecimal plandeliwet;

    @Schema(description = "计划发货数量单位")
    private String plandeliqtyUnit;

    @Schema(description = "计划发货重量单位")
    private String plandeliwetUnit;

    @Schema(description = "特准文号")
    private String approveNo;

    @Schema(description = "创建者姓名")
    private String createEmpNo;

    @Schema(description = "更新者者姓名")
    private String updateEmpNo;

    @Schema(description = "建立人员姓名", example = "赵六")
    private String createitemByName;

    @Schema(description = "修改人员姓名", example = "芋艿")
    private String updateitemByName;

    @Schema(description = "确认人员姓名", example = "张三")
    private String confirmitemByName;

    @Schema(description = "结案人员姓名", example = "芋艿")
    private String endByName;

}
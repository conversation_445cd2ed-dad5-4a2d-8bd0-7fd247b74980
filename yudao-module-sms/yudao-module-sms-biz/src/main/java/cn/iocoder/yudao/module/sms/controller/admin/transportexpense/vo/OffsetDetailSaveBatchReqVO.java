package cn.iocoder.yudao.module.sms.controller.admin.transportexpense.vo;

import cn.iocoder.yudao.module.sms.dal.dataobject.loadwgt.LoadExpenseDetailDO;
import cn.iocoder.yudao.module.sms.dal.dataobject.transportexpense.TransportExpenseOffsetDetailDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 销售配货装车主档新增/修改 Request VO")
@Data
public class OffsetDetailSaveBatchReqVO {
    private Long parentid;
    private String type;
    private String appid;
    private List<TransportExpenseOffsetDetailDO> items;
}
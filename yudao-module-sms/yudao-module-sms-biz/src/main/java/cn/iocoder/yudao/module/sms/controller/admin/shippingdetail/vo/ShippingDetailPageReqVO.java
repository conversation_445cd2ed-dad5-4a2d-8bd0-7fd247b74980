package cn.iocoder.yudao.module.sms.controller.admin.shippingdetail.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 配船明细分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ShippingDetailPageReqVO extends PageParam {

    @Schema(description = "记录编号")
    private String shippingDetailNo;

    @Schema(description = "船中文名")
    private String shipNameCn;

    @Schema(description = "船英文名")
    private String shipNameEn;

    @Schema(description = "离港日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] departureDate;

    @Schema(description = "靠泊日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] berthingDate;

    @Schema(description = "装运港")
    private String shippingPort;

    @Schema(description = "目的港")
    private String destinationPort;

    @Schema(description = "数量")
    private BigDecimal quantity;

    @Schema(description = "毛重")
    private BigDecimal grossWeight;

    @Schema(description = "净重")
    private BigDecimal netWeight;

    @Schema(description = "外贸合同", example = "5762")
    private String contractTradeId;

    @Schema(description = "其他外贸合同")
    private String otherContractTradeId;

    @Schema(description = "批次号")
    private String batchNumber;

    @Schema(description = "航次")
    private String voyageNumber;

    @Schema(description = "到港日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] arrivalDate;

    @Schema(description = "正唛")
    private String shippingMark;

    @Schema(description = "交接人")
    private String handoverPerson;

    @Schema(description = "备注")
    private String remarks;

    @Schema(description = "预留字段1 ")
    private String preText1;

    @Schema(description = "预留字段2 ")
    private String preText2;

    @Schema(description = "预留字段3 ")
    private String preText3;

    @Schema(description = "预留字段4 ")
    private String preText4;

    @Schema(description = "预留字段5 ")
    private String preText5;

    @Schema(description = "预留字段6 ")
    private String preText6;

    @Schema(description = "预留字段7 ")
    private String preText7;

    @Schema(description = "预留字段8 ")
    private String preText8;

    @Schema(description = "预留字段9 ")
    private String preText9;

    @Schema(description = "预留字段10 ")
    private String preText10;

    @Schema(description = "预留字段11 ")
    private BigDecimal preNum11;

    @Schema(description = "预留字段12 ")
    private BigDecimal preNum12;

    @Schema(description = "预留字段13 ")
    private BigDecimal preNum13;

    @Schema(description = "预留字段14 ")
    private BigDecimal preNum14;

    @Schema(description = "预留字段15 ")
    private BigDecimal preNum15;

    @Schema(description = "新增时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "创建者姓名")
    private String createEmpNo;

    @Schema(description = "更新者姓名")
    private String updateEmpNo;

    @Schema(description = "产品来源")
    private String productCode;

    @Schema(description = "产品大类")
    private String productBigCode;

    @Schema(description = "采购产品")
    private String productFormCode;

    @Schema(description = "采购产品名称", example = "张三")
    private String productFormName;

    @Schema(description = "状态")
    private String state;

    @Schema(description = "付款条款")
    private String paymentTerms;

    @Schema(description = "接收时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] handoverDate;

    @Schema(description = "附件")
    private String annex;

    @Schema(description = "流程ID", example = "18434")
    private String processInstanceId;

}
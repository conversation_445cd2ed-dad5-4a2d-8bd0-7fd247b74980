package cn.iocoder.yudao.module.sms.controller.admin.ordernoinvoice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 提货单备份_销售订单主档 Response VO")
@Data
@ExcelIgnoreUnannotated
public class OrderNoInvoiceRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "30734")
    @ExcelProperty("ID")
    private Long id;

    @Schema(description = "对应销售订单主档表ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "25307")
    @ExcelProperty("对应销售订单主档表ID")
    private Long mainid;

    @Schema(description = "提货单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("提货单号")
    private String displistno;

    @Schema(description = "项目号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("项目号")
    private String project;

    @Schema(description = "公司别", requiredMode = Schema.RequiredMode.REQUIRED, example = "2527")
    @ExcelProperty("公司别")
    private String compId;

    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("订单编号")
    private String orderNo;

    @Schema(description = "订单状态", example = "2")
    @ExcelProperty("订单状态")
    private String orderStatus;

    @Schema(description = "订单类型", example = "1")
    @ExcelProperty("订单类型")
    private String orderType;

    @Schema(description = "客户编号")
    @ExcelProperty("客户编号")
    private String custNo;

    @Schema(description = "销售月份")
    @ExcelProperty("销售月份")
    private String saleMonth;

    @Schema(description = "订单日期")
    @ExcelProperty("订单日期")
    private String orderDate;

    @Schema(description = "销售方式")
    @ExcelProperty("销售方式")
    private String saleWay;

    @Schema(description = "订货方式")
    @ExcelProperty("订货方式")
    private String orderWay;

    @Schema(description = "收款方式")
    @ExcelProperty("收款方式")
    private String collWay;

    @Schema(description = "币别")
    @ExcelProperty("币别")
    private String crcyUnit;

    @Schema(description = "销售人员")
    @ExcelProperty("销售人员")
    private String saleBy;

    @Schema(description = "增值税率")
    @ExcelProperty("增值税率")
    private BigDecimal taxrate;

    @Schema(description = "预定交期")
    @ExcelProperty("预定交期")
    private String planDeliveryDate;

    @Schema(description = "建立日期")
    @ExcelProperty("建立日期")
    private String createDate;

    @Schema(description = "建立人员")
    @ExcelProperty("建立人员")
    private String createBy;

    @Schema(description = "修改日期")
    @ExcelProperty("修改日期")
    private String updateDate;

    @Schema(description = "修改人员")
    @ExcelProperty("修改人员")
    private String updateBy;

    @Schema(description = "附件")
    @ExcelProperty("附件")
    private String annex;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "订单变更版本")
    @ExcelProperty("订单变更版本")
    private String orderVer;

    @Schema(description = "运输方式")
    @ExcelProperty("运输方式")
    private String transportWay;

    @Schema(description = "预留字段1 ")
    @ExcelProperty("预留字段1 ")
    private String preText1;

    @Schema(description = "预留字段2 ")
    @ExcelProperty("预留字段2 ")
    private String preText2;

    @Schema(description = "预留字段3 ")
    @ExcelProperty("预留字段3 ")
    private String preText3;

    @Schema(description = "预留字段4 ")
    @ExcelProperty("预留字段4 ")
    private String preText4;

    @Schema(description = "预留字段5 ")
    @ExcelProperty("预留字段5 ")
    private String preText5;

    @Schema(description = "预留字段6 ")
    @ExcelProperty("预留字段6 ")
    private String preText6;

    @Schema(description = "预留字段7 ")
    @ExcelProperty("预留字段7 ")
    private String preText7;

    @Schema(description = "预留字段8 ")
    @ExcelProperty("预留字段8 ")
    private String preText8;

    @Schema(description = "预留字段9 ")
    @ExcelProperty("预留字段9 ")
    private String preText9;

    @Schema(description = "预留字段10 ")
    @ExcelProperty("预留字段10 ")
    private String preText10;

    @Schema(description = "预留字段11 ")
    @ExcelProperty("预留字段11 ")
    private BigDecimal preNum11;

    @Schema(description = "预留字段12 ")
    @ExcelProperty("预留字段12 ")
    private BigDecimal preNum12;

    @Schema(description = "预留字段13 ")
    @ExcelProperty("预留字段13 ")
    private BigDecimal preNum13;

    @Schema(description = "预留字段14 ")
    @ExcelProperty("预留字段14 ")
    private BigDecimal preNum14;

    @Schema(description = "预留字段15 ")
    @ExcelProperty("预留字段15 ")
    private BigDecimal preNum15;

    @Schema(description = "新增时间")
    @ExcelProperty("新增时间")
    private LocalDateTime createTime;

    @Schema(description = "工作流状态", example = "2")
    @ExcelProperty("工作流状态")
    private String status;

    @Schema(description = "销售类型", example = "2")
    @ExcelProperty("销售类型")
    private String saleType;

    @Schema(description = "创建者姓名")
    @ExcelProperty("创建者姓名")
    private String createEmpNo;

    @Schema(description = "更新者者姓名")
    @ExcelProperty("更新者者姓名")
    private String updateEmpNo;

    @Schema(description = "建立人员姓名", example = "赵六")
    @ExcelProperty("建立人员姓名")
    private String createByName;

    @Schema(description = "修改人员姓名", example = "赵六")
    @ExcelProperty("修改人员姓名")
    private String updateByName;

}
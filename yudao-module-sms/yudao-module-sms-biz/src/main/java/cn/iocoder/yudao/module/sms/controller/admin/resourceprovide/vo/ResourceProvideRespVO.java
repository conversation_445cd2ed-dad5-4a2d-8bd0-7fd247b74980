package cn.iocoder.yudao.module.sms.controller.admin.resourceprovide.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 产品资源发放 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ResourceProvideRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("ID")
    private Long id;

    @Schema(description = "资源发放 编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("资源发放 编号")
    private String resoureProvideId;

    @Schema(description = "批次号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("批次号")
    private String batchNo;

    @Schema(description = "部门", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("部门")
    private String department;

    @Schema(description = "操作人", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("操作人")
    private String operater;

    @Schema(description = "生效时间开始时间")
    @ExcelProperty("生效时间开始时间")
    private LocalDateTime effectStartTime;

    @Schema(description = "生效结束时间")
    @ExcelProperty("生效结束时间")
    private LocalDateTime effectEndTime;

    @Schema(description = "备注")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "新增时间")
    @ExcelProperty("新增时间")
    private LocalDateTime createTime;

    @Schema(description = "状态")
    private String state;

    @Schema(description = "是否被挑选")
    private String selected;

    @Schema(description = "创建者姓名")
    private String createEmpNo;

    @Schema(description = "更新者者姓名")
    private String updateEmpNo;

    @Schema(description = "流程实例的编号")
    private String processInstanceId;

}

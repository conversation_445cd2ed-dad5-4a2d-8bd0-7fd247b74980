package cn.iocoder.yudao.module.pms.controller.admin.locmoncumular.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 储位料号月累计收发 Response VO")
@Data
@ExcelIgnoreUnannotated
public class WmsLocMonCumularRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "项目号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("项目号")
    private String project;

    @Schema(description = "公司别", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("公司别")
    private String compId;

    @Schema(description = "库存年月", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("库存年月")
    private String yearMo;

    @Schema(description = "料号")
    @ExcelProperty("料号")
    private String matrlNo;

    @Schema(description = "品名")
    @ExcelProperty("品名")
    private String matrlName;

    @Schema(description = "品别")
    @ExcelProperty("品别")
    private String inventoryType;

    @Schema(description = "储位")
    @ExcelProperty("储位")
    private String locNo;

    @Schema(description = "批号")
    @ExcelProperty("批号")
    private String lotNo;

    @Schema(description = "品级(备件属性)")
    @ExcelProperty("品级(备件属性)")
    private String matrlGrade;

    @Schema(description = "期末库存量")
    @ExcelProperty("期末库存量")
    private BigDecimal endQty;

    @Schema(description = "期末库存金额")
    @ExcelProperty("期末库存金额")
    private BigDecimal endAmt;

    @Schema(description = "湿重期末库存量")
    @ExcelProperty("期末库存湿重")
    private BigDecimal endWetQty;

    @Schema(description = "湿重期末库存金额")
    private BigDecimal endWetAmt;

    @Schema(description = "月初库存量")
    @ExcelProperty("月初库存量")
    private BigDecimal moBeginQty;

    @Schema(description = "月初库存金额")
    @ExcelProperty("月初库存金额")
    private BigDecimal moBeginAmt;

    @Schema(description = "月累积入库量")
    @ExcelProperty("月累积入库量")
    private BigDecimal moAccuRecvQty;

    @Schema(description = "月累积入库金额")
    @ExcelProperty("月累积入库金额")
    private BigDecimal moAccuRecvAmt;

    @Schema(description = "月累积出库量")
    @ExcelProperty("月累积出库量")
    private BigDecimal moAccuIssuQty;

    @Schema(description = "月累积出库金额")
    @ExcelProperty("月累积出库金额")
    private BigDecimal moAccuIssuAmt;

    @Schema(description = "月调整入库量")
    @ExcelProperty("月调整入库量")
    private BigDecimal moAccuAdjRecvQty;

    @Schema(description = "月调整入库金额")
    @ExcelProperty("月调整入库金额")
    private BigDecimal moAccuAdjRecvAmt;

    @Schema(description = "月调整出库量")
    @ExcelProperty("月调整出库量")
    private BigDecimal moAccuAdjIssuQty;

    @Schema(description = "月调整出库金额")
    @ExcelProperty("月调整出库金额")
    private BigDecimal moAccuAdjIssuAmt;

    @Schema(description = "年初库存量")
    @ExcelProperty("年初库存量")
    private BigDecimal yrBeginQty;

    @Schema(description = "年初库存金额")
    @ExcelProperty("年初库存金额")
    private BigDecimal yrBeginAmt;

    @Schema(description = "年累积入库量")
    @ExcelProperty("年累积入库量")
    private BigDecimal yrAccuRecvQty;

    @Schema(description = "年累积入库金额")
    @ExcelProperty("年累积入库金额")
    private BigDecimal yrAccuRecvAmt;

    @Schema(description = "年累积出库量")
    @ExcelProperty("年累积出库量")
    private BigDecimal yrAccuIssuQty;

    @Schema(description = "年累积出库金额")
    @ExcelProperty("年累积出库金额")
    private BigDecimal yrAccuIssuAmt;

    @Schema(description = "年调整入库量")
    @ExcelProperty("年调整入库量")
    private BigDecimal yrAccuAdjRecvQty;

    @Schema(description = "年调整入库金额")
    @ExcelProperty("年调整入库金额")
    private BigDecimal yrAccuAdjRecvAmt;

    @Schema(description = "年调整出库量")
    @ExcelProperty("年调整出库量")
    private BigDecimal yrAccuAdjIssuQty;

    @Schema(description = "年调整出库金额")
    @ExcelProperty("年调整出库金额")
    private BigDecimal yrAccuAdjIssuAmt;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "备注")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "是否贸易")
    @ExcelProperty("是否贸易")
    private String ismy;


    @Schema(description = "月初库存湿重量")
    @ExcelProperty("月初库存湿重量")
    private BigDecimal moBeginWetQty;

    @Schema(description = "月累积入库湿重量")
    @ExcelProperty("月累积入库湿重量")
    private BigDecimal moAccuRecvWetQty;

    @Schema(description = "月累积出库湿重量")
    @ExcelProperty("月累积出库湿重量")
    private BigDecimal moAccuIssuWetQty;

    @Schema(description = "月调整入库湿重量")
    @ExcelProperty("月调整入库湿重量")
    private BigDecimal moAccuAdjRecvWetQty;

    @Schema(description = "月调整出库湿重量")
    @ExcelProperty("月调整出库湿重量")
    private BigDecimal moAccuAdjIssuWetQty;

    @Schema(description = "年初库存湿重量")
    @ExcelProperty("年初库存湿重量")
    private BigDecimal yrBeginWetQty;

    @Schema(description = "年累积入库湿重量")
    @ExcelProperty("年累积入库湿重量")
    private BigDecimal yrAccuRecvWetQty;

    @Schema(description = "年累积出库湿重量")
    @ExcelProperty("年累积出库湿重量")
    private BigDecimal yrAccuIssuWetQty;

    @Schema(description = "年调整入库湿重量")
    @ExcelProperty("年调整入库湿重量")
    private BigDecimal yrAccuAdjRecvWetQty;

    @Schema(description = "年调整出库湿重量")
    @ExcelProperty("年调整出库湿重量")
    private BigDecimal yrAccuAdjIssuWetQty;


    @Schema(description = "月初库存湿重金额")
    @ExcelProperty("月初库存湿重金额")
    private BigDecimal moBeginWetAmt;

    @Schema(description = "月累积入库湿重金额")
    @ExcelProperty("月累积入库湿重金额")
    private BigDecimal moAccuRecvWetAmt;

    @Schema(description = "月累积出库湿重金额")
    @ExcelProperty("月累积出库湿重金额")
    private BigDecimal moAccuIssuWetAmt;

    @Schema(description = "月调整入库湿重金额")
    @ExcelProperty("月调整入库湿重金额")
    private BigDecimal moAccuAdjRecvWetAmt;

    @Schema(description = "月调整出库湿重金额")
    @ExcelProperty("月调整出库湿重金额")
    private BigDecimal moAccuAdjIssuWetAmt;

    @Schema(description = "年初库存湿重金额")
    @ExcelProperty("年初库存湿重金额")
    private BigDecimal yrBeginWetAmt;

    @Schema(description = "年累积入库湿重金额")
    @ExcelProperty("年累积入库湿重金额")
    private BigDecimal yrAccuRecvWetAmt;

    @Schema(description = "年累积出库湿重金额")
    @ExcelProperty("年累积出库湿重金额")
    private BigDecimal yrAccuIssuWetAmt;

    @Schema(description = "年调整入库湿重金额")
    @ExcelProperty("年调整入库湿重金额")
    private BigDecimal yrAccuAdjRecvWetAmt;

    @Schema(description = "年调整出库湿重金额")
    @ExcelProperty("年调整出库湿重金额")
    private BigDecimal yrAccuAdjIssuWetAmt;

}
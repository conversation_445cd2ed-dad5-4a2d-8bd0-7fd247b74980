package cn.iocoder.yudao.module.pms.controller.admin.wrstradedetail2.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 原料待结算明细 Response VO")
@Data
@ExcelIgnoreUnannotated
public class WrsTradeDetail2RespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "项目号")
    @ExcelProperty("项目号")
    private String project;

    @Schema(description = "公司别")
    @ExcelProperty("公司别")
    private String compId;

    @Schema(description = "申请单号")
    @ExcelProperty("申请单号")
    private String issueTallyNo;

    @Schema(description = "流水序号")
    @ExcelProperty("流水序号")
    private String seqNo;

    @Schema(description = "检验批号")
    @ExcelProperty("检验批号")
    private String chkNo;

    @Schema(description = "交易种类")
    @ExcelProperty("交易种类")
    private String issueType;

    @Schema(description = "手工检验批号")
    @ExcelProperty("手工检验批号")
    private String manualChkNo;

    @Schema(description = "供应商代码")
    @ExcelProperty("供应商代码")
    private String supplierNo;

    @Schema(description = "合同编号")
    @ExcelProperty("合同编号")
    private String contractNo;

    @Schema(description = "原料料号")
    @ExcelProperty("原料料号")
    private String matrlNo;

    @Schema(description = "合同单价")
    @ExcelProperty("合同单价")
    private BigDecimal ctrtUnit;

    @Schema(description = "合同水")
    @ExcelProperty("合同水")
    private BigDecimal ctrtWater;

    @Schema(description = "化验水")
    @ExcelProperty("化验水")
    private BigDecimal chkWater;

    @Schema(description = "合同版次")
    @ExcelProperty("合同版次")
    private String pover;

    @Schema(description = "储位代码")
    @ExcelProperty("储位代码")
    private String stgNo;

    @Schema(description = "取得合同单价日期")
    @ExcelProperty("取得合同单价日期")
    private LocalDate contractDate;

    @Schema(description = "结案人")
    @ExcelProperty("结案人")
    private String finalEmpNo;

    @Schema(description = "结案日期")
    @ExcelProperty("结案日期")
    private String finalDate;

    @Schema(description = "结算人")
    @ExcelProperty("结算人")
    private String settleEmpNo;

    @Schema(description = "结算日期")
    @ExcelProperty("结算日期")
    private String settleDate;

    @Schema(description = "发货数量")
    @ExcelProperty("发货数量")
    private BigDecimal loadNum;

    @Schema(description = "检斤数量")
    @ExcelProperty("检斤数量")
    private BigDecimal scaleNum;

    @Schema(description = "计量上抛数量")
    @ExcelProperty("计量上抛数量")
    private BigDecimal scalediNum;

    @Schema(description = "扣杂质")
    @ExcelProperty("扣杂质")
    private BigDecimal impurityWeight;

    @Schema(description = "交易数量")
    @ExcelProperty("交易数量")
    private BigDecimal transNum;

    @Schema(description = "交易金额")
    @ExcelProperty("交易金额")
    private BigDecimal transAmt;

    @Schema(description = "结算用数量")
    @ExcelProperty("结算用数量")
    private BigDecimal settleNum;

    @Schema(description = "结算用金额")
    @ExcelProperty("结算用金额")
    private BigDecimal settleAmt;

    @Schema(description = "MP结算数量")
    @ExcelProperty("MP结算数量")
    private BigDecimal mpSettleNum;

    @Schema(description = "MP结算金额")
    @ExcelProperty("MP结算金额")
    private BigDecimal mpSettleAmt;

    @Schema(description = "运杂费金额")
    @ExcelProperty("运杂费金额")
    private BigDecimal blendAmt;

    @Schema(description = "磅单号")
    @ExcelProperty("磅单号")
    private String wgtListNo;

    @Schema(description = "结算单号")
    @ExcelProperty("结算单号")
    private String tranTallyNo;

    @Schema(description = "检验样")
    @ExcelProperty("检验样")
    private String qtyAcptType;

    @Schema(description = "发站")
    @ExcelProperty("发站")
    private String sendStation;

    @Schema(description = "车船号")
    @ExcelProperty("车船号")
    private String carNo;

    @Schema(description = "结算用车船号")
    @ExcelProperty("结算用车船号")
    private String settleCarNo;

    @Schema(description = "报支单号")
    @ExcelProperty("报支单号")
    private String billNo;

    @Schema(description = "报支日期")
    @ExcelProperty("报支日期")
    private String billDate;

    @Schema(description = "交易状态")
    @ExcelProperty("交易状态")
    private String stus;

    @Schema(description = "建立人")
    @ExcelProperty("建立人")
    private String createEmpNo;

    @Schema(description = "建立日期")
    @ExcelProperty("建立日期")
    private String createDate;

    @Schema(description = "修改人")
    @ExcelProperty("修改人")
    private String updateEmpNo;

    @Schema(description = "修改日期")
    @ExcelProperty("修改日期")
    private String updateDate;

    @Schema(description = "备注")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "新增时间")
    @ExcelProperty("新增时间")
    private String createTime;

    @Schema(description = "是否检验")
    @ExcelProperty("是否检验")
    private String isChecked;

}
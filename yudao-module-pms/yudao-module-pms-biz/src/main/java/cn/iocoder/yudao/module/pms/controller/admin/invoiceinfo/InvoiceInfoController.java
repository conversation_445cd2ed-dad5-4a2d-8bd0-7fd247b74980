package cn.iocoder.yudao.module.pms.controller.admin.invoiceinfo;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.pms.controller.admin.invoiceinfo.vo.InvoiceInfoPageReqVO;
import cn.iocoder.yudao.module.pms.controller.admin.invoiceinfo.vo.InvoiceInfoRespVO;
import cn.iocoder.yudao.module.pms.controller.admin.invoiceinfo.vo.InvoiceInfoSaveReqVO;
import cn.iocoder.yudao.module.pms.controller.admin.poaudit.vo.PoAuditSaveReqVO;
import cn.iocoder.yudao.module.pms.dal.dataobject.invoiceinfo.InvoiceInfoDO;
import cn.iocoder.yudao.module.pms.service.invoiceinfo.InvoiceInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 发票信息")
@RestController
@RequestMapping("/pms/invoice-info")
@Validated
public class InvoiceInfoController {

    @Resource
    private InvoiceInfoService invoiceInfoService;

    @PostMapping("/create")
    @Operation(summary = "创建发票信息")
    @PreAuthorize("@ss.hasPermission('pms:invoice-info:create')")
    public CommonResult<Long> createInvoiceInfo(@Valid @RequestBody InvoiceInfoSaveReqVO createReqVO) {
        return success(invoiceInfoService.createInvoiceInfo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新发票信息")
    @PreAuthorize("@ss.hasPermission('pms:invoice-info:update')")
    public CommonResult<Boolean> updateInvoiceInfo(@Valid @RequestBody InvoiceInfoSaveReqVO updateReqVO) {
        invoiceInfoService.updateInvoiceInfo(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除发票信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:invoice-info:delete')")
    public CommonResult<Boolean> deleteInvoiceInfo(@RequestParam("id") Long id) {
        invoiceInfoService.deleteInvoiceInfo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得发票信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:invoice-info:query')")
    public CommonResult<InvoiceInfoRespVO> getInvoiceInfo(@RequestParam("id") Long id) {
        InvoiceInfoDO invoiceInfo = invoiceInfoService.getInvoiceInfo(id);
        return success(BeanUtils.toBean(invoiceInfo, InvoiceInfoRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得发票信息分页")
    @PreAuthorize("@ss.hasPermission('pms:invoice-info:query')")
    public CommonResult<PageResult<InvoiceInfoRespVO>> getInvoiceInfoPage(@Valid InvoiceInfoPageReqVO pageReqVO) {
        PageResult<InvoiceInfoDO> pageResult = invoiceInfoService.getInvoiceInfoPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InvoiceInfoRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出发票信息 Excel")
    @PreAuthorize("@ss.hasPermission('pms:invoice-info:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInvoiceInfoExcel(@Valid InvoiceInfoPageReqVO pageReqVO,
                                       HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InvoiceInfoDO> list = invoiceInfoService.getInvoiceInfoPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "发票信息.xls", "数据", InvoiceInfoRespVO.class,
                BeanUtils.toBean(list, InvoiceInfoRespVO.class));
    }

    @GetMapping("/confirm")
    @Operation(summary = "获得发票信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:invoice-info:update')")
    public CommonResult<Boolean> confrimInvoiceInfo(@RequestParam("id") Long id, @RequestParam("flag") String flag) {
        invoiceInfoService.confrimInvoiceInfo(id, flag);
        return success(true);
    }

    @PostMapping("/audit/create")
    @Operation(summary = "创建发票审批流程")
    @PreAuthorize("@ss.hasPermission('pms:po-audit:create')")
    public CommonResult<Long> createInvoiceAudit(@Valid @RequestBody PoAuditSaveReqVO createReqVO) {
        return success(invoiceInfoService.createInvoiceAudit(createReqVO));
    }

}
package cn.iocoder.yudao.module.pms.controller.admin.mpppurchase.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 采购案 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MppPurchaseRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "10272")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "项目")
    @ExcelProperty("项目")
    private String project;

    @Schema(description = "公司代码", example = "515")
    @ExcelProperty("公司代码")
    private String compid;

    @Schema(description = "采购案号")
    @ExcelProperty("采购案号")
    private String purno;

    @Schema(description = "采购案别")
    @ExcelProperty("采购案别")
    private String purcode;

    @Schema(description = "承办人单位")
    @ExcelProperty("承办人单位")
    private String respdept;

    @Schema(description = "承办人职工")
    @ExcelProperty("承办人职工")
    private String respEmpno;

    @Schema(description = "承办人Email")
    @ExcelProperty("承办人Email")
    private String respemail;

    @Schema(description = "承办人手机号码")
    @ExcelProperty("承办人手机号码")
    private String respmobile;

    @Schema(description = "承办人电话")
    @ExcelProperty("承办人电话")
    private String resptel;

    @Schema(description = "承办人传真")
    @ExcelProperty("承办人传真")
    private String respfax;

    @Schema(description = "采购案状态")
    @ExcelProperty("采购案状态")
    private String stus;

    @Schema(description = "采购案备注", example = "你说的对")
    @ExcelProperty("采购案备注")
    private String purmemo;

    @Schema(description = "请购类别", example = "2")
    @ExcelProperty("请购类别")
    private String purtype;

    @Schema(description = "工程预算编号")
    @ExcelProperty("工程预算编号")
    private String budgetno;

    @Schema(description = "新增人员")
    @ExcelProperty("新增人员")
    private String createEmpno;

    @Schema(description = "新增人员")
    @ExcelProperty("新增人员")
    private String creator;

    @Schema(description = "新增时间")
    @ExcelProperty("新增时间")
    private String createTime;

    @Schema(description = "修改人员")
    @ExcelProperty("修改人员")
    private String updateEmpno;

    @Schema(description = "修改人员")
    @ExcelProperty("修改人员")
    private String updater;

    @Schema(description = "修改时间")
    @ExcelProperty("修改时间")
    private String updateTime;


}
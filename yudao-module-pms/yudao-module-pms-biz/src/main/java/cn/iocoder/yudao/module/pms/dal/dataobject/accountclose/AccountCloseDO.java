package cn.iocoder.yudao.module.pms.dal.dataobject.accountclose;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 关账日期 DO
 *
 * <AUTHOR>
 */
@TableName("pms_account_close")
@KeySequence("pms_account_close_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountCloseDO extends BaseDO {

    /**
     * 账务编号
     */
    @TableId
    private Long id;
    /**
     * 项目号
     */
    private String project;
    /**
     * 公司别
     */
    private String compid;
    /**
     * 品别
     */
    private String inventorytype;
    /**
     * 年月
     */
    private String yearmo;
    /**
     * 关帐起日
     */
    private String firstdate;
    /**
     * 关帐止日
     */
    private String enddate;
    /**
     * 关帐是否确定
     */
    private String stus;
    /**
     * 关帐人
     */
    private String closeempno;
    /**
     * 关帐日期
     */
    private String closedate;
    /**
     * 关帐时间
     */
    private String closetime;
    /**
     * 备用栏位
     */
    private String typea;
    /**
     * 备用栏位
     */
    private String typeb;
    /**
     * 备用栏位
     */
    private String typec;
    /**
     * 备用栏位
     */
    private String typed;

}
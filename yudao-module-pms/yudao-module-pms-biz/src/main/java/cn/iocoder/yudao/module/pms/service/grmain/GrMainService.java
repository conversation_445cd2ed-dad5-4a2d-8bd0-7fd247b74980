package cn.iocoder.yudao.module.pms.service.grmain;

import javax.validation.*;

import cn.iocoder.yudao.module.pms.api.pomain.dto.GetEmplByPostReqDTO;
import cn.iocoder.yudao.module.pms.api.pomain.dto.PoMainCheckSaveReqDTO;
import cn.iocoder.yudao.module.pms.api.pomain.dto.PoMainInspSaveReqDTO;
import cn.iocoder.yudao.module.pms.api.pomain.dto.PoMainReqDTO;
import cn.iocoder.yudao.module.pms.controller.admin.grmain.vo.*;
import cn.iocoder.yudao.module.pms.controller.admin.wmstrade.vo.WmsTradeSaveReqVO;
import cn.iocoder.yudao.module.pms.controller.app.grmain.vo.AppGrDetailCheckSaveVO;
import cn.iocoder.yudao.module.pms.controller.app.grmain.vo.AppGrDetailFileRelationVO;
import cn.iocoder.yudao.module.pms.dal.dataobject.grmain.*;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.module.pms.dal.dataobject.wrstradedetail2.WrsTradeDetail2DO;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;

import java.util.List;
import java.util.Map;

/**
 * 物料验收 Service 接口
 *
 * <AUTHOR>
 */
public interface GrMainService {

    /**
     * 创建物料验收
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createGrMain(@Valid GrMainSaveReqVO createReqVO);

    /**
     * 更新物料验收
     *
     * @param updateReqVO 更新信息
     */
    void updateGrMain(@Valid GrMainSaveReqVO updateReqVO);

    void updateChecker(PoMainCheckSaveReqDTO updateReqVO);

    void updateInspEmpl(PoMainInspSaveReqDTO reqDTO);

    List<GrMainDO> getGrMainByVendor(PoMainReqDTO reqDTO);

    /**
     * 删除物料验收
     *
     * @param id 编号
     */
    void deleteGrMain(Long id);

    GrMainDO selectByInspno(String inspno);

    /**
     * 获得物料验收
     *
     * @param id 编号
     * @return 物料验收
     */
    GrMainDO getGrMain(Long id);

    GrMainRespVO getGrMain2(Long id);

    /**
     * 获得物料验收分页
     *
     * @param pageReqVO 分页查询
     * @return 物料验收分页
     */
    PageResult<GrMainRespVO> getGrMainPage(GrMainPageReqVO pageReqVO);

    PageResult<GrMainRespVO> getAppPage(GrMainPageReqVO pageReqVO);

    // ==================== 子表（物料验收明细） ====================

    /**
     * 获得物料验收明细分页
     *
     * @param pageReqVO 分页查询
     * @param parentId  父节点编号
     * @return 物料验收明细分页
     */
    PageResult<GrDetailDO> getGrDetailPage(PageParam pageReqVO, Long parentId);

    //验收完成修改主表
    void updateMainStatus(Long parentId);

    /**
     * 创建物料验收明细
     *
     * @param grDetail 创建信息
     * @return 编号
     */
    Long createGrDetail(@Valid GrDetailDO grDetail);

    /**
     * 更新物料验收明细
     *
     * @param grDetail 更新信息
     */
    void updateGrDetail(@Valid GrDetailDO grDetail);

    /**
     * 删除物料验收明细
     *
     * @param id 编号
     */
    void deleteGrDetail(Long id);

    /**
     * 获得物料验收明细
     *
     * @param id 编号
     * @return 物料验收明细
     */
    GrDetailRespVO getGrDetail(Long id);

    void confirmGrMain(Long id);

    void cancelConfirmGrMain(Long id);

    List<AdminUserRespDTO> getUserListByPostIds(GetEmplByPostReqDTO reqDTO);

    // ==================== 子表（原料结算明细） ====================

    /**
     * 获得原料结算明细分页
     *
     * @param pageReqVO 分页查询
     * @param parentId  父节点编号
     * @return 原料结算明细分页
     */
    PageResult<PpDetail1DO> getPpDetail1Page(PageParam pageReqVO, Long parentId);

    /**
     * 创建原料结算明细
     *
     * @param ppDetail1 创建信息
     * @return 编号
     */
    Long createPpDetail1(@Valid PpDetail1DO ppDetail1);

    /**
     * 更新原料结算明细
     *
     * @param ppDetail1 更新信息
     */
    void updatePpDetail1(@Valid PpDetail1DO ppDetail1);

    /**
     * 删除原料结算明细
     *
     * @param id 编号
     */
    void deletePpDetail1(Long id);

    /**
     * 获得原料结算明细
     *
     * @param id 编号
     * @return 原料结算明细
     */
    PpDetail1DO getPpDetail1(Long id);

    // ==================== 子表（结算金额调整） ====================

    /**
     * 获得结算金额调整分页
     *
     * @param pageReqVO 分页查询
     * @param parentId  父节点编号
     * @return 结算金额调整分页
     */
    PageResult<PpDetail2DO> getPpDetail2Page(PageParam pageReqVO, Long parentId);

    /**
     * 创建结算金额调整
     *
     * @param ppDetail2 创建信息
     * @return 编号
     */
    Long createPpDetail2(@Valid PpDetail2DO ppDetail2);

    /**
     * 更新结算金额调整
     *
     * @param ppDetail2 更新信息
     */
    void updatePpDetail2(@Valid PpDetail2DO ppDetail2);

    /**
     * 删除结算金额调整
     *
     * @param id 编号
     */
    void deletePpDetail2(Long id);

    /**
     * 获得结算金额调整
     *
     * @param id 编号
     * @return 结算金额调整
     */
    PpDetail2DO getPpDetail2(Long id);

    // ==================== 子表（总结算） ====================

    /**
     * 获得总结算分页
     *
     * @param pageReqVO 分页查询
     * @param parentId  父节点编号
     * @return 总结算分页
     */
    PageResult<PpDetail3DO> getPpDetail3Page(PageParam pageReqVO, Long parentId);

    /**
     * 创建总结算
     *
     * @param ppDetail3 创建信息
     * @return 编号
     */
    Long createPpDetail3(@Valid PpDetail3DO ppDetail3);

    /**
     * 更新总结算
     *
     * @param ppDetail3 更新信息
     */
    void updatePpDetail3(@Valid PpDetail3DO ppDetail3);

    /**
     * 删除总结算
     *
     * @param id 编号
     */
    void deletePpDetail3(Long id);

    /**
     * 获得总结算
     *
     * @param id 编号
     * @return 总结算
     */
    PpDetail3DO getPpDetail3(Long id);

    void batchInsertTradeList(ppDetail1BatchImportVO reqVO);

    void calSettle(Long id);

    void continueGrMain(Long id, String stus);

    /** 检查明细——动作 */
    void checkGrDetail(AppGrDetailCheckSaveVO checkSaveVO);

    /**
     * 根据明细ID获得明细相关的文件关联记录
     */
    List<AppGrDetailFileRelationVO> selectRelationListByDetailId(Long detailId);

    Map<Long,Long> countDetailByGrMainId(List<Long> grMainIds);

    boolean confirmCheckGrMain(Long id);

    PageResult<GrMainRespVO> getGrMainMPage(GrMainPageReqVO pageReqVO);

    String batchSplitCase(GrDetailSplitSaveReqVO reqVO);

    PageResult<GrMainQueryRespVO> getGrMainPageQuery(GrMainQueryReqVO pageReqVO);

    void batchInsertGrDetails(GrDetailBatchInsertReqVO reqVO);

    void batchSettleInsert(GrDetailSplitSaveReqVO reqVO);

    Long createSettlementDetail(SettlementDetailDO settlementDetailDO);

    void updateSettlementDetail(SettlementDetailDO updateReqVO);

    void deleteSettlementDetail(Long id);

    SettlementDetailDO getSettlementDetail(Long id);

    PageResult<SettlementDetailDO> getSettlementDetailPage(PageParam pageReqVO, Long parentId);

    void downloadSettlementDetail(Long parentId);

    List<SettlementDetailDO> getTotalList(Long parentId);
}

package cn.iocoder.yudao.module.pms.dal.dataobject.inspshipmain;

import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 船运验收入储主档 DO
 *
 * <AUTHOR>
 */
@TableName("pms_insp_ship_main")
@KeySequence("pms_insp_ship_main_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InspShipMainDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 项目号
     */
    private String project;
    /**
     * 公司别
     */
    private String compId;
    /**
     * 检验批号
     */
    private String chkNo;
    /**
     * 入储日期
     */
    private String inStgDate;
    /**
     * 重量验收状态
     */
    private String isMasAcpt;
    /**
     * 原料料号
     */
    private String matrlNo;
    /**
     * 品名
     */
    private String matrlName;
    /**
     * 合同号
     */
    private String contractNo;
    /**
     * 供应商代码
     */
    private String supplierNo;
    /**
     * 供应商
     */
    private String supplyName;
    /**
     * 合同数量
     */
    private BigDecimal ctrtNum;
    /**
     * 合同单价
     */
    private BigDecimal ctrtUnit;
    /**
     * 合同水
     */
    private BigDecimal ctrtWater;
    /**
     * 交运条件
     */
    private String delvType;
    /**
     * 合同种类
     */
    private String ctrtType;
    /**
     * 船运公司
     */
    private String shipCom;
    /**
     * 船名/航次
     */
    private String shipName;
    /**
     * 船运合同
     */
    private String shipCtrt;
    /**
     * 船运类别
     */
    private String shipType;
    /**
     * 装运港名称
     */
    private String loadPort;
    /**
     * 装运港名称
     */
    private String loadPortOut;
    /**
     * 预计装载港离港日期
     */
    private String exptLdPtLvDate;
    /**
     * 实际装载港离港日期
     */
    private String actlLdPtLvDate;
    /**
     * 卸港码头名称
     */
    private String relsPort;
    /**
     * 预计到达日期
     */
    private String exptArivDate;
    /**
     * 实际到达日期
     */
    private String actlArivDate;
    /**
     * 卸空日期时间
     */
    private String finishJobDate;
    /**
     * 卸空时间
     */
    private String finishJobTime;
    /**
     * 卸船系统
     */
    private String dumpNo;
    /**
     * 下抛计量和质检的状态
     */
    private String isSend;
    /**
     * 秤量室
     */
    private String scaleRoom;
    /**
     * 水量基准
     */
    private BigDecimal waterBase;
    /**
     * 当前层数
     */
    private String lwCurrent;
    /**
     * 分层状态
     */
    private String lwStus;
    /**
     * 商检局名
     */
    private String tradeAgent;
    /**
     * 验收重量依据
     */
    private String masAcptBase;
    /**
     * 化验室
     */
    private String testRoom;
    /**
     * 化验水
     */
    private BigDecimal chkWater;
    /**
     * 装港数量[湿]
     */
    private BigDecimal loadNum;
    /**
     * 储存损耗[干]
     */
    private BigDecimal impurityNum;
    /**
     * 途耗-移出途耗库[干]
     */
    private BigDecimal impurityOutNum;
    /**
     * 途耗-移入途耗库[干]
     */
    private BigDecimal impurityDryNum;
    /**
     * 移出量[干]
     */
    private BigDecimal issueNum;
    /**
     * 报港数量[湿]
     */
    private BigDecimal dumpSettleNum;
    /**
     * 商检湿重[湿]
     */
    private BigDecimal scaleWetNum;
    /**
     * 商检干重[干]
     */
    private BigDecimal scaleDryNum;
    /**
     * 入储数量
     */
    private BigDecimal transNum;
    /**
     * 入储储位
     */
    private String stgNo;
    /**
     * 车数
     */
    private BigDecimal carNum;
    /**
     * 运杂费暂估单价
     */
    private BigDecimal blendUnitAmt;
    /**
     * 途耗金额
     */
    private BigDecimal blendAmt;
    /**
     * 验收数量
     */
    private BigDecimal settleNum;
    /**
     * 结算金额
     */
    private BigDecimal settleAmt;
    /**
     * MP结算金额
     */
    private BigDecimal mpSettleAmt;
    /**
     * MP结算数量
     */
    private BigDecimal mpSettleNum;
    /**
     * 入帐日期
     */
    private String vchrDate;
    /**
     * 申请单号
     */
    private String issueTallyNo;
    /**
     * 水运类别
     */
    private String shipReportType;
    /**
     * 关联批号
     */
    private String extraChkNo;
    /**
     * 签证地(废钢)
     */
    private String visaLoc;
    /**
     * 运输公司编码(船转车)
     */
    private String carTeamNo;
    /**
     * 运输公司说明
     */
    private String carTeamDesc;
    /**
     * 计划起运日期
     */
    private String transSDate;
    /**
     * 计划完成日期
     */
    private String transEDate;
    /**
     * 订购性质
     */
    private String poType;
    /**
     * 合同版次
     */
    private String poVer;
    /**
     * GPS运输计划
     */
    private String planListNo;
    /**
     * 备注
     */
    private String remark;
    /**
     * 关单号
     */
    private String customsNo;
    /**
     * 是否缴税
     */
    private String isCustoms;
    /**
     * 缴税日期
     */
    private String customsDate;
    /**
     * 缴税金额
     */
    private BigDecimal customsAmt;
    /**
     * 结关日期起
     */
    private String customsEndSDate;
    /**
     * 结关日期讫
     */
    private String customsEndEDate;
    /**
     * 创建人
     */
    private String createEmpNo;
    /**
     * 创建日期
     */
    private String createDate;
    /**
     * 更新人A
     */
    private String updateEmpNoA;
    /**
     * 更新日期A
     */
    private String updateDateA;
    /**
     * 更新人B
     */
    private String updateEmpNoB;
    /**
     * 更新日期B
     */
    private String updateDateB;
    /**
     * 更新人
     */
    private String updateEmpNo;
    /**
     * 更新日期
     */
    private String updateDate;

    /**
     * 状态
     */
    private String stus;

    /**
     * 附件
     */
    private String annex;

}
package cn.iocoder.yudao.module.pms.dal.mysql.mpp;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.pms.controller.admin.mpp.vo.ProcurementCollectionPageReqVO;
import cn.iocoder.yudao.module.pms.dal.dataobject.mpp.ProcurementCollectionDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 集采信息主表 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProcurementCollectionMapper extends BaseMapperX<ProcurementCollectionDO> {

    default PageResult<ProcurementCollectionDO> selectPage(ProcurementCollectionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProcurementCollectionDO>()
                .likeIfPresent(ProcurementCollectionDO::getCollectionNo, reqVO.getCollectionNo())
                .eqIfPresent(ProcurementCollectionDO::getCompanyCode, reqVO.getCompanyCode())
                .eqIfPresent(ProcurementCollectionDO::getEffectiveStatus, reqVO.getEffectiveStatus())
                .betweenIfPresent(ProcurementCollectionDO::getCreateDate, reqVO.getCreateDate())
                .betweenIfPresent(ProcurementCollectionDO::getEffectiveDate, reqVO.getEffectiveDate())
                .orderByDesc(ProcurementCollectionDO::getId));
    }

    default ProcurementCollectionDO selectByCollectionNo(String collectionNo) {
        return selectOne(ProcurementCollectionDO::getCollectionNo, collectionNo);
    }
}

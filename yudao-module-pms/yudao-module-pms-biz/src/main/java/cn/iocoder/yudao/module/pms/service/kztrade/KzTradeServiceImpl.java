package cn.iocoder.yudao.module.pms.service.kztrade;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.module.pms.controller.admin.grmain.vo.CalculateRuleReqVO;
import cn.iocoder.yudao.module.pms.controller.admin.mpp.vo.MppDetailImportRespVO;
import cn.iocoder.yudao.module.pms.controller.admin.wmstrade.vo.WmsTradeParamReqVO;
import cn.iocoder.yudao.module.pms.dal.dataobject.mpp.MppDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.mpp.MppDetailDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.stock.WmsStockDO;
import cn.iocoder.yudao.module.pms.dal.mysql.stock.WmsStockMapper;
import cn.iocoder.yudao.module.pms.service.wmstrade.WmsTradeServiceImpl;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import com.alibaba.fastjson.annotation.JSONField;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import cn.iocoder.yudao.module.pms.controller.admin.kztrade.vo.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.kztrade.KzTradeDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.kztrade.KzTradeDetailDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.pms.dal.mysql.kztrade.KzTradeMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.kztrade.KzTradeDetailMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.*;
import static cn.iocoder.yudao.module.pms.enums.ErrorCodeConstants.*;

/**
 * 期初开账 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class KzTradeServiceImpl implements KzTradeService {

    @Resource
    private KzTradeMapper kzTradeMapper;
    @Resource
    private KzTradeDetailMapper kzTradeDetailMapper;
    @Resource
    private WmsStockMapper wmsStockMapper;
    @Resource
    private WmsTradeServiceImpl wmsTradeService;
    private static final String confirmSuccess="Y";
    private static final String confirmBefore="N";
    private static final String yMdIncludeGang="yyyy-MM-dd";
    private static final String yMdExcludeGang="yyyyMMdd";
    @Override
    public Long createKzTrade(KzTradeSaveReqVO createReqVO) {
        // 插入
        SimpleDateFormat sdf=new SimpleDateFormat(yMdIncludeGang);
        DateTimeFormatter formatter=DateTimeFormatter.ofPattern(yMdIncludeGang);
        KzTradeDO kzTrade = BeanUtils.toBean(createReqVO, KzTradeDO.class);
        kzTrade.setAppId("kztrade");
        kzTrade.setCreateEmpNo(getLoginUserId().toString());
        kzTrade.setProject(getLoginUserTopDeptId().toString());
        kzTrade.setCompId(getLoginUserTenantId().toString());
        kzTrade.setCreator(getLoginUserNickname());
        kzTrade.setCreateDate(sdf.format(new Date()));
        //申请人联系电话没有
        kzTrade.setIssueEmpNo(getLoginUserNickname());
        LocalDateTime currentTime = LocalDateTime.now();
        String currentTimeFormat=currentTime.format(formatter);
        kzTrade.setIssueDate(LocalDate.parse(currentTimeFormat,formatter));//有问题
        kzTrade.setIssueTranEmpNo(getLoginUserId().toString());
        kzTrade.setIssueTranDeptNo(getLoginUserDeptName());
        kzTrade.setPurposeId("00");
        kzTrade.setUpdater("");
        kzTrade.setStus(confirmBefore);
        kzTradeMapper.insert(kzTrade);
        // 返回
        return kzTrade.getId();
    }

    @Override
    public void updateKzTrade(KzTradeSaveReqVO updateReqVO) {
        // 校验存在
        validateKzTradeExists(updateReqVO.getId());
        // 更新
        SimpleDateFormat sdf=new SimpleDateFormat(yMdIncludeGang);
        KzTradeDO updateObj = BeanUtils.toBean(updateReqVO, KzTradeDO.class);
        updateObj.setUpdateEmpNo(getLoginUserId().toString());
        updateObj.setUpdater(getLoginUserNickname());
        updateObj.setUpdateDate(sdf.format(new Date()));
        kzTradeMapper.updateById(updateObj);
    }
    @Override
    @Transactional//将此函数变成事务
    public void updateKzTradeStus(KzTradeSaveReqVO updateReqVO) {
        // 校验存在
        validateKzTradeExists(updateReqVO.getId());
        // 更新
        SimpleDateFormat sdf=new SimpleDateFormat(yMdIncludeGang);
        KzTradeDO updateObj = BeanUtils.toBean(updateReqVO, KzTradeDO.class);
        updateObj.setUpdateEmpNo(getLoginUserId().toString());
        updateObj.setUpdater(getLoginUserNickname());
        updateObj.setUpdateDate(sdf.format(new Date()));
        List<KzTradeDetailDO> kzTradeDetailDOS=kzTradeDetailMapper.selectByParentId(updateObj.getId());
        for(int i=0;i<kzTradeDetailDOS.size();i++) {
            if(kzTradeDetailDOS.get(i).getStus().equals(confirmBefore)){
                WmsTradeParamReqVO reqVO = new WmsTradeParamReqVO();
                reqVO.setProject(kzTradeDetailDOS.get(i).getProject());
                reqVO.setCompid(kzTradeDetailDOS.get(i).getCompid());
                reqVO.setPono(kzTradeDetailDOS.get(i).getContractno());
                reqVO.setBatch(kzTradeDetailDOS.get(i).getLotno());
                reqVO.setMatrlno(kzTradeDetailDOS.get(i).getMatrlno());
                reqVO.setLocno(kzTradeDetailDOS.get(i).getLocno());
                reqVO.setQty(kzTradeDetailDOS.get(i).getIssueqty());
                reqVO.setAmt(kzTradeDetailDOS.get(i).getIssueamt());
                reqVO.setVchrdate("20250116");
                reqVO.setPurposeid("00");
                reqVO.setSeqno(String.valueOf(1));
                wmsTradeService.updateStock(reqVO);
                kzTradeDetailDOS.get(i).setStus(confirmSuccess);
                kzTradeDetailDOS.get(i).getCreateDate().replace("-","");
                kzTradeDetailMapper.updateById(kzTradeDetailDOS.get(i));
            }
        }
        updateObj.setStus(confirmSuccess);
        kzTradeMapper.updateById(updateObj);
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteKzTrade(Long id) {
        // 校验存在
        validateKzTradeExists(id);
        // 删除
        kzTradeMapper.deleteById(id);

        // 删除子表
        deleteKzTradeDetailByParentId(id);
    }

    private void validateKzTradeExists(Long id) {
        if (kzTradeMapper.selectById(id) == null) {
            throw exception(KZ_TRADE_NOT_EXISTS);
        }
    }
    private void validateWmsStockExists(Long id) {
        if (wmsStockMapper.selectById(id) == null) {
            throw exception(WMS_STOCK_NOT_EXISTS);
        }
    }
    @Override
    public KzTradeDO getKzTrade(Long id) {
        return kzTradeMapper.selectById(id);
    }

    @Override
    public PageResult<KzTradeDO> getKzTradePage(KzTradePageReqVO pageReqVO) {
        //PageResult<KzTradeDO> pageResult=kzTradeMapper.selectPage(pageReqVO);

        return kzTradeMapper.selectPage(pageReqVO);
    }

    // ==================== 子表（开账管理明细） ====================

    @Override
    public PageResult<KzTradeDetailDO> getKzTradeDetailPage(PageParam pageReqVO, Long parentId) {
        PageResult<KzTradeDetailDO> pageResult=kzTradeDetailMapper.selectPage(pageReqVO, parentId);
        for(int i=0;i<pageResult.getTotal();i++){
            SimpleDateFormat inputFormat= new SimpleDateFormat(yMdExcludeGang);
            SimpleDateFormat outFormat= new SimpleDateFormat(yMdIncludeGang);
            String createDate="";
            String updateDate="";
            try {
                createDate = outFormat.format(inputFormat.parse(pageResult.getList().get(i).getCreateDate()));
                pageResult.getList().get(i).setCreateDate(createDate);
                if(pageResult.getList().get(i).getUpdateDate()!=null) {
                    updateDate = outFormat.format(inputFormat.parse(pageResult.getList().get(i).getUpdateDate()));
                    pageResult.getList().get(i).setUpdateDate(updateDate);
                }
            }catch (ParseException e){
                e.printStackTrace();
                return null;
            }
        }
        return pageResult;
    }

    @Override
    public Long createKzTradeDetail(KzTradeDetailDO kzTradeDetail) {
        // 校验是否已经存在
//        if (kzTradeDetailMapper.selectByParentId(kzTradeDetail.getParentId()) != null) {
//            throw exception(KZ_TRADE_DETAIL_EXISTS);
//        }
        // 插入
        SimpleDateFormat sdf=new SimpleDateFormat(yMdExcludeGang);
        kzTradeDetail.setCreateEmpno(getLoginUserId().toString());
        kzTradeDetail.setCreator(getLoginUserNickname());
        kzTradeDetail.setCreateDate(sdf.format(new Date()));
        kzTradeDetail.setStus(confirmBefore);
        KzTradeDO kzTradeDO=kzTradeMapper.selectById(kzTradeDetail.getParentId());
        kzTradeDetail.setProject(kzTradeDO.getProject());
        kzTradeDetail.setCompid(kzTradeDO.getCompId());
        //根据料号入库260101010011 101
//        HashMap map=new HashMap();
//        map.put("project",kzTradeDetail.getProject());
//        map.put("matrlno",kzTradeDetail.getMatrlno());
//        WmsStockDO wmsStockDO=wmsStockMapper.selectStockByKz(map);
//        if(wmsStockDO!=null) {
//            BigDecimal sumInventoryLevel = wmsStockDO.getEndqty().add(kzTradeDetail.getIssueqty());
//            BigDecimal sumMoney = wmsStockDO.getEndamt().add(kzTradeDetail.getIssueamt());
//            wmsStockDO.setEndqty(sumInventoryLevel).setEndamt(sumMoney);
//            //更新仓储信息
//            wmsStockMapper.updateById(wmsStockDO);
//            //插入开账明细
//            kzTradeDetailMapper.insert(kzTradeDetail);
//        }else{
//            throw exception(WMS_STOCK_NOT_EXISTS);
//        }
        kzTradeDetailMapper.insert(kzTradeDetail);
        return kzTradeDetail.getId();
    }

    @Override
    public void updateKzTradeDetail(KzTradeDetailDO kzTradeDetail) {
        // 校验存在
        validateKzTradeDetailExists(kzTradeDetail.getId());
        // 更新
        SimpleDateFormat sdf=new SimpleDateFormat(yMdExcludeGang);
        kzTradeDetail.setUpdateEmpno(getLoginUserId().toString());
        kzTradeDetail.setUpdater(getLoginUserNickname());
        kzTradeDetail.setUpdateDate(sdf.format(new Date()));
        kzTradeDetailMapper.updateById(kzTradeDetail);
    }
    @Override
    @Transactional
    public void updateKzTradeDetailConfirm(KzTradeDetailDO kzTradeDetail) {
        // 校验存在
        validateKzTradeDetailExists(kzTradeDetail.getId());
        // 更新
        SimpleDateFormat sdf=new SimpleDateFormat(yMdExcludeGang);
        kzTradeDetail.setUpdateEmpno(getLoginUserId().toString());
        kzTradeDetail.setUpdater(getLoginUserNickname());
        kzTradeDetail.setUpdateDate(sdf.format(new Date()));
        WmsTradeParamReqVO reqVO = new WmsTradeParamReqVO();
        reqVO.setProject(kzTradeDetail.getProject());
        reqVO.setCompid(kzTradeDetail.getCompid());
        reqVO.setPono(kzTradeDetail.getContractno());
        reqVO.setBatch(kzTradeDetail.getLotno());
        reqVO.setMatrlno(kzTradeDetail.getMatrlno());
        reqVO.setLocno(kzTradeDetail.getLocno());
        reqVO.setQty(kzTradeDetail.getIssueqty());
        reqVO.setAmt(kzTradeDetail.getIssueamt());
        reqVO.setVchrdate("20250116");
        reqVO.setPurposeid("00");
        reqVO.setSeqno(String.valueOf(1));
        wmsTradeService.updateStock(reqVO);
        kzTradeDetail.setStus(confirmSuccess);
        kzTradeDetail.getCreateDate().replace("-","");
        kzTradeDetailMapper.updateById(kzTradeDetail);
    }
    @Override
    public void deleteKzTradeDetail(Long id) {
        // 校验存在
        validateKzTradeDetailExists(id);
        //删除仓储信息
//        KzTradeDetailDO kzTradeDetail=kzTradeDetailMapper.selectById(id);
//        HashMap map=new HashMap();
//        map.put("project",kzTradeDetail.getProject());
//        map.put("matrlno",kzTradeDetail.getMatrlno());
//        WmsStockDO wmsStockDO=wmsStockMapper.selectStockByKz(map);
//        if(wmsStockDO!=null) {
//            BigDecimal subtractInventoryLevel = wmsStockDO.getEndqty().subtract(kzTradeDetail.getIssueqty());
//            BigDecimal subtractMoney = wmsStockDO.getEndamt().subtract(kzTradeDetail.getIssueamt());
//            wmsStockDO.setEndqty(subtractInventoryLevel).setEndamt(subtractMoney);
//            //更新仓储信息
//            wmsStockMapper.updateById(wmsStockDO);
//            // 删除
//            kzTradeDetailMapper.deleteById(id);
//        }else{
//            throw exception(WMS_STOCK_NOT_EXISTS);
//        }
        kzTradeDetailMapper.deleteById(id);
    }

    @Override
    public KzTradeDetailDO getKzTradeDetail(Long id) {
        return kzTradeDetailMapper.selectById(id);
    }



    private void validateKzTradeDetailExists(Long id) {
        if (kzTradeDetailMapper.selectById(id) == null) {
            throw exception(KZ_TRADE_DETAIL_NOT_EXISTS);
        }
    }

    private void deleteKzTradeDetailByParentId(Long parentId) {
        kzTradeDetailMapper.deleteByParentId(parentId);
    }
    @Override
    public KzTradeDetailImportRespVO importKzTradeDetailList(List<KzTradeDetailImportVO> list, Long parentId, Boolean updateSupport) {
        if (CollUtil.isEmpty(list)) {
            throw exception(KZ_TRADE_IMPORT_LIST_IS_EMPTY);
        }
        KzTradeDO kzTradeDO = kzTradeMapper.selectById(parentId);
        if(kzTradeDO==null){
            throw new RuntimeException("期初开账不存在！");
        }
        List<KzTradeDetailDO> kzTradeDetailDOS = kzTradeDetailMapper.selectList(KzTradeDetailDO::getParentId, parentId);
        // 2. 遍历，逐个创建 or 更新
        KzTradeDetailImportRespVO respVO = KzTradeDetailImportRespVO.builder()
                .createDetails(new ArrayList<>()).updateDetails(new ArrayList<>())
                .failureDetails(new LinkedHashMap<>()).build();
        list.forEach(detail -> {
            KzTradeDetailDO  kzTradeDetail = BeanUtils.toBean(detail, KzTradeDetailDO.class);
            kzTradeDetail.setParentId(parentId);
            Long id = 0L;
            try {
                if(updateSupport){
                    Optional<KzTradeDetailDO> first = kzTradeDetailDOS.stream().filter(m -> m.getMatrlno().equals(kzTradeDetail.getMatrlno())).findFirst();
                    if (first.isPresent()) {
                        kzTradeDetail.setId(first.get().getId());
                        updateKzTradeDetail(kzTradeDetail);
                        respVO.getUpdateDetails().add(kzTradeDetail.getMatrlno());
                        return;
                    }
                }
                createKzTradeDetail(kzTradeDetail);
                respVO.getCreateDetails().add(kzTradeDetail.getMatrlno());
            } catch (Exception e){
                respVO.getFailureDetails().put(kzTradeDetail.getMatrlno(),e.getMessage());
            }
        });
        return respVO;
    }
}
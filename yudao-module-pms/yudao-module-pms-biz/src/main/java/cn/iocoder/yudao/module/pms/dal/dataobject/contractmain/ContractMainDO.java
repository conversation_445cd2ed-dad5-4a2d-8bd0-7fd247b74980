package cn.iocoder.yudao.module.pms.dal.dataobject.contractmain;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 法务合同基本信息 DO
 *
 * <AUTHOR>
 */
@TableName("FW_CONTRACT_MAIN")
@KeySequence("fw_contract_main_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContractMainDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 合同名称
     */
    private String contractName;
    /**
     * 业务系统合同编码
     */
    private String thirdContractNo;
    /**
     * 业务系统版本号
     */
    private String thirdVersion;
    /**
     * 系统来源
     */
    private String dataSource;
    /**
     * 系统来源编码
     */
    private String dataSourceCode;
    /**
     * 法务系统合同唯一标识
     */
    private String contractId;
    /**
     * 法务系统合同唯一标识名称
     */
    private String contractIdName;
    /**
     * 合同性质编码
     */
    private Integer dataTypeCode;
    /**
     * 合同分类编码
     */
    private String contractTypeCode;
    /**
     * 经办人
     */
    private String orgId;
    /**
     * 立项决策编码
     */
    private Integer projectDecisionId;
    /**
     * 相对方确定方式编码
     */
    private Integer relativeMethodCode;
    /**
     * 授权类型编码
     */
    private Integer authorizedSource;
    /**
     * 授权名称
     */
    private String authorizedName;
    /**
     * 授权编码
     */
    private String authorizedId;
    /**
     * 是否集团重大合同编码(0-否,1-是)
     */
    private Integer whetherGroupMajor;
    /**
     * 是否本单位重大合同编码(0-否,1-是)
     */
    private Integer whetherUnitMajor;
    /**
     * 金额类型编码
     */
    private Integer moneyTypeCode;
    /**
     * 是否含税(0-否,1-是)
     */
    private Integer isTax;
    /**
     * 结算方式编码
     */
    private String settlementMethodCode;
    /**
     * 收支方向编码
     */
    private Integer revenueExpenditureCode;
    /**
     * 合同约定开始日期(YYYY-MM-DD HH:MM:SS)
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime agreedStartTime;
    /**
     * 合同约定结束日期(YYYY-MM-DD HH:MM:SS)
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime agreedEndTime;
    /**
     * 我方地位
     */
    private String ourPosition;
    /**
     * 我方签约主体
     */
    private String ourPartyName;
    /**
     * 我方签约主体编码
     */
    private String ourPartyList;
    /**
     * 对方签约主体
     */
    private String otherPartyName;
    /**
     * 对方签约主体编码
     */
    private String otherPartyList;
    /**
     * 是否范本(0-否,1-是)
     */
    private Integer isItAtemplate;
    /**
     * 合同金额
     */
    private BigDecimal contractMoney;
    /**
     * 增值税率编码(可多选逗号隔开)
     */
    private String valueAddedTaxRateCode;
    /**
     * 增值税率(可多选逗号隔开)
     */
    private String valueAddedTaxRate;
    /**
     * 增值税额(元)
     */
    private BigDecimal valueAddedTaxAmount;
    /**
     * 币种名称(主数据分发)
     */
    private String currency;
    /**
     * 币种代码(主数据分发)
     */
    private String currencyCode;
    /**
     * 汇率方式编码(见附表)
     */
    private Integer exchangeRateMethodCode;
    /**
     * 汇率
     */
    private BigDecimal exchangeRate;
    /**
     * 原合同编号
     */
    private String originalContractCode;
    /**
     * 金额变更类型编码(见附表)
     */
    private Integer changeMoneyTypeCode;
    /**
     * 本次变更合同金额
     */
    private BigDecimal thisChangeMoney;
    /**
     * 本次变更增值税金额
     */
    private BigDecimal thisChangeValueAddedTaxAmount;

    //生效状态编码	takeEffectCode
    private Integer takeEffectCode;
    //生效时间（日期格式：yyyy-MM-dd HH:mm:ss）	contractTakeEffectDate
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime contractTakeEffectDate;
    //对方盖章时间（日期格式：yyyy-MM-dd HH:mm:ss）	otherSealDate
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime otherSealDate;
    //合同履行状态编码	performStateCode
    private Integer performStateCode;
    //待用印附件名称	staySealFile
    private String staySealFile;
    //待用印附件名称编码	staySealFileId
    private String staySealFileId;
    //生效附件名称	alreadySealFile
    private String alreadySealFile;
    //生效附件名称编码	alreadySealFileId
    private String alreadySealFileId;
    /**
     * 预留扩展字段1
     */
    private String extendedFiled1;
    /**
     * 预留扩展字段2
     */
    private String extendedFiled2;
    /**
     * 预留扩展字段3
     */
    private String extendedFiled3;
    /**
     * 预留扩展字段4
     */
    private String extendedFiled4;
    /**
     * 预留扩展字段5
     */
    private String extendedFiled5;
    /**
     * 预留扩展字段6
     */
    private String extendedFiled6;
    /**
     * 预留扩展字段7
     */
    private String extendedFiled7;
    /**
     * 预留扩展字段8
     */
    private String extendedFiled8;
    /**
     * 预留扩展字段9
     */
    private String extendedFiled9;
    /**
     * 预留扩展字段10
     */
    private String extendedFiled10;
    /**
     * 预留扩展字段11
     */
    private String extendedFiled11;
    /**
     * 预留扩展字段12
     */
    private String extendedFiled12;
    /**
     * 预留扩展字段13
     */
    private String extendedFiled13;
    /**
     * 预留扩展字段14
     */
    private String extendedFiled14;
    /**
     * 预留扩展字段15
     */
    private String extendedFiled15;
    /**
     * 预留扩展字段16
     */
    private String extendedFiled16;
    /**
     * 预留扩展字段17
     */
    private String extendedFiled17;
    /**
     * 预留扩展字段18
     */
    private String extendedFiled18;
    /**
     * 预留扩展字段19
     */
    private String extendedFiled19;
    /**
     * 预留扩展字段20
     */
    private String extendedFiled20;
    /**
     * 附件地址
     */
    private String pdfUrl;
    /**
     * 初始版本合同ID
     */
    private String poidfirst;
    /**
     * 备注
     */
    private String remark;

}

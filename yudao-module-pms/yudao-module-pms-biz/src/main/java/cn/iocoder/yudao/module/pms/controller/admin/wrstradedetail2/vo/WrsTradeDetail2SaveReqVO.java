package cn.iocoder.yudao.module.pms.controller.admin.wrstradedetail2.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 原料待结算明细新增/修改 Request VO")
@Data
public class WrsTradeDetail2SaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "项目号")
    private String project;

    @Schema(description = "公司别")
    private String compId;

    @Schema(description = "申请单号")
    private String issueTallyNo;

    @Schema(description = "流水序号")
    private String seqNo;

    @Schema(description = "检验批号")
    private String chkNo;

    @Schema(description = "交易种类")
    private String issueType;

    @Schema(description = "手工检验批号")
    private String manualChkNo;

    @Schema(description = "供应商代码")
    private String supplierNo;

    @Schema(description = "合同编号")
    private String contractNo;

    @Schema(description = "原料料号")
    private String matrlNo;

    @Schema(description = "合同单价")
    private BigDecimal ctrtUnit;

    @Schema(description = "合同水")
    private BigDecimal ctrtWater;

    @Schema(description = "化验水")
    private BigDecimal chkWater;

    @Schema(description = "合同版次")
    private String pover;

    @Schema(description = "储位代码")
    private String stgNo;

    @Schema(description = "取得合同单价日期")
    private String contractDate;

    @Schema(description = "结案人")
    private String finalEmpNo;

    @Schema(description = "结案日期")
    private String finalDate;

    @Schema(description = "结算人")
    private String settleEmpNo;

    @Schema(description = "结算日期")
    private String settleDate;

    @Schema(description = "发货数量")
    private BigDecimal loadNum;

    @Schema(description = "检斤数量")
    private BigDecimal scaleNum;

    @Schema(description = "计量上抛数量")
    private BigDecimal scalediNum;

    @Schema(description = "扣杂质")
    private BigDecimal impurityWeight;

    @Schema(description = "交易数量")
    private BigDecimal transNum;

    @Schema(description = "交易金额")
    private BigDecimal transAmt;

    @Schema(description = "结算用数量")
    private BigDecimal settleNum;

    @Schema(description = "结算用金额")
    private BigDecimal settleAmt;

    @Schema(description = "MP结算数量")
    private BigDecimal mpSettleNum;

    @Schema(description = "MP结算金额")
    private BigDecimal mpSettleAmt;

    @Schema(description = "运杂费金额")
    private BigDecimal blendAmt;

    @Schema(description = "磅单号")
    private String wgtListNo;

    @Schema(description = "结算单号")
    private String tranTallyNo;

    @Schema(description = "检验样")
    private String qtyAcptType;

    @Schema(description = "发站")
    private String sendStation;

    @Schema(description = "车船号")
    private String carNo;

    @Schema(description = "结算用车船号")
    private String settleCarNo;

    @Schema(description = "报支单号")
    private String billNo;

    @Schema(description = "报支日期")
    private String billDate;

    @Schema(description = "交易状态")
    private String stus;

    @Schema(description = "建立人")
    private String createEmpNo;

    @Schema(description = "建立日期")
    private String createDate;

    @Schema(description = "修改人")
    private String updateEmpNo;

    @Schema(description = "修改日期")
    private String updateDate;

    @Schema(description = "备注")
    private String remark;

}
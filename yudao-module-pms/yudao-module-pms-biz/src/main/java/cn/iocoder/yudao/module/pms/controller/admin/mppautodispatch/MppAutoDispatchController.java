package cn.iocoder.yudao.module.pms.controller.admin.mppautodispatch;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.pms.controller.admin.mppautodispatch.vo.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.mppautodispatch.MppAutoDispatchDO;
import cn.iocoder.yudao.module.pms.service.mppautodispatch.MppAutoDispatchService;

@Tag(name = "管理后台 - 采购计划自动调度规则")
@RestController
@RequestMapping("/pms/mpp-auto-dispatch")
@Validated
public class MppAutoDispatchController {

    @Resource
    private MppAutoDispatchService mppAutoDispatchService;

    @PostMapping("/create")
    @Operation(summary = "创建采购计划自动调度规则")
    @PreAuthorize("@ss.hasPermission('pms:mpp-auto-dispatch:create')")
    public CommonResult<Long> createMppAutoDispatch(@Valid @RequestBody MppAutoDispatchSaveReqVO createReqVO) {
        return success(mppAutoDispatchService.createMppAutoDispatch(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新采购计划自动调度规则")
    @PreAuthorize("@ss.hasPermission('pms:mpp-auto-dispatch:update')")
    public CommonResult<Boolean> updateMppAutoDispatch(@Valid @RequestBody MppAutoDispatchSaveReqVO updateReqVO) {
        mppAutoDispatchService.updateMppAutoDispatch(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除采购计划自动调度规则")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:mpp-auto-dispatch:delete')")
    public CommonResult<Boolean> deleteMppAutoDispatch(@RequestParam("id") Long id) {
        mppAutoDispatchService.deleteMppAutoDispatch(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得采购计划自动调度规则")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:mpp-auto-dispatch:query')")
    public CommonResult<MppAutoDispatchRespVO> getMppAutoDispatch(@RequestParam("id") Long id) {
        MppAutoDispatchDO mppAutoDispatch = mppAutoDispatchService.getMppAutoDispatch(id);
        return success(BeanUtils.toBean(mppAutoDispatch, MppAutoDispatchRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得采购计划自动调度规则分页")
    @PreAuthorize("@ss.hasPermission('pms:mpp-auto-dispatch:query')")
    public CommonResult<PageResult<MppAutoDispatchRespVO>> getMppAutoDispatchPage(@Valid MppAutoDispatchPageReqVO pageReqVO) {
        PageResult<MppAutoDispatchDO> pageResult = mppAutoDispatchService.getMppAutoDispatchPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MppAutoDispatchRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出采购计划自动调度规则 Excel")
    @PreAuthorize("@ss.hasPermission('pms:mpp-auto-dispatch:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportMppAutoDispatchExcel(@Valid MppAutoDispatchPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MppAutoDispatchDO> list = mppAutoDispatchService.getMppAutoDispatchPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "采购计划自动调度规则.xls", "数据", MppAutoDispatchRespVO.class,
                        BeanUtils.toBean(list, MppAutoDispatchRespVO.class));
    }

}
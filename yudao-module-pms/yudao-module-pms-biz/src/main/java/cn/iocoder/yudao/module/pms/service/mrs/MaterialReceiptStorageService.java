package cn.iocoder.yudao.module.pms.service.mrs;

import java.util.*;
import javax.validation.*;

import cn.iocoder.yudao.module.pms.controller.admin.mrp.vo.MrpDetailImportRespVO;
import cn.iocoder.yudao.module.pms.controller.admin.mrs.vo.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.mrs.MaterialReceiptStorageDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 验收入储 Service 接口
 *
 * <AUTHOR>
 */
public interface MaterialReceiptStorageService {

    /**
     * 创建验收入储
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMaterialReceiptStorage(@Valid MaterialReceiptStorageSaveReqVO createReqVO);

    /**
     * 更新验收入储
     *
     * @param updateReqVO 更新信息
     */
    void updateMaterialReceiptStorage(@Valid MaterialReceiptStorageSaveReqVO updateReqVO);

    /**
     * 删除验收入储
     *
     * @param id 编号
     */
    void deleteMaterialReceiptStorage(Long id);

    String batchProcessing(Long[] ids, String chkno);

    void unBatchProcessing(Long[] ids);

    void confirm(Long[] ids);

    void cancel(Long[] ids);

    /**
     * 获得验收入储
     *
     * @param id 编号
     * @return 验收入储
     */
    MaterialReceiptStorageDO getMaterialReceiptStorage(Long id);

    MaterialReceiptStorageDO getMaterialReceiptStorageByChkno(String chkno);

    /**
     * 获得验收入储分页
     *
     * @param pageReqVO 分页查询
     * @return 验收入储分页
     */
    PageResult<MaterialReceiptStorageDO> getMaterialReceiptStoragePage(MaterialReceiptStoragePageReqVO pageReqVO);

    MrsImportRespVO importMrsList(List<MaterialReceiptStorageRespVO> list, Boolean updateSupport);
}
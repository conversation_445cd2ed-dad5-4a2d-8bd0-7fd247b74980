package cn.iocoder.yudao.module.pms.controller.admin.locmoncumular.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 储位料号月累计收发分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WmsLocMonCumularPageReqVO extends PageParam {

    @Schema(description = "料号")
    private String matrlno;

    @Schema(description = "品名")
    private String matrlname;

    @Schema(description = "品别")
    private String inventorytype;

    @Schema(description = "储位")
    private String locno;

    @Schema(description = "项目号")
    private String project;

    @Schema(description = "公司别")
    private String compid;

    @Schema(description = "批号")
    private String lotno;

    @Schema(description = "库存年月")
    private String yearmo;

    @Schema(description = "品级(备件属性)")
    private String matrlgrade;

    @Schema(description = "月初库存量")
    private BigDecimal mobeginqty;

    @Schema(description = "月初库存金额")
    private BigDecimal mobeginamt;

    @Schema(description = "月累积入库量")
    private BigDecimal moaccurecvqty;

    @Schema(description = "月累积入库金额")
    private BigDecimal moaccurecvamt;

    @Schema(description = "月累积出库量")
    private BigDecimal moaccuissuqty;

    @Schema(description = "月累积出库金额")
    private BigDecimal moaccuissuamt;

    @Schema(description = "月调整入库量")
    private BigDecimal moaccuadjrecvqty;

    @Schema(description = "月调整入库金额")
    private BigDecimal moaccuadjrecvamt;

    @Schema(description = "月调整出库量")
    private BigDecimal moaccuadjissuqty;

    @Schema(description = "月调整出库金额")
    private BigDecimal moaccuadjissuamt;

    @Schema(description = "年初库存量")
    private BigDecimal yrbeginqty;

    @Schema(description = "年初库存金额")
    private BigDecimal yrbeginamt;

    @Schema(description = "年累积入库量")
    private BigDecimal yraccurecvqty;

    @Schema(description = "年累积入库金额")
    private BigDecimal yraccurecvamt;

    @Schema(description = "年累积出库量")
    private BigDecimal yraccuissuqty;

    @Schema(description = "年累积出库金额")
    private BigDecimal yraccuissuamt;

    @Schema(description = "年调整入库量")
    private BigDecimal yraccuadjrecvqty;

    @Schema(description = "年调整入库金额")
    private BigDecimal yraccuadjrecvamt;

    @Schema(description = "年调整出库量")
    private BigDecimal yraccuadjissuqty;

    @Schema(description = "年调整出库金额")
    private BigDecimal yraccuadjissuamt;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "是否贸易")
    private String ismy;

}
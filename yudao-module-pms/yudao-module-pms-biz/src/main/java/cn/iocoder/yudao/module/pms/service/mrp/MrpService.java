package cn.iocoder.yudao.module.pms.service.mrp;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.pms.api.mrp.vo.MrpDTO;
import java.util.*;
import javax.validation.*;

import cn.iocoder.yudao.module.pms.controller.admin.kztrade.vo.KzTradeDetailImportRespVO;
import cn.iocoder.yudao.module.pms.controller.admin.kztrade.vo.KzTradeDetailImportVO;
import cn.iocoder.yudao.module.pms.controller.admin.mrp.vo.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.mrp.MrpDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.mrp.MrpDetailDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 需求计划 Service 接口
 *
 * <AUTHOR>
 */
public interface MrpService {

    /**
     * 创建需求计划
     *
     * @param createReqVO
     *            创建信息
     * @return 编号
     */
    Long createMrp(@Valid MrpSaveReqVO createReqVO);

    /**
     * 更新需求计划
     *
     * @param updateReqVO
     *            更新信息
     */
    void updateMrp(@Valid MrpSaveReqVO updateReqVO);

    /**
     * 删除需求计划
     *
     * @param id
     *            编号
     */
    void deleteMrp(Long id);

    /**
     * 获得需求计划
     *
     * @param id
     *            编号
     * @return 需求计划
     */
    MrpDO getMrp(Long id);

    /**
     * 获得需求计划分页
     *
     * @param pageReqVO
     *            分页查询
     * @return 需求计划分页
     */
    PageResult<MrpDO> getMrpPage(MrpPageReqVO pageReqVO);

    // ==================== 子表（需求计划明细） ====================

    /**
     * 获得需求计划明细分页
     *
     * @param pageReqVO
     *            分页查询
     * @param plantallyno
     *            计划单号
     * @return 需求计划明细分页
     */
    PageResult<MrpDetailDO> getMrpDetailPage(PageParam pageReqVO, String plantallyno,String stus);

    /**
     * 创建需求计划明细
     *
     * @param mrpDetail
     *            创建信息
     * @return 编号
     */
    Long createMrpDetail(@Valid MrpDetailDO mrpDetail);

    /**
     * 更新需求计划明细
     *
     * @param mrpDetail
     *            更新信息
     */
    void updateMrpDetail(@Valid MrpDetailDO mrpDetail);

    /**
     * 删除需求计划明细
     *
     * @param id
     *            编号
     */
    void deleteMrpDetail(Long id);

    /**
     * 获得需求计划明细
     *
     * @param id
     *            编号
     * @return 需求计划明细
     */
    MrpDetailDO getMrpDetail(Long id);

    /**
     * 根据需求计划条件，获取铭记计划单价

     *            明细表ID
     * @param matrlno
     *            料号
     *
     * @return 计划单价值
     */
    MrpDetailUnitpriceVO getMrpDetailUnitprice(String matrlno, String year);

    Long createFlow(Long userId, MrpSaveReqVO createReqVO);

    void updateMrpStatus(MrpDTO mrpDTO);

    PageResult<MrpBalancedVO> getMrpPageBalanced(MrpPageReqVO pageReqVO);

    String mrp2mpp(String ids);

    String back2mpp(MrpBalancedVO mrpBalancedVO);

    MrpDetailImportRespVO importMrpDetailList(List<MrpDetailImportVO> list, String plantallyno, Boolean updateSupport);

    List<MrpDetailExportVO> exportMrp(Long id);
    MrpDetailImportRespVO importMrpDetailList(List<MrpDetailImportVO> list, Long plantallyno, Boolean updateSupport);
}

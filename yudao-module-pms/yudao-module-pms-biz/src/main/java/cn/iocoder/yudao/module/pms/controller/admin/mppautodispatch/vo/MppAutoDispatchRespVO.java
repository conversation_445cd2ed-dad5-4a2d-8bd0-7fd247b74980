package cn.iocoder.yudao.module.pms.controller.admin.mppautodispatch.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 采购计划自动调度规则 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MppAutoDispatchRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "26936")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "项目")
    @ExcelProperty("项目")
    private String project;

    @Schema(description = "公司代码", example = "18872")
    @ExcelProperty("公司代码")
    private String compid;

    @Schema(description = "物料编号")
    @ExcelProperty("物料编号")
    private String matrlno;

    @Schema(description = "调度承办人")
    @ExcelProperty("调度承办人")
    private String dispatch;

    @Schema(description = "调度承办人名", example = "李四")
    @ExcelProperty("调度承办人名")
    private String dispatchName;

    @Schema(description = "承办人")
    @ExcelProperty("承办人")
    private String respemp;

    @Schema(description = "承办人名", example = "张三")
    @ExcelProperty("承办人名")
    private String respempName;

    @Schema(description = "新增人员")
    @ExcelProperty("新增人员")
    private String createEmp;

    @Schema(description = "新增日期")
    @ExcelProperty("新增日期")
    private String createDate;

    @Schema(description = "新增时间")
    @ExcelProperty("新增时间")
    private String createTime;

    @Schema(description = "修改人员")
    @ExcelProperty("修改人员")
    private String updateEmp;

    @Schema(description = "修改日期")
    @ExcelProperty("修改日期")
    private String updateDate;

    @Schema(description = "修改时间")
    @ExcelProperty("修改时间")
    private String updateTime;

    @Schema(description = "乐观锁")
    @ExcelProperty("乐观锁")
    private Long version;

}
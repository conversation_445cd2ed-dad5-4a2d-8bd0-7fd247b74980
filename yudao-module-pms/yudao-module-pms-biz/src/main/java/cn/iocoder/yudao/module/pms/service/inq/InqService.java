package cn.iocoder.yudao.module.pms.service.inq;

import java.util.*;
import javax.validation.*;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.pms.controller.admin.inq.vo.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.inq.InqDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.inq.InqDetailDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 采购案主档管理 Service 接口
 *
 * <AUTHOR>
 */
public interface InqService {

    /**
     * 创建采购案主档管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInq(@Valid InqSaveReqVO createReqVO);

    /**
     * 更新采购案主档管理
     *
     * @param updateReqVO 更新信息
     */
    void updateInq(@Valid InqSaveReqVO updateReqVO);

    /**
     * 删除采购案主档管理
     *
     * @param id 编号
     */
    void deleteInq(Long id);

    /**
     * 获得采购案主档管理
     *
     * @param id 编号
     * @return 采购案主档管理
     */
    InqDO getInq(Long id);

    /**
     * 获得采购案主档管理分页
     *
     * @param pageReqVO 分页查询
     * @return 采购案主档管理分页
     */
    PageResult<InqDO> getInqPage(InqPageReqVO pageReqVO);

    // ==================== 子表（采购案项次档管理） ====================

    /**
     * 获得采购案项次档管理分页
     *
     * @param pageReqVO 分页查询
     * @param parentid PARENTID
     * @return 采购案项次档管理分页
     */
    PageResult<InqDetailDO> getInqDetailPage(PageParam pageReqVO, Long parentid);

    /**
     * 创建采购案项次档管理
     *
     * @param inqDetail 创建信息
     * @return 编号
     */
    Long createInqDetail(@Valid InqDetailDO inqDetail);

    /**
     * 更新采购案项次档管理
     *
     * @param inqDetail 更新信息
     */
    void updateInqDetail(@Valid InqDetailDO inqDetail);

    /**
     * 删除采购案项次档管理
     *
     * @param id 编号
     */
    void deleteInqDetail(Long id);

	/**
	 * 获得采购案项次档管理
	 *
	 * @param id 编号
     * @return 采购案项次档管理
	 */
    InqDetailDO getInqDetail(Long id);

    void batchOperate(InqDetailBatchOperateReqVO detail);

    void submitInq(Long id);

    void cancelSubmitInq(Long id);

    void submitPonoInq(Long id);

    void invalidateInq(Long id);

    PageResult<InqDetailDO> getInqDetailPage2(PageParam pageReqVO, String purno);
}
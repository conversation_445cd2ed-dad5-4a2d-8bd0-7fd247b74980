package cn.iocoder.yudao.module.pms.service.pomainv;


import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.date.DateUtil;
import cn.iocoder.yudao.framework.common.util.date.DateUtils;
import cn.iocoder.yudao.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.yudao.module.pms.controller.admin.pomain.vo.MaterialRespVO;
import cn.iocoder.yudao.module.pms.controller.admin.pomain.vo.PoDetailsBatchInsertByMatrlReqVO;
import cn.iocoder.yudao.module.pms.controller.admin.pomain.vo.PoDetailsBatchInsertReqVO;
import cn.iocoder.yudao.module.pms.dal.dataobject.inq.InqDetailDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.mpppurchase.MppPurchasedDetailDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.other.MaterialDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.pomain.PoDetailDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.pomain.PoMainDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.pomain.PoTocDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.pomainv.PoDetailVDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.pomainv.PoTocVDO;
import cn.iocoder.yudao.module.pms.dal.mysql.inq.InqDetailMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.mpppurchase.MppPurchasedDetailMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.other.MaterialMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.pomain.PoDetailMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.pomain.PoMainMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.pomain.PoTocMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.pomainv.PoDetailVMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.pomainv.PoTocVMapper;
import cn.iocoder.yudao.module.pms.service.pomain.PoMainService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.util.*;

import cn.iocoder.yudao.module.pms.controller.admin.pomainv.vo.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.pomainv.PoMainVDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.pms.dal.mysql.pomainv.PoMainVMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertSet;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserNickname;
import static cn.iocoder.yudao.module.pms.enums.ErrorCodeConstants.*;

/**
 * 合同版本管理表 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PoMainVServiceImpl implements PoMainVService {

    @Resource
    private PoMainVMapper poMainVMapper;
    @Resource
    private PoDetailVMapper poDetailVMapper;
    @Resource
    private PoTocVMapper poTocVMapper;
    @Resource
    private PoDetailMapper poDetailMapper;
    @Resource
    private PoTocMapper poTocMapper;
    @Resource
    private PoMainService poMainService;
    @Resource
    private PoMainMapper poMainMapper;
    @Resource
    private MppPurchasedDetailMapper mppPurchasedDetailMapper;
    @Resource
    private MaterialMapper materialMapper;
    @Resource
    private InqDetailMapper inqDetailMapper;

    @Override
    public Long createPoMainV(Long id) {
        List<PoDetailVDO> poDetailVList = new ArrayList<>();
        List<PoTocVDO> poTocVList = new ArrayList<>();
        String nextPover = "00";
        BigDecimal totalAmt = new BigDecimal(0);
        BigDecimal totalQty = new BigDecimal(0);

        PoMainDO poMain = poMainService.getPoMain(id);
        List<PoMainVDO> mainVList = poMainVMapper.selectListByParentId(id);
        List<PoDetailDO> poDetailDOs = poDetailMapper.selectListByParentId(id);
        List<PoTocDO> poTocVDOs = poTocMapper.selectListByParentId(id);

        if (mainVList != null && mainVList.size() > 0) {
            nextPover = String.format("%02d", Integer.valueOf(mainVList.size()));
        }
        PoMainVDO poMainV = BeanUtils.toBean(poMain, PoMainVDO.class);
        poMainV.setId(null);
        poMainV.setProject(poMain.getProject());
        poMainV.setCompid(poMain.getCompid());
        poMainV.setPover(nextPover);
        poMainV.setParentId(id);
        poMainV.setFlowstus("0");
        poMainV.setPostus("0");
        poMainV.setSignplace(null);
        poMainV.setCreateEmpno(getLoginUserNickname());
        poMainV.setUpdateEmpno(getLoginUserNickname());
        poMainV.setCreator(String.valueOf(getLoginUserId()));
        poMainV.setUpdater(String.valueOf(getLoginUserId()));
        poMainV.setCreateDate(DateUtil.getDate());
        poMainV.setUpdateDate(DateUtil.getDate());
        poMainVMapper.insert(poMainV);
        Long parentId = poMainV.getId();
        if (poDetailDOs != null && poDetailDOs.size() > 0) {
            for (PoDetailDO detailDO : poDetailDOs) {
                PoDetailVDO detailVDO = BeanUtils.toBean(detailDO, PoDetailVDO.class);
                detailVDO.setId(null);
                detailVDO.setProject(poMain.getProject());
                detailVDO.setCompid(poMain.getCompid());
                detailVDO.setPover(nextPover);
                detailVDO.setParentId(parentId);
                detailVDO.setCreator(String.valueOf(getLoginUserId()));
                detailVDO.setUpdater(String.valueOf(getLoginUserId()));
                detailVDO.setCreateEmpno(getLoginUserNickname());
                detailVDO.setUpdateEmpno(getLoginUserNickname());
                detailVDO.setCreateDate(DateUtil.getDate());
                detailVDO.setUpdateDate(DateUtil.getDate());
                BigDecimal amt = detailDO.getAmt();
                if (amt == null) {
                    amt = new BigDecimal(0);
                }
                BigDecimal qty = detailDO.getQty();
                if (qty == null) {
                    qty = new BigDecimal(0);
                }
                totalAmt = totalAmt.add(amt);
                totalQty = totalQty.add(qty);
                poDetailVList.add(detailVDO);
            }
            poDetailVMapper.insert(poDetailVList);
        }
        if (poTocVDOs != null && poTocVDOs.size() > 0) {
            for (PoTocDO poTocDO : poTocVDOs) {
                PoTocVDO poTocVDO = BeanUtils.toBean(poTocDO, PoTocVDO.class);
                poTocVDO.setId(null);
                poTocVDO.setPover(nextPover);
                poTocVDO.setParentId(parentId);
                poTocVDO.setCreator(String.valueOf(getLoginUserId()));
                poTocVDO.setUpdater(String.valueOf(getLoginUserId()));
                poTocVDO.setCreateEmpno(getLoginUserNickname());
                poTocVDO.setUpdateEmpno(getLoginUserNickname());
                poTocVDO.setCreateDate(DateUtil.getDate());
                poTocVDO.setUpdateDate(DateUtil.getDate());
                poTocVList.add(poTocVDO);
            }
            poTocVMapper.insert(poTocVList);
        }
        // 返回
        return poMainV.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePoMainV(PoMainVSaveReqVO updateReqVO) {
        // 校验存在
        validatePoMainVExists(updateReqVO.getId());
//        String serialNo = poMainVMapper.selectMaxPoverByParentId(updateReqVO.getParentId());
        // 更新
        PoMainVDO updateObj = BeanUtils.toBean(updateReqVO, PoMainVDO.class);
        updateObj.setUpdateEmpno(getLoginUserNickname());
        updateObj.setUpdater(String.valueOf(getLoginUserId()));
        updateObj.setUpdateDate(DateUtil.getDate());
        //updateObj.setId(null);
        //updateObj.setPover(String.format("%02d", Integer.valueOf(serialNo) + 1));
        poMainVMapper.updateById(updateObj);

        // 更新子表
//        updatePoDetailVList(updateReqVO.getId(), updateReqVO.getPoDetailVs());
//        updatePoTocVList(updateReqVO.getId(), updateReqVO.getPoTocVs());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePoMainV(Long id) {
        // 校验存在
        validatePoMainVExists(id);
        // 删除
        poMainVMapper.deleteById(id);

        // 删除子表
        deletePoDetailVByParentId(id);
        deletePoTocVByParentId(id);
    }

    private void validatePoMainVExists(Long id) {
        if (poMainVMapper.selectById(id) == null) {
            throw exception(PO_MAIN_V_NOT_EXISTS);
        }
    }

    @Override
    public PoMainVDO getPoMainV(Long id) {
        return poMainVMapper.selectById(id);
    }

    @Override
    public List<PoMainVRespVO> getPoMainVByParentId(Long parentId) {
        List<PoMainVDO> poMainVDOs = poMainVMapper.selectListByParentId(parentId);
        List<PoMainVRespVO> result = new ArrayList<>();
        if (poMainVDOs != null && poMainVDOs.size() > 0) {
            for (PoMainVDO mainVDO : poMainVDOs) {
                PoMainVRespVO respVO = BeanUtils.toBean(mainVDO, PoMainVRespVO.class);
                PageParam param = new PageParam();
                param.setPageNo(1);
                param.setPageSize(100);
                respVO.setPoDetailVs(getPoDetailVListByParentId(param, respVO.getId()));
                respVO.setPoTocVs(getPoTocVListByParentId(param, respVO.getId()));
                result.add(respVO);
            }
        }
        return result;
    }

    @Override
    public PoDetailVDO getPoDetailV(Long id) {
        return poDetailVMapper.selectById(id);
    }

    @Override
    public PoTocVDO getPoTocV(Long id) {
        return poTocVMapper.selectById(id);
    }

    @Override
    public PageResult<PoMainVDO> getPoMainVPage(PoMainVPageReqVO pageReqVO) {
        IPage<PoMainVDO> pageResult = poMainVMapper.selectPageList(
                MyBatisUtils.buildPage(pageReqVO),
                pageReqVO.getParentId(),
                pageReqVO.getPono(),
                pageReqVO.getPover(),
                pageReqVO.getAppid()
        );
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }


    // ==================== 子表（合同版本管理明细） ====================

    @Override
    public PageResult<PoDetailVDO> getPoDetailVListByParentId(PageParam pageReqVO, Long parentId) {
        return poDetailVMapper.selectPage(pageReqVO, parentId);
    }

    private void createPoDetailVList(Long parentId, List<PoDetailVDO> list) {
        list.forEach(o -> o.setParentId(parentId));
        poDetailVMapper.insertBatch(list);
    }

    private void updatePoDetailVList(Long parentId, List<PoDetailVDO> list) {
        deletePoDetailVByParentId(parentId);
        list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createPoDetailVList(parentId, list);
    }

    private void deletePoDetailVByParentId(Long parentId) {
        poDetailVMapper.deleteByParentId(parentId);
    }

    // ==================== 子表（合同版本管理涉及的条款内容） ====================

    @Override
    public PageResult<PoTocVDO> getPoTocVListByParentId(PageParam pageReqVO, Long parentId) {
        return poTocVMapper.selectPage(pageReqVO, parentId);
    }

    private void createPoTocVList(Long parentId, List<PoTocVDO> list) {
        list.forEach(o -> o.setParentId(parentId));
        poTocVMapper.insertBatch(list);
    }

    private void updatePoTocVList(Long parentId, List<PoTocVDO> list) {
        deletePoTocVByParentId(parentId);
        list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createPoTocVList(parentId, list);
    }

    private void deletePoTocVByParentId(Long parentId) {
        poTocVMapper.deleteByParentId(parentId);
    }

    @Override
    public void updatePoDetailV(PoDetailVDO poDetailV) {
        // 校验存在
        validatePoDetailVExists(poDetailV.getId());
        poDetailV.setUpdateEmpno(getLoginUserNickname());
        poDetailV.setUpdater(String.valueOf(getLoginUserId()));
        poDetailV.setUpdateDate(DateUtil.getDate());
        poDetailVMapper.updateById(poDetailV);
    }

    private void validatePoDetailVExists(Long id) {
        if (poDetailVMapper.selectById(id) == null) {
            throw exception(PO_DETAIL_V_NOT_EXISTS);
        }
    }

    @Override
    public void updatePoTocV(PoTocVDO poTocV) {
        // 校验存在
        validatePoTocVExists(poTocV.getId());

        poTocV.setUpdateEmpno(getLoginUserNickname());
        poTocV.setUpdater(String.valueOf(getLoginUserId()));
        poTocV.setUpdateDate(DateUtil.getDate());
        // 更新
        poTocVMapper.updateById(poTocV);
    }

    private void validatePoTocVExists(Long id) {
        if (poTocVMapper.selectById(id) == null) {
            throw exception(PO_TOC_V_NOT_EXISTS);
        }
    }

    @Override
    public Long createPoDetailV(PoDetailVDO poDetail) {
        Long parentId = poDetail.getParentId();
        validatePoMainVExists(parentId);
        PoMainVDO mainDO = this.getPoMainV(parentId);
        String serialNo = poDetailVMapper.getMaxPoItemNoByParentId(parentId);
        String poItemNo = "0000";
        if (serialNo != null) {
            poItemNo = String.format("%04d", Integer.valueOf(serialNo) + 1);
        }
        poDetail.setProject(mainDO.getProject());
        poDetail.setCompid(mainDO.getCompid());
        poDetail.setPono(mainDO.getPono());
        poDetail.setPoitemno(poItemNo);
        poDetail.setCreator(String.valueOf(getLoginUserId()));
        poDetail.setUpdater(String.valueOf(getLoginUserId()));
        poDetail.setCreateEmpno(getLoginUserNickname());
        poDetail.setUpdateEmpno(getLoginUserNickname());
        poDetail.setCreateDate(DateUtil.getDate());
        poDetail.setUpdateDate(DateUtil.getDate());
        poDetailVMapper.insert(poDetail);
        return poDetail.getId();
    }

    @Override
    public Long createPoTocV(PoTocVDO poToc) {
        Long parentId = poToc.getParentId();
        validatePoMainVExists(parentId);
        PoMainVDO mainDO = this.getPoMainV(parentId);
        poToc.setProject(mainDO.getProject());
        poToc.setCompid(mainDO.getCompid());
        poToc.setPono(mainDO.getPono());
        poToc.setCreator(String.valueOf(getLoginUserId()));
        poToc.setUpdater(String.valueOf(getLoginUserId()));
        poToc.setCreateEmpno(getLoginUserNickname());
        poToc.setUpdateEmpno(getLoginUserNickname());
        poToc.setCreateDate(DateUtil.getDate());
        poToc.setUpdateDate(DateUtil.getDate());
        poTocVMapper.insert(poToc);
        return poToc.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInsertPoDetailVsByPurId(PoDetailsBatchInsertReqVO reqVO) {
        Long parentId = reqVO.getParentId();
        PoMainVDO poMainVDO = poMainVMapper.selectById(parentId);
        List<MaterialRespVO> idList = reqVO.getItems();
        String serialNo = poDetailVMapper.getMaxPoItemNoByParentId(parentId);
        String poItemNo = "0000";
        if (serialNo != null) {
            poItemNo = String.format("%04d", Integer.valueOf(serialNo) + 1);
        }
        Set<Long> ids = convertSet(idList, MaterialRespVO::getId);
        List<MppPurchasedDetailDO> mppPurchasedDetailDOS = mppPurchasedDetailMapper.selectListById(ids);
        for (MppPurchasedDetailDO mppPurchasedDetailDO : mppPurchasedDetailDOS) {
            for (MaterialRespVO item : idList) {
                if (item.getId().equals(mppPurchasedDetailDO.getId())) {
                    PoDetailVDO poDetailDO = new PoDetailVDO();
                    poDetailDO.setProject(poMainVDO.getProject());
                    poDetailDO.setCompid(poMainVDO.getCompid());
                    poDetailDO.setParentId(parentId);
                    poDetailDO.setPover(poMainVDO.getPover());
                    poDetailDO.setPono(poMainVDO.getPono());
                    poDetailDO.setPoitemno(poItemNo);
                    poDetailDO.setPurno(mppPurchasedDetailDO.getPurno());
                    poDetailDO.setPuritemno(mppPurchasedDetailDO.getPuritemno());
                    poDetailDO.setReqno(mppPurchasedDetailDO.getReqno());
                    poDetailDO.setReqitemno(mppPurchasedDetailDO.getReqitemno());
                    poDetailDO.setMatrlno(mppPurchasedDetailDO.getMatrlno());
                    poDetailDO.setMatrlname(mppPurchasedDetailDO.getCnMdesc());
                    poDetailDO.setSpec((mppPurchasedDetailDO.getChNspec() == null ? "" : mppPurchasedDetailDO.getChNspec()) +
                            (mppPurchasedDetailDO.getQlty() == null ? "" : mppPurchasedDetailDO.getQlty()) +
                            (mppPurchasedDetailDO.getDrawing() == null ? "" : mppPurchasedDetailDO.getDrawing()));
                    poDetailDO.setQlty(mppPurchasedDetailDO.getQlty());
                    poDetailDO.setQty(item.getQty());
                    poDetailDO.setUnitprice(item.getUnitprice());
                    poDetailDO.setAmt(item.getQty().multiply(item.getUnitprice()));
                    poDetailDO.setUnit(mppPurchasedDetailDO.getUnit());
                    poDetailDO.setUsedept(mppPurchasedDetailDO.getUsedept());
                    poDetailDO.setCreator(String.valueOf(getLoginUserId()));
                    poDetailDO.setUpdater(String.valueOf(getLoginUserId()));
                    poDetailDO.setCreateEmpno(getLoginUserNickname());
                    poDetailDO.setUpdateEmpno(getLoginUserNickname());
                    poDetailDO.setCreateDate(DateUtil.getDate());
                    poDetailDO.setUpdateDate(DateUtil.getDate());
                    poDetailVMapper.insert(poDetailDO);
                    poItemNo = String.format("%04d", Integer.valueOf(poItemNo) + 1);
                }
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInsertPoDetailVsByMatrlId(PoDetailsBatchInsertByMatrlReqVO reqVO) {
        Long parentId = reqVO.getParentId();
        PoMainVDO poMainVDO = poMainVMapper.selectById(parentId);
        List<MaterialRespVO> items = reqVO.getItems();
        String serialNo = poDetailVMapper.getMaxPoItemNoByParentId(parentId);
        String poItemNo = "0000";
        if (serialNo != null) {
            poItemNo = String.format("%04d", Integer.valueOf(serialNo) + 1);
        }
        Set<Long> ids = convertSet(items, MaterialRespVO::getId);
        List<MaterialDO> materialDOS = materialMapper.selectListById(ids);
        for (MaterialDO materialDO : materialDOS) {
            for (MaterialRespVO item : items) {
                if (item.getId().equals(materialDO.getId())) {
                    PoDetailVDO poDetailDO = new PoDetailVDO();
                    poDetailDO.setProject(poMainVDO.getProject());
                    poDetailDO.setCompid(poMainVDO.getCompid());
                    poDetailDO.setParentId(parentId);
                    poDetailDO.setPover(poMainVDO.getPover());
                    poDetailDO.setPono(poMainVDO.getPono());
                    poDetailDO.setPoitemno(poItemNo);
                    poDetailDO.setMatrlno(materialDO.getMatrlno());
                    poDetailDO.setMatrlname(materialDO.getCnmdesc());
                    poDetailDO.setSpec((materialDO.getNmspec() == null ? "" : materialDO.getNmspec()) +
                            (materialDO.getQuality() == null ? "" : materialDO.getQuality()) +
                            (materialDO.getPicno() == null ? "" : materialDO.getPicno()));
                    poDetailDO.setQty(item.getQty());
                    poDetailDO.setQlty(materialDO.getQuality());
                    poDetailDO.setUnitprice(item.getUnitprice());
                    poDetailDO.setAmt(item.getQty().multiply(item.getUnitprice()));
                    poDetailDO.setUnit(materialDO.getUnitinv());
                    poDetailDO.setCreator(String.valueOf(getLoginUserId()));
                    poDetailDO.setUpdater(String.valueOf(getLoginUserId()));
                    poDetailDO.setCreateEmpno(getLoginUserNickname());
                    poDetailDO.setUpdateEmpno(getLoginUserNickname());
                    poDetailDO.setCreateDate(DateUtil.getDate());
                    poDetailDO.setUpdateDate(DateUtil.getDate());
                    poDetailVMapper.insert(poDetailDO);
                    poItemNo = String.format("%04d", Integer.valueOf(poItemNo) + 1);
                }
            }
        }
    }

    @Override
    public void batchInsertPoDetailVByInq(PoDetailsBatchInsertReqVO reqVO) {
        Long parentId = reqVO.getParentId();
        PoMainVDO poMainDO = poMainVMapper.selectById(parentId);
        List<MaterialRespVO> idList = reqVO.getItems();
        String serialNo = poDetailVMapper.getMaxPoItemNoByParentId(parentId);
        String poItemNo = "0000";
        if (serialNo != null) {
            poItemNo = String.format("%04d", Integer.valueOf(serialNo) + 1);
        }
        Set<Long> ids = convertSet(idList, MaterialRespVO::getId);
        List<InqDetailDO> inqDetailDOS = inqDetailMapper.selectListByIds(ids);
        for (InqDetailDO inqDetailDO : inqDetailDOS) {
            for (MaterialRespVO item : idList) {
                if (item.getId().equals(inqDetailDO.getId())) {
                    if (item.getQty() == null || (item.getQty().compareTo(new BigDecimal(0)) == 0)) {
                        throw exception(PO_DETAIL_QTY_NOT_EMPTY, inqDetailDO.getInqId(), inqDetailDO.getInqLineId());
                    }
                    if (item.getUnitprice() == null || (item.getUnitprice().compareTo(new BigDecimal(0)) == 0)) {
                        throw exception(PO_DETAIL_UNIT_PRICE_NOT_EMPTY, inqDetailDO.getInqId(), inqDetailDO.getInqLineId());
                    }
                    /*
                    // 国贸需求确认 暂不校验请购数量是否超出
                    BigDecimal inpriceqt = mppPurchasedDetailDO.getInpriceqt() == null ? new BigDecimal(0) : mppPurchasedDetailDO.getInpriceqt();
                    if (item.getQty().compareTo(mppPurchasedDetailDO.getQty().subtract(inpriceqt)) > 0) {
                        throw exception(PO_DETAIL_QTY_MORE_THAN, mppPurchasedDetailDO.getReqno(), mppPurchasedDetailDO.getReqitemno());
                    }*/
                    PoDetailVDO poDetailDO = new PoDetailVDO();
                    poDetailDO.setProject(poMainDO.getProject());
                    poDetailDO.setCompid(poMainDO.getCompid());
                    poDetailDO.setParentId(parentId);
                    poDetailDO.setPono(poMainDO.getPono());
                    poDetailDO.setPoitemno(poItemNo);
                    poDetailDO.setPurno(inqDetailDO.getInqId());
                    poDetailDO.setPuritemno(inqDetailDO.getInqLineId());
                    poDetailDO.setReqitemno(inqDetailDO.getMrLineId());
                    poDetailDO.setMatrlno(inqDetailDO.getItemId());
                    poDetailDO.setMatrlname(inqDetailDO.getItemName());
                    poDetailDO.setSpec(inqDetailDO.getItemDesc());
                    poDetailDO.setQty(item.getQty());
                    poDetailDO.setUnitprice(item.getUnitprice());
                    poDetailDO.setAmt(item.getQty().multiply(item.getUnitprice()));
                    poDetailDO.setUnit(inqDetailDO.getItemUom());
                    poDetailDO.setUsedept(inqDetailDO.getDeptId());
                    poDetailDO.setCreator(String.valueOf(getLoginUserId()));
                    poDetailDO.setUpdater(String.valueOf(getLoginUserId()));
                    poDetailDO.setCreateEmpno(getLoginUserNickname());
                    poDetailDO.setUpdateEmpno(getLoginUserNickname());
                    poDetailDO.setCreateDate(DateUtil.getDate());
                    poDetailDO.setUpdateDate(DateUtil.getDate());
                    poDetailVMapper.insert(poDetailDO);
                    poItemNo = String.format("%04d", Integer.valueOf(poItemNo) + 1);
                }
            }
        }
    }
    private final static String POMAINV="POMAINV:";
    @Override
    @Cacheable(value = POMAINV,key = "#poNo + '_' + #poVer",unless = "#result == null")
    public PoMainVDO getPoMainVPre(String poNo, String poVer) {
        return poMainVMapper.selectPoNoByPre(poNo,poVer);
    }

}

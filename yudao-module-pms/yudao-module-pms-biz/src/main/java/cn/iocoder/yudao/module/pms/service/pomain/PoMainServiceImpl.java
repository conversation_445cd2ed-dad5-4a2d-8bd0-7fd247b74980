package cn.iocoder.yudao.module.pms.service.pomain;

import cn.hutool.core.date.DatePattern;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.date.DateUtil;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.framework.common.util.number.MoneyUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.dict.core.DictFrameworkUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.pms.api.pomain.dto.PoDetailRespDTO;
import cn.iocoder.yudao.module.pms.api.pomain.dto.PoMainReqDTO;
import cn.iocoder.yudao.module.pms.api.pomain.dto.PoMainRespDTO;
import cn.iocoder.yudao.module.pms.controller.admin.pomain.vo.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.epdetail.EpDetailDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.inq.InqDetailDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.invoiceinfo.InvoiceInfoDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.mpppurchase.MppPurchasedDetailDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.other.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.pomain.PoDetailDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.pomain.PoMainDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.pomain.PoTocDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.pomainv.PoDetailVDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.pomainv.PoMainVDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.pomainv.PoTocVDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.potoccontent.PoTocContentDO;
import cn.iocoder.yudao.module.pms.dal.mysql.epdetail.EpDetailMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.inq.InqDetailMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.invoiceinfo.InvoiceInfoMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.mpppurchase.MppPurchasedDetailMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.other.*;
import cn.iocoder.yudao.module.pms.dal.mysql.pomain.PoDetailMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.pomain.PoMainMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.pomain.PoTocMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.pomainv.PoDetailVMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.pomainv.PoMainVMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.pomainv.PoTocVMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.potoccontent.PoTocContentMapper;
import cn.iocoder.yudao.module.pms.dal.redis.no.PmsNoRedisDAO;
import cn.iocoder.yudao.module.pms.util.PostUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertSet;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.*;
import static cn.iocoder.yudao.module.pms.enums.ErrorCodeConstants.*;

/**
 * 合同信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PoMainServiceImpl implements PoMainService {

    @Resource
    private PoMainMapper poMainMapper;
    @Resource
    private PoMainVMapper poMainVMapper;
    @Resource
    private PoDetailMapper poDetailMapper;
    @Resource
    private PoTocMapper poTocMapper;
    @Resource
    private PoDetailVMapper poDetailVMapper;
    @Resource
    private PoTocVMapper poTocVMapper;
    @Resource
    private PmsNoRedisDAO noRedisDAO;
    @Resource
    private PoTocContentMapper poTocContentMapper;
    @Resource
    private MppPurchasedDetailMapper mppPurchasedDetailMapper;
    @Resource
    private MaterialMapper materialMapper;
    @Resource
    private EpDetailMapper epDetailMapper;
    @Resource
    private InvoiceInfoMapper invoiceInfoMapper;
    @Resource
    private Tbso011Mapper tbso011Mapper;
    @Resource
    private Tbso012Mapper tbso012Mapper;
    @Resource
    private Tbso013Mapper tbso013Mapper;
    @Resource
    private ContractTradeDetailMapper contractTradeDetailMapper;
    @Resource
    private ContractTradeMapper contractTradeMapper;
    @Resource
    private InqDetailMapper inqDetailMapper;

    @Override
    public Long createPoMain(PoMainSaveReqVO createReqVO) {
        String pono = createReqVO.getPono();
        if (StringUtils.isBlank(pono)) {
            // 生成订购单号
            pono = noRedisDAO.generate(PmsNoRedisDAO.PO_MAIN_NO_PREFIX);
        }
        // 校验唯一
        if (poMainMapper.selectByPono(pono) != null) {
            throw exception(PO_NO_EXISTS);
        }
        PoMainDO poMain = BeanUtils.toBean(createReqVO, PoMainDO.class);
        poMain.setProject(getLoginUserTopDeptId().toString());
        poMain.setCompid(getLoginUserTenantId().toString());
        poMain.setPono(pono);
        poMain.setCreateEmpno(getLoginUserNickname());
        poMain.setUpdateEmpno(getLoginUserNickname());
        poMain.setCreator(String.valueOf(getLoginUserId()));
        poMain.setUpdater(String.valueOf(getLoginUserId()));
        poMain.setCreateDate(DateUtil.getDate());
        poMain.setUpdateDate(DateUtil.getDate());
        poMain.setFlowstus("0");
        poMain.setPostus("0");
        poMainMapper.insert(poMain);
        return poMain.getId();
    }

    @Override
    @Transactional
    public void updatePoMain(PoMainSaveReqVO updateReqVO) {
        // 校验存在
        validatePoMainExists(updateReqVO.getId());
        // 更新
        PoMainDO updateObj = BeanUtils.toBean(updateReqVO, PoMainDO.class);
        updateObj.setUpdateEmpno(getLoginUserNickname());
        updateObj.setUpdater(String.valueOf(getLoginUserId()));
        updateObj.setUpdateDate(DateUtil.getDate());
        poMainMapper.updateById(updateObj);

        PoMainDO poMainDO = poMainMapper.selectById(updateReqVO.getId());
        String istax = poMainDO.getIstax();
        if (istax != null && !istax.equals(updateReqVO.getIstax())) {
            List<PoDetailDO> poDetailDOS = poDetailMapper.selectListByParentId(updateReqVO.getId());
            if (poDetailDOS.size() > 0) {
                for (PoDetailDO poDetailDO : poDetailDOS) {
                    poDetailDO.setUnitprice(new BigDecimal(0));
                    poDetailDO.setUnitpriceexdtax(new BigDecimal(0));
                    poDetailDO.setUpdateEmpno(getLoginUserNickname());
                    poDetailDO.setUpdater(String.valueOf(getLoginUserId()));
                    poDetailDO.setUpdateDate(DateUtil.getDate());
                    poDetailMapper.updateById(poDetailDO);
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePoMain(Long id) {
        // 校验存在
        validatePoMainExists(id);
        // 删除
        poMainMapper.deleteById(id);
        // 删除子表
        deletePoDetailByParentId(id);
        deletePoTocByParentId(id);
    }

    @Override
    public void validatePoMainExists(Long id) {
        if (poMainMapper.selectById(id) == null) {
            throw exception(PO_MAIN_NOT_EXISTS);
        }
    }

    public boolean validatePoNoNotExists(String pono) {
        if (poMainMapper.selectByPono(pono) != null) {
            return false;
        }
        return true;
    }

    @Override
    public PoMainDO getPoMain(Long id) {
        return poMainMapper.selectById(id);
    }

    @Override
    public PoMainDO getPoMain(String pono) {
        return poMainMapper.selectOne(PoMainDO::getPono, pono);
    }

    @Override
    public PageResult<PoMainDO> getPoMainPage(PoMainPageReqVO pageReqVO) {
        return poMainMapper.selectPage(pageReqVO);
    }

    @Transactional
    @Override
    public void continuePoMainProcess(Long id) {
        List<PoDetailVDO> poDetailVList = new ArrayList<>();
        List<PoTocVDO> poTocVList = new ArrayList<>();
        List<PoDetailDO> poDetailList = new ArrayList<>();
        List<PoTocDO> poTocList = new ArrayList<>();
        String nextPover = "00";
        BigDecimal totalAmt = new BigDecimal(0);
        BigDecimal totalQty = new BigDecimal(0);
        List<PoMainVDO> mainVList = poMainVMapper.selectListByParentId(id);

        if (mainVList != null && mainVList.size() > 0) {
            nextPover = String.format("%02d", Integer.valueOf(mainVList.size()));
        }
        if ("00".equals(nextPover)) {//第一次确认时 将合同同步至合同版本表
            PoMainDO poMain = getPoMain(id);
            poMain.setFlowstus("M");
            poMain.setPostus("M");
            List<PoDetailDO> poDetailDOs = poDetailMapper.selectListByParentId(id);
            List<PoTocDO> poTocVDOs = poTocMapper.selectListByParentId(id);
            PoMainVDO poMainV = BeanUtils.toBean(poMain, PoMainVDO.class);
            poMainV.setId(null);
            poMainV.setProject(poMain.getProject());
            poMainV.setCompid(poMain.getCompid());
            poMainV.setPover(nextPover);
            poMainV.setParentId(id);
            poMainV.setCreateEmpno(getLoginUserNickname());
            poMainV.setUpdateEmpno(getLoginUserNickname());
            poMainV.setCreator(String.valueOf(getLoginUserId()));
            poMainV.setUpdater(String.valueOf(getLoginUserId()));
            poMainV.setCreateDate(DateUtil.getDate());
            poMainV.setUpdateDate(DateUtil.getDate());
            poMainVMapper.insert(poMainV);

            Long parentId = poMainV.getId();
            if (poDetailDOs != null && poDetailDOs.size() > 0) {
                for (PoDetailDO detailDO : poDetailDOs) {
                    detailDO.setIsConfirm("Y"); //确认后 更新是否确认状态为Y
                    poDetailMapper.updateById(detailDO);
                    MppPurchasedDetailDO mppPurchasedDetailDO = mppPurchasedDetailMapper.selectOne(MppPurchasedDetailDO::getReqno, detailDO.getReqno(),
                            MppPurchasedDetailDO::getReqitemno, detailDO.getReqitemno(),
                            MppPurchasedDetailDO::getMatrlno, detailDO.getMatrlno());
                    if (mppPurchasedDetailDO != null) {
                        if (detailDO.getQty() == null || (detailDO.getQty().compareTo(new BigDecimal(0)) == 0)) {
                            throw exception(PO_DETAIL_QTY_NOT_EMPTY, mppPurchasedDetailDO.getReqno(), mppPurchasedDetailDO.getReqitemno());
                        }
                        BigDecimal inpriceqt = mppPurchasedDetailDO.getInpriceqt() == null ? new BigDecimal(0) : mppPurchasedDetailDO.getInpriceqt();
                        if (detailDO.getQty().compareTo(mppPurchasedDetailDO.getQty().subtract(inpriceqt)) > 0) {
                            //国贸需求确认 不需要校验请购数量是否超出
                            //throw exception(PO_DETAIL_QTY_MORE_THAN, mppPurchasedDetailDO.getReqno(), mppPurchasedDetailDO.getReqitemno());
                        } else {
                            // 确认时 回写采购明细 中标数量
                            mppPurchasedDetailDO.setInpriceqt((mppPurchasedDetailDO.getInpriceqt() == null ? new BigDecimal(0) : mppPurchasedDetailDO.getInpriceqt()).add(detailDO.getQty()));
                            mppPurchasedDetailMapper.updateById(mppPurchasedDetailDO);
                        }
                    }
                    PoDetailVDO detailVDO = BeanUtils.toBean(detailDO, PoDetailVDO.class);
                    detailVDO.setId(null);
                    detailVDO.setProject(poMain.getProject());
                    detailVDO.setCompid(poMain.getCompid());
                    detailVDO.setPover(nextPover);
                    detailVDO.setParentId(parentId);
                    detailVDO.setIsConfirm("Y");
                    detailVDO.setCreator(String.valueOf(getLoginUserId()));
                    detailVDO.setUpdater(String.valueOf(getLoginUserId()));
                    detailVDO.setCreateEmpno(getLoginUserNickname());
                    detailVDO.setUpdateEmpno(getLoginUserNickname());
                    detailVDO.setCreateDate(DateUtil.getDate());
                    detailVDO.setUpdateDate(DateUtil.getDate());
                    BigDecimal amt = detailDO.getAmt();
                    if (amt == null) {
                        amt = new BigDecimal(0);
                    }
                    BigDecimal qty = detailDO.getQty();
                    if (qty == null) {
                        qty = new BigDecimal(0);
                    }
                    totalAmt = totalAmt.add(amt);
                    totalQty = totalQty.add(qty);
                    poDetailVList.add(detailVDO);
                }
                poDetailVMapper.insert(poDetailVList);
            }
            if (poTocVDOs != null && poTocVDOs.size() > 0) {
                for (PoTocDO poTocDO : poTocVDOs) {
                    PoTocVDO poTocVDO = BeanUtils.toBean(poTocDO, PoTocVDO.class);
                    poTocVDO.setId(null);
                    poTocVDO.setPover(nextPover);
                    poTocVDO.setParentId(parentId);
                    poTocVDO.setCreator(String.valueOf(getLoginUserId()));
                    poTocVDO.setUpdater(String.valueOf(getLoginUserId()));
                    poTocVDO.setCreateEmpno(getLoginUserNickname());
                    poTocVDO.setUpdateEmpno(getLoginUserNickname());
                    poTocVDO.setCreateDate(DateUtil.getDate());
                    poTocVDO.setUpdateDate(DateUtil.getDate());
                    poTocVList.add(poTocVDO);
                }
                poTocVMapper.insert(poTocVList);
            }
            poMainV.setTotalAmt(totalAmt);
            poMainV.setTotalQty(totalQty);
            poMainVMapper.updateById(poMainV);

            poMain.setTotalAmt(totalAmt);
            poMain.setTotalQty(totalQty);
            poMainMapper.updateById(poMain);
        } else {//第一次确认后 将合同版本表的最新数据同步至合同表
            PoMainVDO poMainV = mainVList.get(0);
            poMainV.setFlowstus("M");
            poMainV.setPostus("M");
            PoMainDO poMain = BeanUtils.toBean(poMainV, PoMainDO.class);
            poMain.setId(id);
            poMain.setUpdateEmpno(getLoginUserNickname());
            poMain.setUpdater(String.valueOf(getLoginUserId()));
            poMain.setUpdateDate(DateUtil.getDate());

            List<PoDetailVDO> poDetailVDOs = poDetailVMapper.selectListByParentId(poMainV.getId());
            List<PoTocVDO> poTocVDOs = poTocVMapper.selectListByParentId(poMainV.getId());
            Long parentId = poMain.getId();
            if (poDetailVDOs != null && poDetailVDOs.size() > 0) {
                poDetailMapper.deleteByParentId(parentId);
                for (PoDetailVDO detailVDO : poDetailVDOs) {
                    detailVDO.setIsConfirm("Y");//确认后 更新是否确认状态为Y
                    poDetailVMapper.updateById(detailVDO);
                    PoDetailDO detailDO = BeanUtils.toBean(detailVDO, PoDetailDO.class);
                    detailDO.setId(null);
                    detailDO.setParentId(parentId);
                    detailDO.setUpdater(String.valueOf(getLoginUserId()));
                    detailDO.setUpdateEmpno(getLoginUserNickname());
                    detailDO.setUpdateDate(DateUtil.getDate());
                    BigDecimal amt = detailDO.getAmt();
                    if (amt == null) {
                        amt = new BigDecimal(0);
                    }
                    BigDecimal qty = detailDO.getQty();
                    if (qty == null) {
                        qty = new BigDecimal(0);
                    }
                    totalAmt = totalAmt.add(amt);
                    totalQty = totalQty.add(qty);
                    poDetailList.add(detailDO);
                }
                poDetailMapper.insert(poDetailList);
            }
            if (poTocVDOs != null && poTocVDOs.size() > 0) {
                poTocMapper.deleteByParentId(parentId);
                for (PoTocVDO poTocVDO : poTocVDOs) {
                    PoTocDO poTocDO = BeanUtils.toBean(poTocVDO, PoTocDO.class);
                    poTocDO.setId(null);
                    poTocDO.setParentId(parentId);
                    poTocDO.setUpdater(String.valueOf(getLoginUserId()));
                    poTocDO.setUpdateEmpno(getLoginUserNickname());
                    poTocDO.setUpdateDate(DateUtil.getDate());
                    poTocList.add(poTocDO);
                }
                poTocMapper.insert(poTocList);
                for (PoTocDO poTocDO : poTocList) {
                    this.linkPoTocContent(poTocDO, poMain);
                }
            }
            poMainV.setTotalAmt(totalAmt);
            poMainV.setTotalQty(totalQty);
            poMainVMapper.updateById(poMainV);
            poMain.setTotalAmt(totalAmt);
            poMain.setTotalQty(totalQty);
            poMainMapper.updateById(poMain);
        }
    }

    // ==================== 子表（合同明细） ====================

    @Override
    public PageResult<PoDetailDO> getPoDetailPage(PoDetailPageReqVO pageReqVO) {
        return poDetailMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<PoDetailRespVO> getPoDetailPage2(PoDetailPageReqVO pageReqVO) {
        IPage<PoDetailRespVO> pageResult = poDetailMapper.selectPage2(
                MyBatisUtils.buildPage(pageReqVO),
                pageReqVO.getPono(),
                pageReqVO.getMatrlno(),
                pageReqVO.getMatrlname());
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }

    @Override
    public PageResult<PoDetailRespVO> getPoDetailConfirmPage(PoDetailPageReqVO pageReqVO) {
        IPage<PoDetailRespVO> pageResult = poDetailMapper.selectPoDetailConfirmPage(
                MyBatisUtils.buildPage(pageReqVO),
                pageReqVO.getPono(),
                pageReqVO.getPoitemno(),
                pageReqVO.getMatrlno(),
                pageReqVO.getMatrlname());
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }

    @Override
    public PageResult<PoDetailRespVO> getPoDetailYPage(PoDetailPageReqVO pageReqVO) {
        IPage<PoDetailRespVO> pageResult = poDetailMapper.selectPoDetailYPage(
                MyBatisUtils.buildPage(pageReqVO),
                pageReqVO.getPono(),
                pageReqVO.getPoitemno(),
                pageReqVO.getMatrlno(),
                pageReqVO.getMatrlname());
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }


    @Override
    @Transactional
    public String createChildPo(Long id) {
        PoMainDO poMain = getPoMain(id);
        BigDecimal totalAmt = new BigDecimal(0);
        BigDecimal totalQty = new BigDecimal(0);
        List<PoDetailDO> poDetailList = new ArrayList<>();
        List<PoTocDO> poTocList = new ArrayList<>();
        List<PoDetailDO> poDetailDOs = poDetailMapper.selectListByParentId(id);
        List<PoTocDO> poTocDOs = poTocMapper.selectListByParentId(id);

        // 生成订购单号 校验唯一
        String pono = noRedisDAO.generate(PmsNoRedisDAO.PO_MAIN_NO_PREFIX);
        if (poMainMapper.selectByPono(pono) != null) {
            throw exception(PO_NO_EXISTS);
        }
        PoMainDO newPoMain = BeanUtils.toBean(poMain, PoMainDO.class);
        newPoMain.setId(null);
        newPoMain.setFlowstus("0");
        newPoMain.setPostus("0");
        newPoMain.setPotype("A");
        newPoMain.setSignplace(null);
        newPoMain.setContractno(poMain.getPono());
        newPoMain.setProject(poMain.getProject());
        newPoMain.setCompid(poMain.getCompid());
        newPoMain.setPono(pono);
        newPoMain.setMemo("该合同为子约合同：长约编号为：" + poMain.getPono());
        newPoMain.setTotalAmt(totalAmt);
        newPoMain.setTotalQty(totalQty);
        newPoMain.setCreateEmpno(getLoginUserNickname());
        newPoMain.setUpdateEmpno(getLoginUserNickname());
        newPoMain.setCreator(String.valueOf(getLoginUserId()));
        newPoMain.setUpdater(String.valueOf(getLoginUserId()));
        newPoMain.setCreateDate(DateUtil.getDate());
        newPoMain.setUpdateDate(DateUtil.getDate());
        poMainMapper.insert(newPoMain);

        Long parentId = newPoMain.getId();
        if (poDetailDOs != null && poDetailDOs.size() > 0) {
            for (PoDetailDO detailDO : poDetailDOs) {
                PoDetailDO newPoDetailDO = BeanUtils.toBean(detailDO, PoDetailDO.class);
                newPoDetailDO.setId(null);
                newPoDetailDO.setProject(poMain.getProject());
                newPoDetailDO.setCompid(poMain.getCompid());
                newPoDetailDO.setParentId(parentId);
                newPoDetailDO.setCreator(String.valueOf(getLoginUserId()));
                newPoDetailDO.setUpdater(String.valueOf(getLoginUserId()));
                newPoDetailDO.setCreateEmpno(getLoginUserNickname());
                newPoDetailDO.setUpdateEmpno(getLoginUserNickname());
                newPoDetailDO.setCreateDate(DateUtil.getDate());
                newPoDetailDO.setUpdateDate(DateUtil.getDate());
                poDetailList.add(newPoDetailDO);
            }
            poDetailMapper.insert(poDetailList);
        }
        if (poTocDOs != null && poTocDOs.size() > 0) {
            for (PoTocDO poTocDO : poTocDOs) {
                PoTocDO newPoTocDO = BeanUtils.toBean(poTocDO, PoTocDO.class);
                newPoTocDO.setId(null);
                newPoTocDO.setParentId(parentId);
                newPoTocDO.setCreator(String.valueOf(getLoginUserId()));
                newPoTocDO.setUpdater(String.valueOf(getLoginUserId()));
                newPoTocDO.setCreateEmpno(getLoginUserNickname());
                newPoTocDO.setUpdateEmpno(getLoginUserNickname());
                newPoTocDO.setCreateDate(DateUtil.getDate());
                newPoTocDO.setUpdateDate(DateUtil.getDate());
                poTocList.add(newPoTocDO);
            }
            poTocMapper.insert(poTocList);
        }
        newPoMain.setTotalAmt(totalAmt);
        newPoMain.setTotalQty(totalQty);
        poMainMapper.updateById(newPoMain);
        return pono;
    }

    @Override
    public List<PoDetailDO> getPoDetailPageFee(String pono) {
        return poDetailMapper.selectListByPono(pono);
    }

    @Override
    public void endCasePo(Long id, String flag) {
        StringBuffer message = new StringBuffer("");
        PoMainDO mainDO = this.getPoMain(id);
        String pono = mainDO.getPono();
        if ("Y".equals(flag)) {
            List<PoDetailDO> poDetailList = poDetailMapper.selectList(PoDetailDO::getPono, pono);
            List<InvoiceInfoDO> invoiceList = invoiceInfoMapper.selectList(InvoiceInfoDO::getPoNo, pono);
            List<EpDetailDO> epDetailList = epDetailMapper.selectList(EpDetailDO::getPono, pono);
            if (poDetailList == null || poDetailList.size() == 0) {
                throw exception(PO_DETAIL_NOT_EXISTS);
            }
            if (invoiceList == null || invoiceList.size() == 0) {
                throw exception(PO_INVOICE_NOT_EXISTS);
            }
            if (epDetailList == null || epDetailList.size() == 0) {
                throw exception(PO_EP_DETAIL_NOT_EXISTS);
            }
            for (PoDetailDO poDetailDO : poDetailList) {
                boolean finalFlag = false;
                for (InvoiceInfoDO invoiceInfoDO : invoiceList) {
                    if (poDetailDO.getPoitemno().equals(invoiceInfoDO.getPoItemNo()) && "C".equals(invoiceInfoDO.getVoucherType())) {
                        finalFlag = true;
                        boolean invoiceFlag = false;
                        for (EpDetailDO epDetailDO : epDetailList) {
                            if (epDetailDO.getSrlno().equals(invoiceInfoDO.getApNo()) && "Y".equals(epDetailDO.getStus())) {
                                invoiceFlag = true;
                            }
                        }
                        if (!invoiceFlag) {
                            message.append("该合同项次：" + poDetailDO.getPoitemno() + "，料号：" + poDetailDO.getMatrlno() + "存在未支付账款");
                        }
                    }
                }
                if (!finalFlag) {
                    message.append("该合同项次：" + poDetailDO.getPoitemno() + "，料号：" + poDetailDO.getMatrlno() + "的最终发票信息不存在");
                }
            }

            if (StringUtils.isNotBlank(message)) {
                throw exception(PO_END_CASE_FAIL, message);
            }
            mainDO.setPostus("X");
            mainDO.setFlowstus("X");
            mainDO.setCloseDate(DateUtil.getDate());
        }
        if ("N".equals(flag)) {
            mainDO.setPostus("M");
            mainDO.setFlowstus("M");
            mainDO.setCloseDate(null);
        }
        mainDO.setUpdateEmpno(getLoginUserNickname());
        mainDO.setUpdater(String.valueOf(getLoginUserId()));
        mainDO.setUpdateDate(DateUtil.getDate());
        poMainMapper.updateById(mainDO);
    }

    @Override
    public Long createPoDetail(PoDetailDO poDetail) {
        Long parentId = poDetail.getParentId();
        validatePoMainExists(parentId);
        PoMainDO mainDO = this.getPoMain(parentId);
        String serialNo = poDetailMapper.getMaxPoItemNoByParentId(parentId);
        String poItemNo = "0000";
        if (serialNo != null) {
            poItemNo = String.format("%04d", Integer.valueOf(serialNo) + 1);
        }
        if ("pomainy".equals(mainDO.getAppid())) {
            //运杂费合同时 不能新增
            List<PoDetailDO> poDetailDOS = poDetailMapper.selectListByParentId(parentId);
            boolean containsValue = poDetailDOS.stream()
                    .anyMatch(detail -> poDetail.getCosttype().equals(detail.getCosttype()));
            if (containsValue) {
                throw exception(PO_DETAIL_PURITEMNO_EXISTS);
            }
        }
        poDetail.setProject(mainDO.getProject());
        poDetail.setCompid(mainDO.getCompid());
        poDetail.setPono(mainDO.getPono());
        poDetail.setPoitemno(poItemNo);
        poDetail.setCreator(String.valueOf(getLoginUserId()));
        poDetail.setUpdater(String.valueOf(getLoginUserId()));
        poDetail.setCreateEmpno(getLoginUserNickname());
        poDetail.setUpdateEmpno(getLoginUserNickname());
        poDetail.setCreateDate(DateUtil.getDate());
        poDetail.setUpdateDate(DateUtil.getDate());
        poDetailMapper.insert(poDetail);
        return poDetail.getId();
    }

    @Override
    public void updatePoDetail(PoDetailDO poDetail) {
        // 校验存在
        validatePoDetailExists(poDetail.getId());
        poDetail.setUpdateEmpno(getLoginUserNickname());
        poDetail.setUpdater(String.valueOf(getLoginUserId()));
        poDetail.setUpdateDate(DateUtil.getDate());

        PoMainDO mainDO = poMainMapper.selectById(poDetail.getParentId());
        String istax = mainDO.getIstax();
        BigDecimal taxrate = mainDO.getTaxrate(); // 假设税率字段为taxrate
        if (!"pomainy".equals(mainDO.getAppid())) {
            if ("Y".equals(istax)) {
                // 含税模式下，保持含税单价不变，重新计算不含税单价
                if (poDetail.getUnitprice() != null && taxrate != null) {
                    BigDecimal unitprice = poDetail.getUnitprice();
                    // 不含税单价 = 含税单价 / (1 + 税率)
                    BigDecimal unitpriceexdtax = unitprice.divide(
                            BigDecimal.ONE.add(taxrate.divide(new BigDecimal("100"), 4, BigDecimal.ROUND_HALF_UP)),
                            4, BigDecimal.ROUND_HALF_UP
                    );
                    poDetail.setUnitpriceexdtax(unitpriceexdtax);
                }
            } else {
                // 不含税模式下，保持不含税单价不变，重新计算含税单价
                if (poDetail.getUnitpriceexdtax() != null && taxrate != null) {
                    BigDecimal unitpriceexdtax = poDetail.getUnitpriceexdtax();
                    // 含税单价 = 不含税单价 * (1 + 税率)
                    BigDecimal unitprice = unitpriceexdtax.multiply(
                            BigDecimal.ONE.add(taxrate.divide(new BigDecimal("100"), 4, BigDecimal.ROUND_HALF_UP))
                    );
                    poDetail.setUnitprice(unitprice);
                }
            }
            // 更新总金额（无论哪种模式都需要更新）
            if (poDetail.getQty() != null) {
                BigDecimal amt = poDetail.getUnitprice().multiply(poDetail.getQty());
                poDetail.setAmt(amt);
            }
        }
        poDetailMapper.updateById(poDetail);

    }

    @Override
    public void deletePoDetail(Long id) {
        // 校验存在
        validatePoDetailExists(id);
        // 删除
        poDetailMapper.deleteById(id);
    }

    @Override
    public PoDetailDO getPoDetail(Long id) {
        return poDetailMapper.selectById(id);
    }

    private void validatePoDetailExists(Long id) {
        if (poDetailMapper.selectById(id) == null) {
            throw exception(PO_DETAIL_NOT_EXISTS);
        }
    }

    private void deletePoDetailByParentId(Long parentId) {
        poDetailMapper.deleteByParentId(parentId);
    }

    // ==================== 子表（合同条款内容） ====================

    @Override
    public PageResult<PoTocDO> getPoTocPage(PageParam pageReqVO, Long parentId) {
        return poTocMapper.selectPage(pageReqVO, parentId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createPoToc(PoTocDO poToc) {
        Long parentId = poToc.getParentId();
        validatePoMainExists(parentId);
        PoMainDO mainDO = this.getPoMain(parentId);
        poToc.setProject(mainDO.getProject());
        poToc.setCompid(mainDO.getCompid());
        poToc.setPono(mainDO.getPono());
        poToc.setCreator(String.valueOf(getLoginUserId()));
        poToc.setUpdater(String.valueOf(getLoginUserId()));
        poToc.setCreateEmpno(getLoginUserNickname());
        poToc.setUpdateEmpno(getLoginUserNickname());
        poToc.setCreateDate(DateUtil.getDate());
        poToc.setUpdateDate(DateUtil.getDate());
        poTocMapper.insert(poToc);

        this.linkPoTocContent(poToc, mainDO);
        return poToc.getId();
    }

    // 将合同内容，替换后，按行存库。  便于合同打印时，一行一行展示
    private void linkPoTocContent(PoTocDO poToc, PoMainDO mainDO) {
        // 先删除数据
        poTocContentMapper.delete(PoTocContentDO::getParentId, poToc.getParentId());
        // 重新拼接合同内容
        String termContent = poToc.getTermContent();
        String startDate = DateUtil.StringtoString(mainDO.getStartDate(), DatePattern.NORM_DATE_PATTERN, DatePattern.CHINESE_DATE_PATTERN);
        String endDate = DateUtil.StringtoString(mainDO.getEndDate(), DatePattern.NORM_DATE_PATTERN, DatePattern.CHINESE_DATE_PATTERN);
        // 按字段替换内容
        termContent = termContent.replace("$@合同执行期@$", startDate + "~" + endDate);
        termContent = termContent.replace("$@质量标准@$", DictFrameworkUtils.getDictDataLabel("pms_param_level", poToc.getParamlevel()));
        termContent = termContent.replace("$@验收标准@$", DictFrameworkUtils.getDictDataLabel("pms_insp_level", poToc.getInsplevel()));
        termContent = termContent.replace("$@质量保证期@$", DictFrameworkUtils.getDictDataLabel("PMS_PARAM_DATE", poToc.getParamDate()));
        termContent = termContent.replace("$@包装标准@$", DictFrameworkUtils.getDictDataLabel("pms_pack_level", poToc.getPacklevel()));
        termContent = termContent.replace("$@包装物回收@$", DictFrameworkUtils.getDictDataLabel("pms_pack_return_type", poToc.getPackreturntype()));
        termContent = termContent.replace("$@交货方式@$", DictFrameworkUtils.getDictDataLabel("pms_deliver_method", poToc.getDilvytype()));
        termContent = termContent.replace("$@运输方式@$", DictFrameworkUtils.getDictDataLabel("PMS_TRANS_TYPE", poToc.getTranstype()));
        termContent = termContent.replace("$@结算办理@$", DictFrameworkUtils.getDictDataLabel("PMS_SETTLE_TYPE", poToc.getSettletype()));
        termContent = termContent.replace("$@付款方式@$", DictFrameworkUtils.getDictDataLabel("PMS_PAY_TYPE", poToc.getPaytype()));
        // 保存合同条款
        this.saveContentByType(termContent, "termContent", poToc.getParentId());
        // 保存合同补增条款
        this.saveContentByType(poToc.getTermSupplementContent(), "termSupplementContent", poToc.getParentId());
    }

    // 按行保存
    private void saveContentByType(String termContent, String type, Long parentId) {
        if (StringUtils.isEmpty(termContent)) {
            return;
        }
        PoTocContentDO poTocContentDO;
        // 按行保存 -- 方便打印
        String[] termContentArray = termContent.split("\n");
        Integer srlNo = 0;
        for (String itemContent : termContentArray) {
            srlNo++;
            poTocContentDO = new PoTocContentDO();
            poTocContentDO.setParentId(parentId);
            poTocContentDO.setSrlno(srlNo);
            poTocContentDO.setType(type);
            poTocContentDO.setContent(itemContent);
            poTocContentMapper.insert(poTocContentDO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePoToc(PoTocDO poToc) {
        // 校验存在
        validatePoTocExists(poToc.getId());

        poToc.setUpdateEmpno(getLoginUserNickname());
        poToc.setUpdater(String.valueOf(getLoginUserId()));
        poToc.setUpdateDate(DateUtil.getDate());
        // 更新
        poTocMapper.updateById(poToc);
        this.linkPoTocContent(poToc, poMainMapper.selectById(poToc.getParentId()));
    }

    @Override
    public void deletePoToc(Long id) {
        // 校验存在
        validatePoTocExists(id);
        // 删除
        poTocMapper.deleteById(id);
    }

    @Override
    public PoTocDO getPoToc(Long id) {
        return poTocMapper.selectById(id);
    }

    private void validatePoTocExists(Long id) {
        if (poTocMapper.selectById(id) == null) {
            throw exception(PO_TOC_NOT_EXISTS);
        }
    }

    private void deletePoTocByParentId(Long parentId) {
        poTocMapper.deleteByParentId(parentId);
    }

    @Override
    public List<PoMainRespDTO> getPoMainPageByVendor(PoMainReqDTO reqDTO) {
        TenantContextHolder.setTenantId(Long.valueOf(reqDTO.getTenantId()));
        List<PoMainRespDTO> poMainDTOList = new ArrayList<>();
        List<PoMainDO> poMainDOList = poMainMapper.selectListByVendor(reqDTO.getVendorno());
        for (PoMainDO poMainDO : poMainDOList) {
            List<PoDetailDO> poDetailList = poDetailMapper.selectListByParentId(poMainDO.getId());
            PoMainRespDTO poMainDTO = BeanUtils.toBean(poMainDO, PoMainRespDTO.class);
            List<PoDetailRespDTO> poDetailDTOList = BeanUtils.toBean(poDetailList, PoDetailRespDTO.class);
            poMainDTO.setPoDetailList(poDetailDTOList);
            poMainDTOList.add(poMainDTO);
        }
        return poMainDTOList;
    }

    @Override
    public PageResult<PoDetailDO> getPoDetailPageByParams(PoDetailPageReqVO pageReqVO) {
        return poDetailMapper.selectPoDetailPage(pageReqVO);
    }

    @Override
    public PageResult<PoDetailRespVO> getShipPoDetailPage(PoDetailPageReqVO pageReqVO) {
        IPage<PoDetailRespVO> pageResult = poDetailMapper.selectShipDetailPoPage(
                MyBatisUtils.buildPage(pageReqVO),
                pageReqVO.getMatrlno(),
                pageReqVO.getMatrlname(),
                pageReqVO.getPono(),
                pageReqVO.getVendorno(),
                pageReqVO.getVendorname()
        );
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }


    /**
     * 根据料号和合同号 获得合同明细记录，要求主表状态大于‘F’
     */
    @Override
    public PoDetailDO getDetailByMatrlnoAndPono(String matrlno, String pono) {
        PoDetailDO detail =
                poDetailMapper.selectOne(new LambdaQueryWrapperX<PoDetailDO>().eq(PoDetailDO::getMatrlno, matrlno)
                        .eq(PoDetailDO::getPono, pono).eq(PoDetailDO::getDeleted, false), false);
        if (detail == null) {
            return null;
        }
        PoMainDO pomain = poMainMapper.selectById(detail.getParentId());
        if (!StringUtils.isBlank(pomain.getFlowstus()) && pomain.getFlowstus().compareTo("F") > 0
                && !pomain.getDeleted()) {
            return detail;
        }
        return null;
    }

    @Override
    public String getPoTocCNTotalAmt(Long parentId) {
        TenantContextHolder.setIgnore(true);
        BigDecimal totalAmt = poTocMapper.selectPoTocCNTotalAmt(parentId);
        // 获取大写金额
        String chineseUpper = MoneyUtils.convertToChinese(totalAmt);
        System.out.println("chineseUpper--->" + chineseUpper);
        return chineseUpper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInsertPoDetailsByPurId(PoDetailsBatchInsertReqVO reqVO) {
        Long parentId = reqVO.getParentId();
        PoMainDO poMainDO = poMainMapper.selectById(parentId);
        List<MaterialRespVO> idList = reqVO.getItems();
        String serialNo = poDetailMapper.getMaxPoItemNoByParentId(parentId);
        String poItemNo = "0000";
        if (serialNo != null) {
            poItemNo = String.format("%04d", Integer.valueOf(serialNo) + 1);
        }
        Set<Long> ids = convertSet(idList, MaterialRespVO::getId);
        List<MppPurchasedDetailDO> mppPurchasedDetailDOS = mppPurchasedDetailMapper.selectListById(ids);
        for (MppPurchasedDetailDO mppPurchasedDetailDO : mppPurchasedDetailDOS) {
            for (MaterialRespVO item : idList) {
                if (item.getId().equals(mppPurchasedDetailDO.getId())) {
                    if (item.getQty() == null || (item.getQty().compareTo(new BigDecimal(0)) == 0)) {
                        throw exception(PO_DETAIL_QTY_NOT_EMPTY, mppPurchasedDetailDO.getReqno(), mppPurchasedDetailDO.getReqitemno());
                    }
                    if (item.getUnitprice() == null || (item.getUnitprice().compareTo(new BigDecimal(0)) == 0)) {
                        throw exception(PO_DETAIL_UNIT_PRICE_NOT_EMPTY, mppPurchasedDetailDO.getReqno(), mppPurchasedDetailDO.getReqitemno());
                    }
                    /*
                    // 国贸需求确认 暂不校验请购数量是否超出
                    BigDecimal inpriceqt = mppPurchasedDetailDO.getInpriceqt() == null ? new BigDecimal(0) : mppPurchasedDetailDO.getInpriceqt();
                    if (item.getQty().compareTo(mppPurchasedDetailDO.getQty().subtract(inpriceqt)) > 0) {
                        throw exception(PO_DETAIL_QTY_MORE_THAN, mppPurchasedDetailDO.getReqno(), mppPurchasedDetailDO.getReqitemno());
                    }*/
                    PoDetailDO poDetailDO = new PoDetailDO();
                    poDetailDO.setProject(poMainDO.getProject());
                    poDetailDO.setCompid(poMainDO.getCompid());
                    poDetailDO.setParentId(parentId);
                    poDetailDO.setPono(poMainDO.getPono());
                    poDetailDO.setPoitemno(poItemNo);
                    poDetailDO.setPurno(mppPurchasedDetailDO.getPurno());
                    poDetailDO.setPuritemno(mppPurchasedDetailDO.getPuritemno());
                    poDetailDO.setReqno(mppPurchasedDetailDO.getReqno());
                    poDetailDO.setReqitemno(mppPurchasedDetailDO.getReqitemno());
                    poDetailDO.setMatrlno(mppPurchasedDetailDO.getMatrlno());
                    poDetailDO.setMatrlname(mppPurchasedDetailDO.getCnMdesc());
                    poDetailDO.setSpec((mppPurchasedDetailDO.getChNspec() == null ? "" : mppPurchasedDetailDO.getChNspec()) +
                            (mppPurchasedDetailDO.getQlty() == null ? "" : mppPurchasedDetailDO.getQlty()) +
                            (mppPurchasedDetailDO.getDrawing() == null ? "" : mppPurchasedDetailDO.getDrawing()));
                    poDetailDO.setQlty(mppPurchasedDetailDO.getQlty());
                    poDetailDO.setQty(item.getQty());
                    poDetailDO.setUnitprice(item.getUnitprice());
                    poDetailDO.setAmt(item.getQty().multiply(item.getUnitprice()));
                    poDetailDO.setUnit(mppPurchasedDetailDO.getUnit());
                    poDetailDO.setUsedept(mppPurchasedDetailDO.getUsedept());
                    poDetailDO.setCreator(String.valueOf(getLoginUserId()));
                    poDetailDO.setUpdater(String.valueOf(getLoginUserId()));
                    poDetailDO.setCreateEmpno(getLoginUserNickname());
                    poDetailDO.setUpdateEmpno(getLoginUserNickname());
                    poDetailDO.setCreateDate(DateUtil.getDate());
                    poDetailDO.setUpdateDate(DateUtil.getDate());
                    poDetailMapper.insert(poDetailDO);
                    poItemNo = String.format("%04d", Integer.valueOf(poItemNo) + 1);
                }
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInsertPoDetailsByMatrlId(PoDetailsBatchInsertByMatrlReqVO reqVO) {
        Long parentId = reqVO.getParentId();
        PoMainDO poMainDO = poMainMapper.selectById(parentId);
        List<MaterialRespVO> items = reqVO.getItems();
        String serialNo = poDetailMapper.getMaxPoItemNoByParentId(parentId);
        String poItemNo = "0000";
        if (serialNo != null) {
            poItemNo = String.format("%04d", Integer.valueOf(serialNo) + 1);
        }
        Set<Long> ids = convertSet(items, MaterialRespVO::getId);
        List<MaterialDO> materialDOS = materialMapper.selectListById(ids);
        for (MaterialDO materialDO : materialDOS) {
            for (MaterialRespVO item : items) {
                if (item.getId().equals(materialDO.getId())) {
                    PoDetailDO poDetailDO = new PoDetailDO();
                    poDetailDO.setProject(poMainDO.getProject());
                    poDetailDO.setCompid(poMainDO.getCompid());
                    poDetailDO.setParentId(parentId);
                    poDetailDO.setPono(poMainDO.getPono());
                    poDetailDO.setPoitemno(poItemNo);
                    poDetailDO.setMatrlno(materialDO.getMatrlno());
                    poDetailDO.setMatrlname(materialDO.getCnmdesc());
                    poDetailDO.setSpec((materialDO.getNmspec() == null ? "" : materialDO.getNmspec()) +
                            (materialDO.getQuality() == null ? "" : materialDO.getQuality()) +
                            (materialDO.getPicno() == null ? "" : materialDO.getPicno()));
                    poDetailDO.setQty(item.getQty());
                    poDetailDO.setQlty(materialDO.getQuality());
                    poDetailDO.setUnitprice(item.getUnitprice());
                    poDetailDO.setAmt(item.getQty().multiply(item.getUnitprice()));
                    poDetailDO.setUnit(materialDO.getUnitinv());
                    poDetailDO.setCreator(String.valueOf(getLoginUserId()));
                    poDetailDO.setUpdater(String.valueOf(getLoginUserId()));
                    poDetailDO.setCreateEmpno(getLoginUserNickname());
                    poDetailDO.setUpdateEmpno(getLoginUserNickname());
                    poDetailDO.setCreateDate(DateUtil.getDate());
                    poDetailDO.setUpdateDate(DateUtil.getDate());
                    poDetailMapper.insert(poDetailDO);
                    poItemNo = String.format("%04d", Integer.valueOf(poItemNo) + 1);
                }
            }
        }
    }

    @Override
    public PageResult<PoPayInfoRespVO> getPayInfo(PoDetailPageReqVO pageReqVO) {
        Long parentId = pageReqVO.getParentId();
        PoMainDO poMainDO = poMainMapper.selectById(parentId);
        IPage<PoPayInfoRespVO> pageResult = poMainMapper.selectPayInfoList(
                MyBatisUtils.buildPage(pageReqVO),
                poMainDO.getPono()
        );
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }

    @Override
    public PageResult<PoPayInfoRespVO> getPayInfoY(PoDetailPageReqVO pageReqVO) {
        Long parentId = pageReqVO.getParentId();
        PoMainDO poMainDO = poMainMapper.selectById(parentId);
        IPage<PoPayInfoRespVO> pageResult = poMainMapper.selectPayInfoYList(
                MyBatisUtils.buildPage(pageReqVO),
                poMainDO.getPono()
        );
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }

    @Override
    public List<PoMonthlyRespVO> getMonthly(String startDate, String endDate) {
        List<PoMonthlyRespVO> data = poMainMapper.selectMonthlyList(startDate, endDate);
        return data;
    }

    @Override
    public List<PoUpComingRespVO> getUpcoming() {
        List<PoUpComingRespVO> data = poMainMapper.selectUpComingList();
        return data;
    }

    @Override
    public List<PoUpComingRespVO> getOneDayPayInfo(String day) {
        List<PoUpComingRespVO> data = poMainMapper.selectOneDayPayInfo(day);
        return data;
    }

    @Override
    public Tbso011DO getOrderInfo(Long parentId) {
        return tbso011Mapper.selectOne(Tbso011DO::getParentId, parentId);
    }

    @Override
    @Transactional
    public Long createOrderInfo(Tbso011DO tbso011DO) {
        if (tbso011Mapper.selectOne(Tbso011DO::getParentId, tbso011DO.getParentId()) != null) {
            throw exception(ORDER_INFO_EXISTS);
        }
        tbso011DO.setParentId(tbso011DO.getParentId());
        tbso011DO.setCreEmpNo(String.valueOf(getLoginUserId()));
        tbso011DO.setCreDate(DateUtil.getDate());
        tbso011DO.setUptEmpNo(String.valueOf(getLoginUserId()));
        tbso011DO.setUptDate(DateUtil.getDate());
        tbso011DO.setCreator(String.valueOf(getLoginUserNickname()));
        tbso011DO.setUpdater(String.valueOf(getLoginUserNickname()));
        tbso011Mapper.insert(tbso011DO);

        List<ContractTradeDetailDO> contractDetailList = contractTradeDetailMapper.selectList(ContractTradeDetailDO::getContractTradeId, tbso011DO.getContractNo());
        for (ContractTradeDetailDO contractTradeDetailDO : contractDetailList) {
            Tbso012DO updateObj = BeanUtils.toBean(contractTradeDetailDO, Tbso012DO.class);
            updateObj.setId(null);
            updateObj.setParentId(tbso011DO.getParentId());
            tbso012Mapper.insert(updateObj);
        }
        return tbso011DO.getId();
    }

    @Override
    public void updateOrderInfo(Tbso011DO tbso011DO) {
        if (tbso011Mapper.selectOne(Tbso011DO::getParentId, tbso011DO.getParentId()) == null) {
            throw exception(ORDER_INFO_EXISTS);
        }
        tbso011DO.setCreEmpNo(String.valueOf(getLoginUserId()));
        tbso011DO.setCreDate(DateUtil.getDate());
        tbso011DO.setUptEmpNo(String.valueOf(getLoginUserId()));
        tbso011DO.setUptDate(DateUtil.getDate());
        tbso011DO.setCreator(String.valueOf(getLoginUserNickname()));
        tbso011DO.setUpdater(String.valueOf(getLoginUserNickname()));
        tbso011Mapper.updateById(tbso011DO);
    }

    @Override
    public Tbso012DO getOrderDetail(Long id) {
        return tbso012Mapper.selectById(id);
    }

    @Override
    public Long createOrderDetail(Tbso012DO tbso012DO) {
        Long parentId = tbso012DO.getParentId();
        validatePoMainExists(parentId);
        tbso012DO.setParentId(parentId);
        tbso012DO.setCreEmpNo(String.valueOf(getLoginUserId()));
        tbso012DO.setCreDate(DateUtil.getDate());
        tbso012DO.setUptEmpNo(String.valueOf(getLoginUserId()));
        tbso012DO.setUptDate(DateUtil.getDate());
        tbso012DO.setCreator(String.valueOf(getLoginUserNickname()));
        tbso012DO.setUpdater(String.valueOf(getLoginUserNickname()));
        tbso012Mapper.insert(tbso012DO);
        return tbso012DO.getId();
    }

    @Override
    public void updateOrderDetail(Tbso012DO tbso012DO) {
        if (tbso012Mapper.selectById(tbso012DO.getId()) == null) {
            throw exception(ORDER_INFO_EXISTS);
        }
        tbso012DO.setCreEmpNo(String.valueOf(getLoginUserId()));
        tbso012DO.setCreDate(DateUtil.getDate());
        tbso012DO.setUptEmpNo(String.valueOf(getLoginUserId()));
        tbso012DO.setUptDate(DateUtil.getDate());
        tbso012DO.setCreator(String.valueOf(getLoginUserNickname()));
        tbso012DO.setUpdater(String.valueOf(getLoginUserNickname()));
        tbso012Mapper.updateById(tbso012DO);
    }

    @Override
    public PageResult<Tbso012DO> getOrderDetailPage(PageParam pageReqVO, Long parentId) {
        return tbso012Mapper.selectPage(pageReqVO, parentId);
    }

    @Override
    @Transactional
    public void batchInsertPoDetailByInq(PoDetailsBatchInsertReqVO reqVO) {
        Long parentId = reqVO.getParentId();
        PoMainDO poMainDO = poMainMapper.selectById(parentId);
        List<MaterialRespVO> idList = reqVO.getItems();
        String serialNo = poDetailMapper.getMaxPoItemNoByParentId(parentId);
        String poItemNo = "0000";
        if (serialNo != null) {
            poItemNo = String.format("%04d", Integer.valueOf(serialNo) + 1);
        }
        Set<Long> ids = convertSet(idList, MaterialRespVO::getId);
        List<InqDetailDO> inqDetailDOS = inqDetailMapper.selectListByIds(ids);
        for (InqDetailDO inqDetailDO : inqDetailDOS) {
            for (MaterialRespVO item : idList) {
                if (item.getId().equals(inqDetailDO.getId())) {
                    if (item.getQty() == null || (item.getQty().compareTo(new BigDecimal(0)) == 0)) {
                        throw exception(PO_DETAIL_QTY_NOT_EMPTY, inqDetailDO.getInqId(), inqDetailDO.getInqLineId());
                    }
                    if (item.getUnitprice() == null || (item.getUnitprice().compareTo(new BigDecimal(0)) == 0)) {
                        throw exception(PO_DETAIL_UNIT_PRICE_NOT_EMPTY, inqDetailDO.getInqId(), inqDetailDO.getInqLineId());
                    }
                    /*
                    // 国贸需求确认 暂不校验请购数量是否超出
                    BigDecimal inpriceqt = mppPurchasedDetailDO.getInpriceqt() == null ? new BigDecimal(0) : mppPurchasedDetailDO.getInpriceqt();
                    if (item.getQty().compareTo(mppPurchasedDetailDO.getQty().subtract(inpriceqt)) > 0) {
                        throw exception(PO_DETAIL_QTY_MORE_THAN, mppPurchasedDetailDO.getReqno(), mppPurchasedDetailDO.getReqitemno());
                    }*/
                    PoDetailDO poDetailDO = new PoDetailDO();
                    poDetailDO.setProject(poMainDO.getProject());
                    poDetailDO.setCompid(poMainDO.getCompid());
                    poDetailDO.setParentId(parentId);
                    poDetailDO.setPono(poMainDO.getPono());
                    poDetailDO.setPoitemno(poItemNo);
                    poDetailDO.setPurno(inqDetailDO.getInqId());
                    poDetailDO.setPuritemno(inqDetailDO.getInqLineId());
                    poDetailDO.setReqitemno(inqDetailDO.getMrLineId());
                    poDetailDO.setMatrlno(inqDetailDO.getItemId());
                    poDetailDO.setMatrlname(inqDetailDO.getItemName());
                    poDetailDO.setSpec(inqDetailDO.getItemDesc());
                    poDetailDO.setQty(item.getQty());
                    poDetailDO.setUnitprice(item.getUnitprice());
                    poDetailDO.setAmt(item.getQty().multiply(item.getUnitprice()));
                    poDetailDO.setUnit(inqDetailDO.getItemUom());
                    poDetailDO.setUsedept(inqDetailDO.getDeptId());
                    poDetailDO.setCreator(String.valueOf(getLoginUserId()));
                    poDetailDO.setUpdater(String.valueOf(getLoginUserId()));
                    poDetailDO.setCreateEmpno(getLoginUserNickname());
                    poDetailDO.setUpdateEmpno(getLoginUserNickname());
                    poDetailDO.setCreateDate(DateUtil.getDate());
                    poDetailDO.setUpdateDate(DateUtil.getDate());
                    poDetailMapper.insert(poDetailDO);
                    poItemNo = String.format("%04d", Integer.valueOf(poItemNo) + 1);
                }
            }
        }
    }

    @Override
    public void pushErpPo(Long id) {
        PoMainDO poMainDO = poMainMapper.selectById(id);
        Tbso011DO tbso011DO = tbso011Mapper.selectOne(Tbso011DO::getParentId, id);
        List<Tbso012DO> tbso012DOList = tbso012Mapper.selectList(Tbso012DO::getParentId, id);
        List<Tbso013DO> tbso013DOList = tbso013Mapper.selectList(Tbso013DO::getParentId, id);

        PushErpPoReqDTO erpDTO = new PushErpPoReqDTO();
        poMainDO.setSubmitType("add");
        erpDTO.setPoMainList(poMainDO);
        if (StringUtils.isNotBlank(tbso011DO.getOrderMonth())) {
            tbso011DO.setOrderMonth(tbso011DO.getOrderMonth().replace("-", ""));
        }
        if (StringUtils.isNotBlank(tbso011DO.getCreDate())) {
            tbso011DO.setCreDate(tbso011DO.getCreDate().replace("-", ""));
        }
        if (StringUtils.isNotBlank(tbso011DO.getUptDate())) {
            tbso011DO.setUptDate(tbso011DO.getUptDate().replace("-", ""));
        }

        erpDTO.setOrderMainList(tbso011DO);
        erpDTO.setOrderDetailList(tbso012DOList);
        erpDTO.setOrderSpecList(tbso013DOList);
        String jsonString = JsonUtils.toJsonString(erpDTO);
        System.out.println("JSON字符串: " + jsonString);
        // 格式化输出
        String prettyJson = JsonUtils.toJsonPrettyString(erpDTO);
        System.out.println("格式化JSON:");
        System.out.println(prettyJson);
        String bodyStr = prettyJson;
        PostUtil postUtil = new PostUtil();
        String response = postUtil.post("GM_SCM_CONTRACT_LIST", bodyStr);
        JSON.parseObject(response, PoMainDO.class);
    }

    @Override
    @Transactional
    public void batchInserOrderDetail(PoDetailsBatchInsertReqVO reqVO) {
        Long parentId = reqVO.getParentId();
        PoMainDO poMainDO = poMainMapper.selectById(parentId);
        List<MaterialRespVO> idList = reqVO.getItems();
        String serialNo = poDetailMapper.getMaxPoItemNoByParentId(parentId);
        String poItemNo = "0000";
        if (serialNo != null) {
            poItemNo = String.format("%04d", Integer.valueOf(serialNo) + 1);
        }
        Tbso011DO tbso011DO = tbso011Mapper.selectOne(Tbso011DO::getParentId, parentId);
        Set<Long> ids = convertSet(idList, MaterialRespVO::getId);
        List<ContractTradeDetailDO> contractDetailList = contractTradeDetailMapper.selectListByIds(ids);
        for (ContractTradeDetailDO contractTradeDetailDO : contractDetailList) {
            Tbso012DO updateObj = BeanUtils.toBean(contractTradeDetailDO, Tbso012DO.class);
            updateObj.setId(null)
                    .setPoItemNo(poItemNo)
                    .setParentId(parentId)
                    .setOrderNo(contractTradeDetailDO.getContractTradeId())
                    .setOrderItem(contractTradeDetailDO.getContractTradeDetailId())
                    .setCompId(tbso011DO.getCompId())
                    .setProdClass(tbso011DO.getProdClass())
                    .setProdType(tbso011DO.getProdType())
                    //.setProdDetailClass(tbso011DO.getProductFormName())
                    .setPsrNo(contractTradeDetailDO.getStandard())
                    .setPsrNoMes(contractTradeDetailDO.getStandardMes())
                    .setProdSpecNo(contractTradeDetailDO.getShopSign())
                    .setShippingDate(tbso011DO.getShippingDate())
                    .setOrderThickScreen(new BigDecimal(contractTradeDetailDO.getThickness()))
                    .setOrderThickUnit("mm")
                    .setOrderWidthScreen(new BigDecimal(contractTradeDetailDO.getWidth()))
                    .setOrderWidthUnit("mm")
                    .setOrderLengthScreen(new BigDecimal(contractTradeDetailDO.getLength()))
                    .setOrderLengthUnit("mm")
                    .setSizeMsg(contractTradeDetailDO.getSizeInfo())
                    .setSpecMark(contractTradeDetailDO.getSpecification())
                    .setShipCtrlMode(contractTradeDetailDO.getControlType())
                    .setOrderWeight(new BigDecimal(contractTradeDetailDO.getOrderWeight()))
                    .setOrderQty(new BigDecimal(contractTradeDetailDO.getOrderCount()))
                    .setSpecialCode(contractTradeDetailDO.getSpecialProof())
                    .setMemo(contractTradeDetailDO.getRemark());
            tbso012Mapper.insert(updateObj);
//                      .setShipMinWidth(new BigDecimal(contractTradeDetailDO.getWidthBoundStart()))
//                    .setShipMaxWidth(new BigDecimal(contractTradeDetailDO.getWidthBoundEnd()))
//                    .setShipMinLength(new BigDecimal(contractTradeDetailDO.getLengthBoundStart()))
//                    .setShipMaxLength(new BigDecimal(contractTradeDetailDO.getLengthBoundEnd()))
//                    .setUnitPrice(new BigDecimal(contractTradeDetailDO.getClearanceAveragePrice()))
//                    .setFobUnitPrice(new BigDecimal(contractTradeDetailDO.getFobPrice()))
//                    .setLockOrderPrice(contractTradeDetailDO.getRecordPrice())
//                    .setFreightPrice(new BigDecimal(contractTradeDetailDO.getCostSingle()))
//                    .setFreightAmount(new BigDecimal(contractTradeDetailDO.getCostMake()))
//                    .setFirstCfmDate(contractTradeDetailDO.getRecordTime() != null ? contractTradeDetailDO.getRecordTime().toString() : null)
//                    .setProdAmount(new BigDecimal(contractTradeDetailDO.getCostComplete()))


            PoDetailDO poDetailDO = new PoDetailDO();
            poDetailDO.setProject(poMainDO.getProject())
                    .setCompid(poMainDO.getCompid())
                    .setParentId(parentId)
                    .setPono(poMainDO.getPono())
                    .setPoitemno(poItemNo)
                    .setOrderNo(contractTradeDetailDO.getContractTradeId())
                    .setOrderItem(contractTradeDetailDO.getContractTradeDetailId())
                    .setMatrlno(tbso011DO.getGroupCode() + "-" + contractTradeDetailDO.getStandard() + "-" + contractTradeDetailDO.getSpecification())
                    .setMatrlname(tbso011DO.getProductFormName())
                    .setSpec(contractTradeDetailDO.getSpecification())
                    .setQty(new BigDecimal(contractTradeDetailDO.getOrderCount()))
                    .setUnitprice(new BigDecimal(contractTradeDetailDO.getSellPrice()))
                    .setAmt(poDetailDO.getQty().multiply(poDetailDO.getUnitprice()))
                    .setUnit("_T")
                    .setCreateEmpno(getLoginUserNickname())
                    .setUpdateEmpno(getLoginUserNickname())
                    .setCreator(String.valueOf(getLoginUserId()))
                    .setUpdater(String.valueOf(getLoginUserId()));

            poDetailMapper.insert(poDetailDO);
            poItemNo = String.format("%04d", Integer.valueOf(poItemNo) + 1);
        }
    }

    @Override
    @Transactional
    public void deleteOrderDetail(Long id) {
        Tbso012DO tbso012DO = tbso012Mapper.selectById(id);
        if (tbso012DO == null) {
            throw exception(ORDER_DETAIL_NOT_EXISTS);
        }
        PoDetailDO poDetailDO = poDetailMapper.selectOne(PoDetailDO::getParentId, tbso012DO.getParentId(), PoDetailDO::getPoitemno, tbso012DO.getPoItemNo());
        poDetailMapper.deleteById(poDetailDO);
        tbso012Mapper.deleteById(id);
    }

    @Override
    public PageResult<PoDetailRespVO> getDetailPage(PoDetailPageReqVO pageReqVO) {
        IPage<PoDetailRespVO> pageResult = poDetailMapper.selectDetailPoPage(
                MyBatisUtils.buildPage(pageReqVO),
                pageReqVO.getAppid(),
                pageReqVO.getMatrlno(),
                pageReqVO.getMatrlname(),
                pageReqVO.getPono(),
                pageReqVO.getVendorno(),
                pageReqVO.getVendorname()
        );
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }


}
package cn.iocoder.yudao.module.pms.controller.admin.demandplanaudit;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.pms.controller.admin.demandplanaudit.vo.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.demandplanaudit.DemandPlanAuditDO;
import cn.iocoder.yudao.module.pms.service.demandplanaudit.DemandPlanAuditService;

@Tag(name = "管理后台 - 需求计划申报工作流关联")
@RestController
@RequestMapping("/pms/demand-plan-audit")
@Validated
public class DemandPlanAuditController {

    @Resource
    private DemandPlanAuditService demandPlanAuditService;

    @PostMapping("/create")
    @Operation(summary = "创建需求计划申报工作流关联")
    @PreAuthorize("@ss.hasPermission('pms:demand-plan-audit:create')")
    public CommonResult<Long> createDemandPlanAudit(@Valid @RequestBody DemandPlanAuditSaveReqVO createReqVO) {
        return success(demandPlanAuditService.createDemandPlanAudit(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新需求计划申报工作流关联")
    @PreAuthorize("@ss.hasPermission('pms:demand-plan-audit:update')")
    public CommonResult<Boolean> updateDemandPlanAudit(@Valid @RequestBody DemandPlanAuditSaveReqVO updateReqVO) {
        demandPlanAuditService.updateDemandPlanAudit(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除需求计划申报工作流关联")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:demand-plan-audit:delete')")
    public CommonResult<Boolean> deleteDemandPlanAudit(@RequestParam("id") Long id) {
        demandPlanAuditService.deleteDemandPlanAudit(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得需求计划申报工作流关联")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:demand-plan-audit:query')")
    public CommonResult<DemandPlanAuditRespVO> getDemandPlanAudit(@RequestParam("id") Long id) {
        DemandPlanAuditDO demandPlanAudit = demandPlanAuditService.getDemandPlanAudit(id);
        return success(BeanUtils.toBean(demandPlanAudit, DemandPlanAuditRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得需求计划申报工作流关联分页")
    @PreAuthorize("@ss.hasPermission('pms:demand-plan-audit:query')")
    public CommonResult<PageResult<DemandPlanAuditRespVO>> getDemandPlanAuditPage(@Valid DemandPlanAuditPageReqVO pageReqVO) {
        PageResult<DemandPlanAuditDO> pageResult = demandPlanAuditService.getDemandPlanAuditPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DemandPlanAuditRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出需求计划申报工作流关联 Excel")
    @PreAuthorize("@ss.hasPermission('pms:demand-plan-audit:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportDemandPlanAuditExcel(@Valid DemandPlanAuditPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<DemandPlanAuditDO> list = demandPlanAuditService.getDemandPlanAuditPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "需求计划申报工作流关联.xls", "数据", DemandPlanAuditRespVO.class,
                        BeanUtils.toBean(list, DemandPlanAuditRespVO.class));
    }

}
package cn.iocoder.yudao.module.pms.controller.admin.mpppurchase.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 采购案分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MppPurchasePageReqVO extends PageParam {

    @Schema(description = "项目")
    private String project;

    @Schema(description = "公司代码", example = "515")
    private String compid;

    @Schema(description = "采购案号")
    private String purno;

}
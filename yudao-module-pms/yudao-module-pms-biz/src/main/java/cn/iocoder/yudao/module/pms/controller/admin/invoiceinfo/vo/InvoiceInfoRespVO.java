package cn.iocoder.yudao.module.pms.controller.admin.invoiceinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.*;
import java.math.BigDecimal;

import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 发票信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InvoiceInfoRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "项目")
    @ExcelProperty("项目")
    private String project;

    @Schema(description = "公司代码")
    @ExcelProperty("公司代码")
    private String compId;

    @Schema(description = "申请单号")
    @ExcelProperty("申请单号")
    private String issueNo;

    @Schema(description = "发票号")
    @ExcelProperty("发票号")
    private String voucherNo;

    @Schema(description = "发票日期")
    @ExcelProperty("发票日期")
    private String voucherDate;

    @Schema(description = "发票性质")
    @ExcelProperty("发票性质")
    private String voucherType;

    @Schema(description = "厂商编号")
    @ExcelProperty("厂商编号")
    private String vendorNo;

    @Schema(description = "装运通知单")
    @ExcelProperty("装运通知单")
    private String inputTransNo;

    @Schema(description = "厂商名称")
    @ExcelProperty("厂商名称")
    private String vendorName;

    @Schema(description = "状态")
    @ExcelProperty("状态")
    private String poStus;

    @Schema(description = "部门")
    @ExcelProperty("部门")
    private String issueDept;

    @Schema(description = "录入人")
    @ExcelProperty("录入人")
    private String issueEmpNo;

    @Schema(description = "录入说明")
    @ExcelProperty("录入说明")
    private String memo;

    @Schema(description = "汇率")
    @ExcelProperty("汇率")
    private BigDecimal exchangeRate;

    @Schema(description = "发票金额(原币)")
    @ExcelProperty("发票金额(原币)")
    private BigDecimal prodVoucherAmt;

    @Schema(description = "发票金额(人民币)")
    @ExcelProperty("发票金额(人民币)")
    private BigDecimal prodVoucherAmtRmb;

    @Schema(description = "附件数目")
    @ExcelProperty("附件数目")
    private String uploadFile;

    @Schema(description = "订购合同号")
    @ExcelProperty("订购合同号")
    private String poNo;

    @Schema(description = "物料编号")
    @ExcelProperty("物料编号")
    private String matrlNo;

    @Schema(description = "品名")
    @ExcelProperty("品名")
    private String matrlName;

    @Schema(description = "订购数量")
    @ExcelProperty("订购数量")
    private BigDecimal qty;

    @Schema(description = "单价")
    @ExcelProperty("单价")
    private BigDecimal unitPrice;

    @Schema(description = "暂估流水号档")
    @ExcelProperty("暂估流水号档")
    private String apNo;

    @Schema(description = "订购合同项次号")
    @ExcelProperty("订购合同项次号")
    private String poItemNo;

    @Schema(description = "验收单号(结算单号)")
    @ExcelProperty("验收单号(结算单号)")
    private String inspNo;

    @Schema(description = "验收单项次")
    @ExcelProperty("验收单项次")
    private String inspItemNo;

    @Schema(description = "币别")
    @ExcelProperty("币别")
    private String crcy;

    @Schema(description = "入库单")
    @ExcelProperty("入库单")
    private String miNo;

    @Schema(description = "账务日期")
    @ExcelProperty("账务日期")
    private String vchrDate;

    @Schema(description = "新增人员")
    @ExcelProperty("新增人员")
    private String createEmpNo;

    @Schema(description = "新增日期")
    @ExcelProperty("新增日期")
    private String createDate;

    @Schema(description = "修改人员")
    @ExcelProperty("修改人员")
    private String updateEmpNo;

    @Schema(description = "修改日期")
    @ExcelProperty("修改日期")
    private String updateDate;

    @Schema(description = "新增时间")
    @ExcelProperty("新增时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @ExcelProperty("更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "新增人员")
    @ExcelProperty("新增人员")
    private String creator;

    @Schema(description = "修改人员")
    @ExcelProperty("修改人员")
    private String updater;

    @Schema(description = "流程id")
    private String processId;

    @Schema(description = "附件")
    @ExcelProperty("附件")
    private String annex;

}
package cn.iocoder.yudao.module.pms.controller.admin.accountclose.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 关账日期分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AccountClosePageReqVO extends PageParam {

    @Schema(description = "账务编号")
    private Long id;

    @Schema(description = "项目号")
    private String project;

    @Schema(description = "公司别")
    private String compid;

    @Schema(description = "品别")
    private String inventorytype;

    @Schema(description = "年月")
    private String yearmo;

    @Schema(description = "关帐起日")
    private String firstdate;

    @Schema(description = "关帐止日")
    private String enddate;

    @Schema(description = "关帐是否确定")
    private String stus;

    @Schema(description = "关帐人")
    private String closeempno;

    @Schema(description = "关帐日期")
    private String closedate;

    @Schema(description = "关帐时间")
    private String closetime;

    @Schema(description = "新增时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
package cn.iocoder.yudao.module.pms.dal.dataobject.contractmain;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 合同签章子 DO
 *
 * <AUTHOR>
 */
@TableName("FW_CONTRACT_SEAL")
@KeySequence("fw_contract_seal_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContractSealDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 主表主键
     */
    private Long mainId;
    /**
     * 业务系统合同编码
     */
    private String thirdContractNo;
    /**
     * 业务系统版本号
     */
    private String thirdVersion;
    /**
     * 印章名称
     */
    private String sealName;
    /**
     * 印章编码
     */
    private String sealCode;
    /**
     * 印章类型
     */
    private String sealType;
    /**
     * 印章类型编码
     */
    private String sealTypeId;
    /**
     * 印章管理员
     */
    private String sealAdmin;
    /**
     * 印章管理员编码
     */
    private String sealAdminCode;
    /**
     * 申请用印份数
     */
    private Integer sealNumberOld;
    /**
     * 申请用印枚数
     */
    private Integer printsNumberOld;
    /**
     * 实际用印份数
     */
    private Integer sealNumber;
    /**
     * 实际用印枚数
     */
    private Integer printsNumber;
    /**
     * 我方盖章时间(YYYY-MM-DD HH:MM)
     */
//    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime ourSealDate;
    /**
     * 备注
     */
    private String remark;

}

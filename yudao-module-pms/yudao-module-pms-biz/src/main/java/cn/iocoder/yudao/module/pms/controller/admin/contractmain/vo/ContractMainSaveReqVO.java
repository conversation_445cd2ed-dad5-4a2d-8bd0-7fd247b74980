package cn.iocoder.yudao.module.pms.controller.admin.contractmain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import cn.iocoder.yudao.module.pms.dal.dataobject.contractmain.ContractSealDO;

@Schema(description = "管理后台 - 法务合同基本信息新增/修改 Request VO")
@Data
public class ContractMainSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "30051")
    private Long id;

    @Schema(description = "合同名称", example = "赵六")
    private String contractName;

    @Schema(description = "业务系统合同编码")
    private String thirdContractNo;

    @Schema(description = "业务系统版本号")
    private String thirdVersion;

    @Schema(description = "系统来源")
    private String dataSource;

    @Schema(description = "系统来源编码")
    private String dataSourceCode;

    @Schema(description = "法务系统合同唯一标识", example = "7481")
    private String contractId;

    @Schema(description = "法务系统合同唯一标识名称", example = "芋艿")
    private String contractIdName;

    @Schema(description = "合同性质编码")
    private Integer dataTypeCode;

    @Schema(description = "合同分类编码")
    private String contractTypeCode;

    @Schema(description = "经办人", example = "13753")
    private String orgId;
    private Long orgIdUserId;

    @Schema(description = "立项决策编码", example = "4770")
    private Integer projectDecisionId;

    @Schema(description = "相对方确定方式编码")
    private Integer relativeMethodCode;

    @Schema(description = "授权类型编码")
    private Integer authorizedSource;

    @Schema(description = "授权名称", example = "赵六")
    private String authorizedName;

    @Schema(description = "授权编码", example = "17288")
    private String authorizedId;

    @Schema(description = "是否集团重大合同编码(0-否,1-是)")
    private Integer whetherGroupMajor;

    @Schema(description = "是否本单位重大合同编码(0-否,1-是)")
    private Integer whetherUnitMajor;

    @Schema(description = "金额类型编码")
    private Integer moneyTypeCode;

    @Schema(description = "是否含税(0-否,1-是)")
    private Integer isTax;

    @Schema(description = "结算方式编码")
    private String settlementMethodCode;

    @Schema(description = "收支方向编码")
    private Integer revenueExpenditureCode;

    @Schema(description = "合同约定开始日期(YYYY-MM-DD HH:MM:SS)")
    private LocalDateTime agreedStartTime;

    @Schema(description = "合同约定结束日期(YYYY-MM-DD HH:MM:SS)")
    private LocalDateTime agreedEndTime;

    @Schema(description = "我方地位")
    private String ourPosition;

    @Schema(description = "我方签约主体", example = "张三")
    private String ourPartyName;

    @Schema(description = "我方签约主体编码")
    private String ourPartyList;

    @Schema(description = "对方签约主体", example = "芋艿")
    private String otherPartyName;

    @Schema(description = "对方签约主体编码")
    private String otherPartyList;

    @Schema(description = "是否范本(0-否,1-是)")
    private Integer isItAtemplate;

    @Schema(description = "合同金额")
    private BigDecimal contractMoney;

    @Schema(description = "增值税率编码(可多选逗号隔开)")
    private String valueAddedTaxRateCode;

    @Schema(description = "增值税率(可多选逗号隔开)")
    private String valueAddedTaxRate;

    @Schema(description = "增值税额(元)")
    private BigDecimal valueAddedTaxAmount;

    @Schema(description = "币种名称(主数据分发)")
    private String currency;

    @Schema(description = "币种代码(主数据分发)")
    private String currencyCode;

    @Schema(description = "汇率方式编码(见附表)")
    private Integer exchangeRateMethodCode;

    @Schema(description = "汇率")
    private BigDecimal exchangeRate;

    @Schema(description = "原合同编号")
    private String originalContractCode;

    @Schema(description = "金额变更类型编码(见附表)")
    private Integer changeMoneyTypeCode;

    @Schema(description = "本次变更合同金额")
    private BigDecimal thisChangeMoney;

    @Schema(description = "本次变更增值税金额")
    private BigDecimal thisChangeValueAddedTaxAmount;

    @Schema(description = "附件地址", example = "https://www.iocoder.cn")
    private String pdfUrl;

    private String extendedFiled1;

    private boolean isCg;

    private String stus;

    //生效状态编码	takeEffectCode
    private Integer takeEffectCode;
    //生效时间（日期格式：yyyy-MM-dd HH:mm:ss）	contractTakeEffectDate
    private LocalDateTime contractTakeEffectDate;
    //对方盖章时间（日期格式：yyyy-MM-dd HH:mm:ss）	otherSealDate
    private LocalDateTime otherSealDate;
    //合同履行状态编码	performStateCode
    private Integer performStateCode;

}

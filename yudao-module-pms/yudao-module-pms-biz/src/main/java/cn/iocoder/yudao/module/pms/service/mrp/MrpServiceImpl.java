package cn.iocoder.yudao.module.pms.service.mrp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.redis.lock.RedisDistributedLock;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.basic.api.BasicApi;
import cn.iocoder.yudao.module.basic.api.dto.MaterialDetailReqDto;
import cn.iocoder.yudao.module.basic.api.dto.MaterialDetailRespDto;
import cn.iocoder.yudao.module.basic.api.dto.MaterialDto;
import cn.iocoder.yudao.module.basic.api.dto.MaterialRespDto;
import cn.iocoder.yudao.module.bpm.api.task.BpmProcessInstanceApi;
import cn.iocoder.yudao.module.bpm.api.task.dto.BpmProcessInstanceCreateReqDTO;
import cn.iocoder.yudao.module.pms.api.mrp.vo.MrpDTO;
import cn.iocoder.yudao.module.pms.controller.admin.mpp.vo.MppDetailImportRespVO;
import cn.iocoder.yudao.module.pms.controller.admin.mpp.vo.MppDetailImportVO;
import cn.iocoder.yudao.module.pms.controller.admin.mpp.vo.MppSaveReqVO;
import cn.iocoder.yudao.module.pms.controller.admin.mrp.vo.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.mpp.MppDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.mpp.MppDetailDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.mrp.MrpDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.mrp.MrpDetailDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.paramsinfo.ParamsInfoDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.stock.WmsStockDO;
import cn.iocoder.yudao.module.pms.dal.mysql.mrp.MrpDetailMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.mrp.MrpMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.paramsinfo.ParamsInfoMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.stock.WmsStockMapper;
import cn.iocoder.yudao.module.pms.service.mpp.MppService;
import cn.iocoder.yudao.module.pms.service.pomain.PoMainService;
import cn.iocoder.yudao.module.pms.service.stock.WmsStockService;
import liquibase.pro.packaged.I;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.redis.lock.RedisDistributedLock;
import cn.iocoder.yudao.module.basic.api.BasicApi;
import cn.iocoder.yudao.module.basic.api.dto.MaterialDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.*;
import static cn.iocoder.yudao.module.pms.enums.ApiConstants.MRP_PROCESS_KEY;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.*;
import static cn.iocoder.yudao.module.pms.enums.ErrorCodeConstants.*;

/**
 * 需求计划 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Validated
public class MrpServiceImpl implements MrpService {

    @Resource
    private MrpMapper mrpMapper;
    @Resource
    private MrpDetailMapper mrpDetailMapper;
    @Resource
    private WmsStockService wmsStockService;
    @Resource
    private PoMainService poMainService;
    @Resource
    private BasicApi basicApi;
    @Resource
    private WmsStockMapper wmsStockMapper;
    @Resource
    private RedisDistributedLock redisDistributedLock;

    public static final String PLAN_NO ="MRP_PLAN_NO";
    private static final String yMdIncludeGang="yyyy-MM-dd";
    private static final String yMdExcludeGang="yyyyMMdd";
    private static final String JHDH="JHDH";
    @Override
    public Long createMrp(MrpSaveReqVO createReqVO) {
        // 插入
        SimpleDateFormat sdf=new SimpleDateFormat(yMdIncludeGang);
        DateTimeFormatter formatter=DateTimeFormatter.ofPattern(yMdIncludeGang);
        MrpDO mrp = BeanUtils.toBean(createReqVO, MrpDO.class);
        mrp.setProject(String.valueOf(getLoginUserTopDeptId()));
        mrp.setCompid(String.valueOf(TenantContextHolder.getTenantId()));
        mrp.setIssueEmpno(String.valueOf(getLoginUserNickname()));
        mrp.setTrandeptno(String.valueOf(getLoginUserDeptId()));
        mrp.setRemark1(String.valueOf(getLoginUserDeptName()));
        mrp.setIssueDate(DateUtil.format(LocalDateTime.now(),"yyyy-MM-dd"));
//        mrp.setCreateEmpno(getLoginUserNickname());
//        mrp.setCreateDate(LocalDateTimeUtil.formatNormal(LocalDate.now()));
        String seq = redisDistributedLock.lockWithNum(PLAN_NO);
        mrp.setPlantallyno("P"+mrp.getPlantype()+seq);

/*        String simpleCGA="";
        String month=new SimpleDateFormat("yyyyMM").format(Calendar.getInstance().getTime());
        simpleCGA=redisDistributedLock.lockWithNum(JHDH,month,4);
        String rule1=mrp.getTrandeptno().substring(0,2);
        String plantallyno=rule1+simpleCGA;
        mrp.setPlantallyno(plantallyno);*/
        mrpMapper.insert(mrp);
        // 返回
        return mrp.getId();
    }

    @Override
    public void updateMrp(MrpSaveReqVO updateReqVO) {
        // 校验存在
        MrpDO mrpDO = validateMrpExists(updateReqVO.getId());
        if(!mrpDO.getStus().matches("[0IJ]")){
            throw exception(TEADE_COMMON_ERROR,"需求计划目前不允许操作修改！请刷新");
        }
        //如果有明细，预算的计算有变化
        if(!mrpDO.getAssigndeptno().equals(updateReqVO.getAssigndeptno())||!mrpDO.getFeetype().equals(updateReqVO.getFeetype())) {
            List<MrpDetailDO> mrpDetailDO = mrpDetailMapper.selectListByPlantallyno(updateReqVO.getPlantallyno());
            if (mrpDetailDO != null && !mrpDetailDO.isEmpty()) {
                throw exception(TEADE_COMMON_ERROR, "存在明细信息，请先删除明细数据");
            }
        }
        // 更新
        MrpDO updateObj = BeanUtils.toBean(updateReqVO, MrpDO.class);
        updateObj.setUpdater(getLoginUserId().toString());
        mrpMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMrp(Long id) {
        // 校验存在
        MrpDO mrpDO = validateMrpExists(id);
        if(!mrpDO.getStus().matches("[0IJ]")){
            throw exception(TEADE_COMMON_ERROR,"需求计划目前不允许操作删除！请刷新");
        }
        // 根据id查询计划号
        String plantallyno = mrpDO.getPlantallyno();
        // 删除子表
        deleteMrpDetailByPlantallyno(plantallyno);
        // 删除
        mrpMapper.deleteById(id);
    }

    private MrpDO validateMrpExists(Long id) {
        MrpDO mrpDO = mrpMapper.selectById(id);
        if (mrpDO == null) {
            throw exception(MRP_NOT_EXISTS);
        }
        return mrpDO;
    }

    @Override
    public MrpDO getMrp(Long id) {
        return mrpMapper.selectById(id);
    }

    @Override
    public PageResult<MrpDO> getMrpPage(MrpPageReqVO pageReqVO) {
        return mrpMapper.selectPage(pageReqVO);
    }

    // ==================== 子表（需求计划明细） ====================
    private static final String PLANITEMNO ="MRP_PLANITEMNO:";
    @Override
    public PageResult<MrpDetailDO> getMrpDetailPage(PageParam pageReqVO, String plantallyno,String stus) {
        return mrpDetailMapper.selectPage(pageReqVO, plantallyno,stus);
    }

    @Override
    public Long createMrpDetail(MrpDetailDO mrpDetail) {
        MrpDO mrpDO = mrpMapper.selectMrpByPlantallyno(mrpDetail.getPlantallyno());
        if(!mrpDO.getStus().matches("[0IJ]")){
            throw exception(TEADE_COMMON_ERROR,"需求计划目前不允许操作删除！请刷新");
        }
        MrpDetailDO existsDetail = mrpDetailMapper.selectMrpDetailDO(mrpDetail.getPlantallyno(), mrpDetail.getMatrlno());
        if (existsDetail != null) {
            throw exception(MRP_MATRLNO_ALREADY_EXISTS);
        }
        if (mrpDetail.getIssueqty() == null || mrpDetail.getIssueqty().compareTo(BigDecimal.ZERO) <= 0) {
            throw exception(MRP_ISSUEQTY_NEED_LG_ZERO);
        }

        // 需要查询计划单价，插入数据
        MrpDetailUnitpriceVO priceVO = getMrpDetailUnitprice(mrpDetail.getMatrlno(),mrpDO.getPlanyearmo().substring(0,4));//mrpDO.getId(), mrpDetail.getId(),
        if (mrpDetail.getUnitprice()==null||mrpDetail.getUnitprice().compareTo(priceVO.getUnitprice())!=0) {
            mrpDetail.setUnitprice(priceVO.getUnitprice());
            mrpDetail.setIssueamt(mrpDetail.getUnitprice().multiply(mrpDetail.getIssueqty()));//重新计算单价
        }
        if (priceVO.getEndqty().compareTo(BigDecimal.ZERO) > 0 || priceVO.getBudgetAmt().compareTo(mrpDetail.getIssueamt()) <= 0
                || priceVO.getMinconsumpqty().compareTo(mrpDetail.getIssueqty())<0) {
            mrpDetail.setStus("D");
        } else {
            mrpDetail.setStus("0");
        }

        mrpDetail.setCompid(String.valueOf(TenantContextHolder.getTenantId()));
        mrpDetail.setProject(String.valueOf(getLoginUserTopDeptId()));
        Supplier<Long> func = ()->{
            try {
                String maxReqitemno = mrpDetailMapper.selectMaxReqitemno(mrpDetail.getPlantallyno());
                DecimalFormat formatter = new DecimalFormat("0000");
                int iMaxSeq = Integer.parseInt(maxReqitemno);
                mrpDetail.setSeqno(formatter.format(++iMaxSeq));
                mrpDetail.setProject(String.valueOf(getLoginUserTopDeptId()));
                mrpDetailMapper.insert(mrpDetail);
                return mrpDetail.getId();
            } catch (Exception e) {
                e.printStackTrace();
                return -1L;
            }
        };
        Long lock = redisDistributedLock.lock(PLANITEMNO+mrpDetail.getPlantallyno(), func);
        if(lock==-1L){
            throw exception(TEADE_COMMON_ERROR,"新增失败");
        }

        return lock;
    }

    @Override
    public void updateMrpDetail(MrpDetailDO mrpDetail) {
        MrpDO mrpDO = mrpMapper.selectMrpByPlantallyno(mrpDetail.getPlantallyno());
        if(!mrpDO.getStus().matches("[0IJ]")){
            throw exception(TEADE_COMMON_ERROR,"需求计划目前不允许操作修改！请刷新");
        }
        MrpDetailDO existsDetail = mrpDetailMapper.selectMrpDetailDO(mrpDetail.getPlantallyno(), mrpDetail.getMatrlno());
        if (existsDetail != null && !existsDetail.getId().equals(mrpDetail.getId())) {
            throw exception(MRP_MATRLNO_ALREADY_EXISTS);
        }
        if (mrpDetail.getIssueqty() == null || mrpDetail.getIssueqty().compareTo(BigDecimal.ZERO) <= 0) {
            throw exception(MRP_ISSUEQTY_NEED_LG_ZERO);
        }
        // 需要查询计划单价，插入数据
        MrpDetailUnitpriceVO priceVO = getMrpDetailUnitprice(mrpDetail.getMatrlno(),mrpDO.getPlanyearmo().substring(0,4));//mrpDO.getId(), mrpDetail.getId(),
        if (mrpDetail.getUnitprice()==null||mrpDetail.getUnitprice().compareTo(priceVO.getUnitprice())!=0) {
            mrpDetail.setUnitprice(priceVO.getUnitprice());
            mrpDetail.setIssueamt(mrpDetail.getUnitprice().multiply(mrpDetail.getIssueqty()));//重新计算单价
        }
        if (priceVO.getEndqty().compareTo(BigDecimal.ZERO) > 0 || priceVO.getBudgetAmt().compareTo(mrpDetail.getIssueamt()) <= 0
                || priceVO.getMinconsumpqty().compareTo(mrpDetail.getIssueqty())<0) {
            mrpDetail.setStus("D");
        } else {
            mrpDetail.setStus("0");
        }
        // 校验存在
        validateMrpDetailExists(mrpDetail.getId());
        // 更新
        mrpDetailMapper.updateById(mrpDetail);
    }

    @Override
    public void deleteMrpDetail(Long id) {
        // 校验存在
        validateMrpDetailExists(id);
        // 删除
        mrpDetailMapper.deleteById(id);
    }

    @Override
    public MrpDetailDO getMrpDetail(Long id) {
        return mrpDetailMapper.selectById(id);
    }


    // id ,plantallyno ,matrlno ,contractno
    @Override
    public MrpDetailUnitpriceVO getMrpDetailUnitprice(String matrlno, String year) {
        MrpDetailUnitpriceVO result = new MrpDetailUnitpriceVO();
        result.setUnitprice(BigDecimal.ZERO);
        result.setEndqty(BigDecimal.ZERO);
        result.setMinconsumpqty(BigDecimal.ZERO);
        result.setBudgetAmt(BigDecimal.ZERO);
//        result.setId(detailId);
//        result.setSource(MrpDetailUnitpriceVO.UnitpriceSourceEnum.MRP_INPUT.getSource());
//        MrpDO mrpDO = mrpMapper.selectById(parentId);
//        if (mrpDO == null || mrpDO.getDeleted()) {
//            return result;
//        }
        if (StringUtils.isBlank(matrlno)) {
            return result;
        }

        // 1、如果合同价存在，则取合同价
        // 查询库存价 按照料号+合同查询
//        WmsStockDO wmsStockDO = wmsStockService.getWmsStockByMatrlnoAndPono(matrlno, mrpDO.getContractno());
//
//        if (wmsStockDO == null) {
//            // 如果不存在 只按照料号查询
//            wmsStockDO = wmsStockService.getWmsStockByMatrlnoAndPono(matrlno, null);
//        }
//        if (wmsStockDO != null) {
//            // 金额
//            BigDecimal endamt = Optional.ofNullable(wmsStockDO.getEndamt()).orElse(BigDecimal.ZERO);
//            // 量
//            BigDecimal endqty = Optional.ofNullable(wmsStockDO.getEndqty()).orElse(BigDecimal.ZERO);
//            // 计算单价
//            if (endamt.compareTo(BigDecimal.ZERO) > 0 && endqty.compareTo(BigDecimal.ZERO) > 0) {
//                BigDecimal unitprice = endamt.divide(endqty, 6, BigDecimal.ROUND_HALF_UP);
//                result.setUnitprice(unitprice);
//                result.setSource(MrpDetailUnitpriceVO.UnitpriceSourceEnum.STOCK.getSource());
//                return result;
//            }
//        }
        // 2、 如果合同中存在，取合同
//        PoDetailDO poDetailDO = poMainService.getDetailByMatrlnoAndPono(matrlno, mrpDO.getContractno());
//        if (poDetailDO != null) {
//            if (poDetailDO.getUnitprice() != null && poDetailDO.getUnitprice().compareTo(BigDecimal.ZERO) > 0) {
//                result.setUnitprice(poDetailDO.getUnitprice());
//                result.setSource(MrpDetailUnitpriceVO.UnitpriceSourceEnum.PO_DETAIL.getSource());
//                return result;
//            }
//        }
        // 3、 如果料号表存在，取料号表 目前取值stdunitcost
        MaterialDetailReqDto materialDetailReqDto = new MaterialDetailReqDto().setMatrlno(matrlno)
            .setCompid(TenantContextHolder.getTenantId().toString()).setTenantId(TenantContextHolder.getTenantId());
        MaterialDetailRespDto materialDetailRespDto =
            basicApi.getMaterialDetailByMatrlnoAndCompid(materialDetailReqDto);
        if (materialDetailRespDto != null) {
            if (materialDetailRespDto.getBudgetPrice() != null) {
                result.setUnitprice(materialDetailRespDto.getBudgetPrice());

//                result.setSource(MrpDetailUnitpriceVO.UnitpriceSourceEnum.MATERIAL_DETAIL.getSource());
            }
            if (materialDetailRespDto.getConsumeQty() != null) {
                result.setMinconsumpqty(materialDetailRespDto.getConsumeQty());//消耗定额
            }
        }
        // 4、取输入的值，也是detail表中保存的值。
//        if (detailId != null) {
//            MrpDetailDO detail = getMrpDetail(detailId);
//            result.setUnitprice(detail.getUnitprice());
//            result.setId(detailId);
//            result.setSource(MrpDetailUnitpriceVO.UnitpriceSourceEnum.MRP_INPUT.getSource());
//        }
        //获取已经申请数量，金额
        MrpDetailDO detail =  mrpDetailMapper.selectSumQtyAndAmt(matrlno,year);
        if (detail!=null&&detail.getIssueqty()!=null) {
            result.setMinconsumpqty(result.getMinconsumpqty().subtract(detail.getIssueqty()));
        }
        //获取库存
        WmsStockDO wmsStockDO = wmsStockMapper.selectByMatrlno(matrlno);
        if(wmsStockDO!=null){
            result.setEndqty(wmsStockDO.getEndqty());
        }
        //todo 获取预算 超过预算不让新增 新增时更新预算明细
        result.setBudgetAmt(new BigDecimal("999999999"));

        return result;
    }


    private MrpDetailDO validateMrpDetailExists(Long id) {
        MrpDetailDO mrpDetailDO = mrpDetailMapper.selectById(id);
        if (mrpDetailDO == null) {
            throw exception(MRP_DETAIL_NOT_EXISTS);
        }
        return mrpDetailDO;
    }

    private void deleteMrpDetailByPlantallyno(String plantallyno) {
        mrpDetailMapper.deleteByPlantallyno(plantallyno);
    }


    //--------------------------流程
    @Resource
    private BpmProcessInstanceApi processInstanceApi;
    @Override
    public Long createFlow(Long userId, MrpSaveReqVO createReqVO) {
        Long mrpId = createReqVO.getId();
        MrpDO mrpDO = validateMrpExists(mrpId);
        if(!mrpDO.getStus().matches("[0IJ]")){
            throw exception(TEADE_COMMON_ERROR,"需求计划目前不允许操作！请刷新");
        }
        PageParam pageReqVO = new PageParam();
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        PageResult<MrpDetailDO> mrpDetailPage = getMrpDetailPage(pageReqVO, createReqVO.getPlantallyno(),"");
        if (mrpDetailPage.getList().isEmpty()) {
            throw exception(TEADE_COMMON_ERROR,"需求计划没有明细信息，请添加！");
        }

        // 发起 BPM 流程
//        Map<String, Object> processInstanceVariables = new HashMap<>();
//        processInstanceVariables.put("day", day);
        Map<String, List<Long>> startUserSelectAssignees = new HashMap<>();
//        startUserSelectAssignees.put("Activity_0tx2pkp", Arrays.asList(1L));
        createReqVO.setStartUserSelectAssignees(startUserSelectAssignees);
        BpmProcessInstanceCreateReqDTO bpmProcessInstanceCreateReqDTO=new BpmProcessInstanceCreateReqDTO();
        bpmProcessInstanceCreateReqDTO.setProcessDefinitionKey(MRP_PROCESS_KEY);
        bpmProcessInstanceCreateReqDTO.setBusinessKey(String.valueOf(mrpId));
        bpmProcessInstanceCreateReqDTO.setStartUserSelectAssignees(createReqVO.getStartUserSelectAssignees());
        String processInstanceId = processInstanceApi.createProcessInstance(userId, bpmProcessInstanceCreateReqDTO).getCheckedData();
//        String processInstanceId = processInstanceApi.createProcessInstance(userId,
//                        new BpmProcessInstanceCreateReqDTO().setProcessDefinitionKey(MPP_PROCESS_KEY)
////                        .setVariables(processInstanceVariables)
//                                .setBusinessKey(String.valueOf(mppId))
//                                .setStartUserSelectAssignees(createReqVO.getStartUserSelectAssignees())
//                )
//                .getCheckedData();

        // 将工作流的编号，更新到 OA 请假单中
        mrpDO.setProcessInstanceId(processInstanceId);
        mrpDO.setStus("G");
        mrpDO.setFlowstus("-");
        mrpDO.setUpdateTime(LocalDateTime.now());
        mrpMapper.updateById(mrpDO);
//        mppMapper.updateById(new MppDO().setId(mppId)
//                .setProcessInstanceId(processInstanceId)
//                .setUpdateDate(LocalDateTimeUtil.formatNormal(LocalDate.now()))
//                .setUpdateEmpno(getLoginUserNickname())
//                .setStus("G")
//                .setFlowstus("-"));
//        leaveMapper.updateById(new BpmOALeaveDO().setId(leave.getId()).setProcessInstanceId(processInstanceId));

        //todo 进行预算扣除

        return mrpId;
    }
    @Resource
    private ParamsInfoMapper paramsInfoMapper;
    @Override
    public void updateMrpStatus(MrpDTO mrpDTO) {
        MrpDO mrpDO=new MrpDO();
        mrpDO.setFlowstus(mrpDTO.getAuditType());
        mrpDO.setStus(mrpDTO.getStatus());
        mrpDO.setId(Long.valueOf(mrpDTO.getBusinessKey()));
//        mrpMapper.updateMrpStatus(mrpDO);
        mrpMapper.updateById(mrpDO);

        if("D".equals(mrpDTO.getStatus())){
            //todo 回退预算
        }
        if("H".equals(mrpDTO.getStatus())){
            mrpDO = mrpMapper.selectById(mrpDTO.getBusinessKey());

            List<MrpDetailDO> mrpDetailDOS = mrpDetailMapper.selectListByPlantallyno(mrpDO.getPlantallyno());
            //获取计划平衡员
//            for (MrpDetailDO d : mrpDetailDOS) {
//                String matrlNo = d.getMatrlno();
//                List<String> matrlnoList = IntStream.rangeClosed(1, matrlNo.length())
//                        .mapToObj(i -> matrlNo.substring(0, i))
//                        .collect(Collectors.toList());
//
//                ParamsInfoDO mrpdispatch = paramsInfoMapper.selectParamByABIn("MRPDISPATCH", mrpDO.getAssigndeptno(), matrlnoList);
//                if(mrpdispatch!=null){
//                    d.setTreatEmpno(mrpdispatch.getValuec());
//                }
//                mrpDetailMapper.updateById(d);
//            }
            getTreatEmpno(mrpDO,mrpDetailDOS);
            //平衡利库
            BalancedInventory(mrpDO,mrpDetailDOS);

            //更新状态
            mrpDetailMapper.updateStusByPlantallyno(mrpDO.getPlantallyno(),"H");
        }



    }



    public static final String PLANBALANCED = "PLANBALANCED";

    private void getTreatEmpno(MrpDO mrpDO,List<MrpDetailDO> mrpDetailDOS){
        for (MrpDetailDO d : mrpDetailDOS) {
            String matrlNo = d.getMatrlno();
            List<String> matrlnoList = IntStream.rangeClosed(1, matrlNo.length())
                    .mapToObj(i -> matrlNo.substring(0, i))
                    .collect(Collectors.toList());

            ParamsInfoDO mrpdispatch = paramsInfoMapper.selectParamByABIn("MRPDISPATCH", mrpDO.getAssigndeptno(), matrlnoList);
            if(mrpdispatch!=null){
                d.setTreatEmpno(mrpdispatch.getValuec());
                d.setRemark1(mrpdispatch.getValued());
                d.setVerDate(DateUtil.format(LocalDateTime.now(),"yyyy-MM-dd HH:mm:ss"));//审批完成时间
            }
            mrpDetailMapper.updateById(d);
        }
    }

    /**
     * 平衡利库
     */
    protected void BalancedInventory(MrpDO mrpDO,List<MrpDetailDO> mrpDetailDOS){
        Supplier<Void> func = ()->{
            for (MrpDetailDO mrpDetailDO : mrpDetailDOS) {
                try {
                    //自动平衡利库
                    MrpDetailUnitpriceVO mrpDetailUnitprice = getMrpDetailUnitprice(mrpDetailDO.getMatrlno(), mrpDO.getPlanyearmo().substring(0, 4));
                    if(mrpDetailUnitprice.getEndqty().compareTo(BigDecimal.ZERO)>0){
                        //有库存直接全部平衡掉
                        mrpDetailDO.setAcctotalQty(mrpDetailDO.getIssueqty());
                        mrpDetailDO.setIssueqty(BigDecimal.ZERO);
                        mrpDetailDO.setAcctotalAmt(mrpDetailDO.getIssueamt());
                        mrpDetailDO.setIssueamt(BigDecimal.ZERO);
                    } else if(mrpDetailUnitprice.getMinconsumpqty().compareTo(mrpDetailDO.getIssueqty())<0){
                        //超过消耗定额的数量
                        mrpDetailDO.setAcctotalQty(mrpDetailDO.getIssueqty().subtract(mrpDetailUnitprice.getMinconsumpqty()));
                        mrpDetailDO.setAcctotalAmt(mrpDetailDO.getUnitprice().multiply(mrpDetailDO.getAcctotalQty()).setScale(2,RoundingMode.HALF_UP));
                        mrpDetailDO.setIssueqty(mrpDetailUnitprice.getMinconsumpqty());
                        mrpDetailDO.setIssueamt(mrpDetailDO.getIssueamt().subtract(mrpDetailDO.getAcctotalAmt()));
                        //todo 补充预算表。
//                        mrpDetailDO.getAcctotalAmt();
                    } else {
                        //不用平衡
                        mrpDetailDO.setAcctotalQty(BigDecimal.ZERO);
                        mrpDetailDO.setAcctotalAmt(BigDecimal.ZERO);
                    }
                    mrpDetailDO.setStus("X");
                    mrpDetailMapper.updateById(mrpDetailDO);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            return null;
        };
        redisDistributedLock.lock(PLANBALANCED, func);//默认 10L*1000,
    }


    @Override
    public PageResult<MrpBalancedVO> getMrpPageBalanced(MrpPageReqVO pageReqVO) {
        return mrpDetailMapper.selectPageBalanced(pageReqVO);
    }
    @Resource
    private MppService mppService;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String mrp2mpp(String ids) {
        AtomicInteger i = new AtomicInteger();
        AtomicLong reqid = new AtomicLong();
        AtomicReference<String> reqNo= new AtomicReference<>("");
        for (String id : ids.split(",")) {
            try {
                if (!redisDistributedLock.tryLock(PLANBALANCED + ":" + id)) {
                    throw exception(TEADE_COMMON_ERROR,"操作失败");
                }
                MrpDetailDO mrpDetailDO = validateMrpDetailExists(Long.valueOf(id));
                if ("Z".equals(mrpDetailDO.getStus())) {
                    throw exception(TEADE_COMMON_ERROR,"需求计划号【" + mrpDetailDO.getPlantallyno() + "】项次【" + mrpDetailDO.getSeqno() + "】物料【" + mrpDetailDO.getMatrlno() + "】已经生成请购计划【" + mrpDetailDO.getReqno() + "】");
                }
                if ("C".equals(mrpDetailDO.getStus())) {
                    throw exception(TEADE_COMMON_ERROR,"需求计划号【" + mrpDetailDO.getPlantallyno() + "】项次【" + mrpDetailDO.getSeqno() + "】物料【" + mrpDetailDO.getMatrlno() + "】已经被退回");
                }
                if (!"X".equals(mrpDetailDO.getStus())) {
                    throw exception(TEADE_COMMON_ERROR,"需求计划号【" + mrpDetailDO.getPlantallyno() + "】项次【" + mrpDetailDO.getSeqno() + "】物料【" + mrpDetailDO.getMatrlno() + "】状态不正确，无法操作");
                }
                MrpDO mrpDO = mrpMapper.selectOneByPlantallyno(mrpDetailDO.getPlantallyno());
                //todo 转mpp 请购
                if (i.getAndIncrement() == 0) {
                    MppSaveReqVO createReqVO = new MppSaveReqVO();
                    createReqVO.setUseDept(mrpDO.getTrandeptno());
                    createReqVO.setUseDate(mrpDetailDO.getPlanDate());
                    createReqVO.setCrcy("CNY");
                    createReqVO.setUseCode(mrpDO.getPurposeid());
                    switch (mrpDO.getInventorytype()) {
                        case "R":
                            createReqVO.setPurtype("03");
                            break;
                        case "P":
                            createReqVO.setPurtype("02");
                            break;
                        case "M":
                            createReqVO.setPurtype("01");
                            break;
                        default:
                            createReqVO.setPurtype("");
                    }
                    createReqVO.setFromap("MRP");
                    createReqVO.setReqDate(LocalDateTimeUtil.formatNormal(LocalDate.now()));
                    createReqVO.setStus("0");
                    reqid.set(mppService.createMpp(createReqVO));
                    reqNo.set(createReqVO.getReqno());
                }
                MppDetailDO mppDetail = new MppDetailDO();
                mppDetail.setMainId(reqid.get());
                mppDetail.setReqno(reqNo.get());
                mppDetail.setPlantallyno(mrpDetailDO.getPlantallyno());
                mppDetail.setPlanseqno(mrpDetailDO.getSeqno());
                mppDetail.setMatrlno(mrpDetailDO.getMatrlno());
                mppDetail.setUnit(mrpDetailDO.getUnitinv());
                mppDetail.setUsedept(mrpDO.getTrandeptno());
                mppDetail.setUseDate(mrpDetailDO.getPlanDate());
                mppDetail.setReqqty(mrpDetailDO.getIssueqty());
                mppDetail.setQty(mrpDetailDO.getIssueqty());
                mppDetail.setUnitprice(mrpDetailDO.getUnitprice());
                mppDetail.setAmt(mrpDetailDO.getIssueamt());
                MaterialDto materialDto = new MaterialDto();
                materialDto.setMatrlno(mppDetail.getMatrlno());
                PageResult<MaterialRespDto> materialDtoPageResult = basicApi.apiMaterialList(materialDto).getCheckedData();
                if (materialDtoPageResult.getTotal()==0) {
                    throw exception(TEADE_COMMON_ERROR,"料号【"+mppDetail.getMatrlno()+"】没有找到");
                }
                MaterialRespDto materialDto2 = materialDtoPageResult.getList().get(0);
                mppDetail.setMatrlno(materialDto2.getMatrlno());
                mppDetail.setCnMdesc(materialDto2.getCnmdesc());
                mppDetail.setChNspec(materialDto2.getNmspec());
                mppDetail.setQlty(materialDto2.getQuality());
                mppDetail.setDrawing(materialDto2.getPicno());

                mppService.createMppDetail(mppDetail);
                mrpDetailDO.setReqno(reqNo.get());
                mrpDetailDO.setReqitemno(mppDetail.getReqitemno());
                mrpDetailDO.setStus("Z");
                mrpDetailDO.setUpdateTime(LocalDateTime.now());
                mrpDetailMapper.updateById(mrpDetailDO);
            }finally {
                redisDistributedLock.unlock(PLANBALANCED + ":" + id);
            }
//            Supplier<String> func = ()->{
//                try {
//                    MrpDetailDO mrpDetailDO = validateMrpDetailExists(Long.valueOf(id));
//                    if ("Z".equals(mrpDetailDO.getStus())) {
//                        throw exception(TEADE_COMMON_ERROR,"需求计划号【" + mrpDetailDO.getPlantallyno() + "】项次【" + mrpDetailDO.getSeqno() + "】物料【" + mrpDetailDO.getMatrlno() + "】已经生成请购计划【" + mrpDetailDO.getReqno() + "】");
//                    }
//                    if ("C".equals(mrpDetailDO.getStus())) {
//                        throw exception(TEADE_COMMON_ERROR,"需求计划号【" + mrpDetailDO.getPlantallyno() + "】项次【" + mrpDetailDO.getSeqno() + "】物料【" + mrpDetailDO.getMatrlno() + "】已经被退回");
//                    }
//                    if (!"X".equals(mrpDetailDO.getStus())) {
//                        throw exception(TEADE_COMMON_ERROR,"需求计划号【" + mrpDetailDO.getPlantallyno() + "】项次【" + mrpDetailDO.getSeqno() + "】物料【" + mrpDetailDO.getMatrlno() + "】状态不正确，无法操作");
//                    }
//                    MrpDO mrpDO = mrpMapper.selectOneByPlantallyno(mrpDetailDO.getPlantallyno());
//                    //todo 转mpp 请购
//                    if (i.getAndIncrement() == 0) {
//                        MppSaveReqVO createReqVO = new MppSaveReqVO();
//                        createReqVO.setUseDept(mrpDO.getTrandeptno());
//                        createReqVO.setUseDate(mrpDetailDO.getPlanDate());
//                        createReqVO.setCrcy("CNY");
//                        createReqVO.setUseCode(mrpDO.getPurposeid());
//                        switch (mrpDO.getInventorytype()) {
//                            case "R":
//                                createReqVO.setPurtype("03");
//                                break;
//                            case "P":
//                                createReqVO.setPurtype("02");
//                                break;
//                            case "M":
//                                createReqVO.setPurtype("01");
//                                break;
//                            default:
//                                createReqVO.setPurtype("");
//                        }
//                        createReqVO.setFromap("MRP");
//                        createReqVO.setReqDate(LocalDateTimeUtil.formatNormal(LocalDate.now()));
//                        createReqVO.setStus("0");
//                        reqid.set(mppService.createMpp(createReqVO));
//                        reqNo.set(createReqVO.getReqno());
//                    }
//                    MppDetailDO mppDetail = new MppDetailDO();
//                    mppDetail.setMainId(reqid.get());
//                    mppDetail.setReqno(reqNo.get());
//                    mppDetail.setPlantallyno(mrpDetailDO.getPlantallyno());
//                    mppDetail.setPlanseqno(mrpDetailDO.getSeqno());
//                    mppDetail.setMatrlno(mrpDetailDO.getMatrlno());
//                    mppDetail.setUnit(mrpDetailDO.getUnitinv());
//                    mppDetail.setUsedept(mrpDO.getTrandeptno());
//                    mppDetail.setUseDate(mrpDetailDO.getPlanDate());
//                    mppDetail.setReqqty(mrpDetailDO.getIssueqty());
//                    mppDetail.setQty(mrpDetailDO.getIssueqty());
//                    mppDetail.setUnitprice(mrpDetailDO.getUnitprice());
//                    mppDetail.setAmt(mrpDetailDO.getIssueamt());
//                    MaterialDto materialDto = new MaterialDto();
//                    materialDto.setMatrlno(mppDetail.getMatrlno());
//                    PageResult<MaterialRespDto> materialDtoPageResult = basicApi.apiMaterialList(materialDto).getCheckedData();
//                    if (materialDtoPageResult.getTotal()==0) {
//                        throw exception(TEADE_COMMON_ERROR,"料号【"+mppDetail.getMatrlno()+"】没有找到");
//                    }
//                    MaterialRespDto materialDto2 = materialDtoPageResult.getList().get(0);
//                    mppDetail.setMatrlno(materialDto2.getMatrlno());
//                    mppDetail.setCnMdesc(materialDto2.getCnmdesc());
//                    mppDetail.setChNspec(materialDto2.getNmspec());
//                    mppDetail.setQlty(materialDto2.getQuality());
//                    mppDetail.setDrawing(materialDto2.getPicno());
//
//                    mppService.createMppDetail(mppDetail);
//                    mrpDetailDO.setReqno(reqNo.get());
//                    mrpDetailDO.setReqitemno(mppDetail.getReqitemno());
//                    mrpDetailDO.setStus("Z");
//                    mrpDetailDO.setUpdateTime(LocalDateTime.now());
//                    mrpDetailMapper.updateById(mrpDetailDO);
//                    return "";
//                } catch (Exception e){
//                    e.printStackTrace();
//                    return e.getMessage();
//                }
//            };
//            String lock = redisDistributedLock.lock(PLANBALANCED + ":" + id, func);//默认 10L*1000,
//            if(!lock.isEmpty()){
//                throw exception(TEADE_COMMON_ERROR,lock);
//            }
        }
        return reqNo.get();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String back2mpp(MrpBalancedVO mrpBalancedVO) {
        if(mrpBalancedVO.getIds()!=null){
            for (Long id : mrpBalancedVO.getIds()){
                try {
                    if (!redisDistributedLock.tryLock(PLANBALANCED + ":" + id)) {
                        throw exception(TEADE_COMMON_ERROR, "操作失败");
                    }
                    MrpDetailDO mrpDetailDO = validateMrpDetailExists(Long.valueOf(id));
                    if ("Z".equals(mrpDetailDO.getStus())) {
                        throw exception(TEADE_COMMON_ERROR,"需求计划号【" + mrpDetailDO.getPlantallyno() + "】项次【" + mrpDetailDO.getSeqno() + "】物料【" + mrpDetailDO.getMatrlno() + "】已经生成请购计划【" + mrpDetailDO.getReqno() + "】");
                    }
                    if ("C".equals(mrpDetailDO.getStus())) {
                        throw exception(TEADE_COMMON_ERROR,"需求计划号【" + mrpDetailDO.getPlantallyno() + "】项次【" + mrpDetailDO.getSeqno() + "】物料【" + mrpDetailDO.getMatrlno() + "】已经被退回");
                    }
                    if (!"X".equals(mrpDetailDO.getStus())) {
                        throw exception(TEADE_COMMON_ERROR,"需求计划号【" + mrpDetailDO.getPlantallyno() + "】项次【" + mrpDetailDO.getSeqno() + "】物料【" + mrpDetailDO.getMatrlno() + "】状态不正确，无法操作");
                    }
                    mrpDetailDO.setStus("C");
                    mrpDetailDO.setUpdateTime(LocalDateTime.now());
                    mrpDetailDO.setVerEmpno(Objects.requireNonNull(getLoginUserId()).toString());
                    mrpDetailDO.setVerstus(getLoginUserNickname());
                    mrpDetailDO.setVeropinion(mrpBalancedVO.getBackMemo());
                    mrpDetailDO.setVerDate(LocalDateTimeUtil.formatNormal(LocalDateTime.now()));
                    mrpDetailMapper.updateById(mrpDetailDO);
                }finally {
                    redisDistributedLock.unlock(PLANBALANCED + ":" + id);
                }
//                Supplier<String> func = ()->{
//                    try {
//                        MrpDetailDO mrpDetailDO = validateMrpDetailExists(Long.valueOf(id));
//                        if ("Z".equals(mrpDetailDO.getStus())) {
//                            throw exception(TEADE_COMMON_ERROR,"需求计划号【" + mrpDetailDO.getPlantallyno() + "】项次【" + mrpDetailDO.getSeqno() + "】物料【" + mrpDetailDO.getMatrlno() + "】已经生成请购计划【" + mrpDetailDO.getReqno() + "】");
//                        }
//                        if ("C".equals(mrpDetailDO.getStus())) {
//                            throw exception(TEADE_COMMON_ERROR,"需求计划号【" + mrpDetailDO.getPlantallyno() + "】项次【" + mrpDetailDO.getSeqno() + "】物料【" + mrpDetailDO.getMatrlno() + "】已经被退回");
//                        }
//                        if (!"X".equals(mrpDetailDO.getStus())) {
//                            throw exception(TEADE_COMMON_ERROR,"需求计划号【" + mrpDetailDO.getPlantallyno() + "】项次【" + mrpDetailDO.getSeqno() + "】物料【" + mrpDetailDO.getMatrlno() + "】状态不正确，无法操作");
//                        }
//                        mrpDetailDO.setStus("C");
//                        mrpDetailDO.setUpdateTime(LocalDateTime.now());
//                        mrpDetailDO.setVerEmpno(Objects.requireNonNull(getLoginUserId()).toString());
//                        mrpDetailDO.setVerstus(getLoginUserNickname());
//                        mrpDetailDO.setVeropinion(mrpBalancedVO.getBackMemo());
//                        mrpDetailDO.setVerDate(LocalDateTimeUtil.formatNormal(LocalDateTime.now()));
//                        mrpDetailMapper.updateById(mrpDetailDO);
//                        return "";
//                    } catch (Exception e){
//                        e.printStackTrace();
//                        return e.getMessage();
//                    }
//                };
//                String lock = redisDistributedLock.lock(PLANBALANCED + ":" + id, func);//默认 10L*1000,
//                if(!lock.isEmpty()){
//                    throw exception(TEADE_COMMON_ERROR,lock);
//                }
            }
        }

        return null;
    }

    @Override
    public MrpDetailImportRespVO importMrpDetailList(List<MrpDetailImportVO> list, String plantallyno, Boolean updateSupport) {
        // 1 参数校验
        if (CollUtil.isEmpty(list)) {
            throw exception(MRP_IMPORT_LIST_IS_EMPTY);
        }
        MrpDO mrpDO = mrpMapper.selectOneByPlantallyno(plantallyno);
        if(mrpDO==null){
            throw exception(TEADE_COMMON_ERROR,"需求计划不存在！");
        }
        if(!mrpDO.getStus().matches("[0IJ]")){
            throw exception(TEADE_COMMON_ERROR,"需求计划目前不允许操作导入！请刷新");
        }
        List<MrpDetailDO> mrpDetailDOs = mrpDetailMapper.selectListByPlantallyno(plantallyno);
        // 2. 遍历，逐个创建 or 更新
        MrpDetailImportRespVO respVO = MrpDetailImportRespVO.builder()
                .createDetails(new ArrayList<>()).updateDetails(new ArrayList<>())
                .failureDetails(new LinkedHashMap<>()).build();
        Set<String> matrlnos= new HashSet<>();
        list.forEach(detail -> {
            String seq = String.valueOf(detail.getSeq());
            if(detail.getMatrlno()==null||detail.getMatrlno().isEmpty()){
                respVO.getFailureDetails().put(seq,"料号不能为空");
                return;
            }
            if(detail.getPlanDate()==null||detail.getPlanDate().isEmpty()||!detail.getPlanDate().matches("\\d{4}-\\d{2}-\\d{2}")){
                respVO.getFailureDetails().put(seq,"计划用料日期不能为空或格式不为yyyy-MM-dd");
                return;
            }
            if (detail.getIssueqty()==null||BigDecimal.ZERO.compareTo(detail.getIssueqty())==0) {
                respVO.getFailureDetails().put(seq,"计划数量不存在或不能为0");
                return;
            }
            MrpDetailDO mrpDetail = BeanUtils.toBean(detail, MrpDetailDO.class);
            mrpDetail.setPlantallyno(plantallyno);
            try {
                if (matrlnos.contains(mrpDetail.getMatrlno())) {
                    respVO.getFailureDetails().put(seq,"料号重复导入");
                    return ;
                }
                matrlnos.add(mrpDetail.getMatrlno());
                Optional<MrpDetailDO> first = mrpDetailDOs.stream().filter(m -> m.getMatrlno().equals(mrpDetail.getMatrlno())).findFirst();
                if (first.isPresent()) {
                    if(updateSupport){
                        mrpDetail.setId(first.get().getId());
                        updateMrpDetail(mrpDetail);
                        respVO.getUpdateDetails().add(seq);
                        return ;
                    }
                    respVO.getFailureDetails().put(seq,"料号已存在");
                    return ;
                }

                MaterialDto materialDto = new MaterialDto();
                materialDto.setMatrlno(mrpDetail.getMatrlno());
                PageResult<MaterialRespDto> materialDtoPageResult = basicApi.apiMaterialList(materialDto).getCheckedData();
                if (materialDtoPageResult.getTotal()==0) {
                    respVO.getFailureDetails().put(seq,"没有找到料号资料");
                    return;
                }
                MaterialRespDto materialDto2 = materialDtoPageResult.getList().get(0);
                mrpDetail.setMatrlno(materialDto2.getMatrlno());
                mrpDetail.setUnitinv(materialDto2.getUnitinv());
                createMrpDetail(mrpDetail);
                respVO.getCreateDetails().add(seq);
            } catch (Exception e){
                e.printStackTrace();
                respVO.getFailureDetails().put(seq,e.getMessage());
            }

        });
        return respVO;
    }

    @Override
    public List<MrpDetailExportVO> exportMrp(Long id) {
        return mrpMapper.selectExportMrp(id);
    }

    @Override
    public MrpDetailImportRespVO importMrpDetailList(List<MrpDetailImportVO> list, Long plantallyno, Boolean updateSupport) {
        if (CollUtil.isEmpty(list)) {
            throw exception(MRP_DETAIL_IMPORT_LIST_IS_EMPTY);
        }
        MrpDO mrpDO = mrpMapper.selectById(plantallyno);
        if(mrpDO==null){
            throw new RuntimeException("需求计划不存在！");
        }
        List<MrpDetailDO> mrpDetailDOS = mrpDetailMapper.selectList(MrpDetailDO::getPlantallyno, plantallyno);
        // 2. 遍历，逐个创建 or 更新
        MrpDetailImportRespVO respVO = MrpDetailImportRespVO.builder()
                .createDetails(new ArrayList<>()).updateDetails(new ArrayList<>())
                .failureDetails(new LinkedHashMap<>()).build();
        list.forEach(detail -> {
            MrpDetailDO  mrpDetail = BeanUtils.toBean(detail, MrpDetailDO.class);
            mrpDetail.setPlantallyno(plantallyno.toString());
            Long id = 0L;
            try {
                if(updateSupport){
                    Optional<MrpDetailDO> first = mrpDetailDOS.stream().filter(m -> m.getMatrlno().equals(mrpDetail.getMatrlno())).findFirst();
                    if (first.isPresent()) {
                        mrpDetail.setId(first.get().getId());
                        updateMrpDetail(mrpDetail);
                        respVO.getUpdateDetails().add(mrpDetail.getMatrlno());
                        return;
                    }
                }
                MaterialDto materialDto=basicApi.apiGetMaterialMessage(mrpDetail.getMatrlno());
                if(materialDto==null){
                    throw exception(MATERIAL_DETAIL_CHECK_EXISTS);
                }
                mrpDetail.setInventorytype(materialDto.getInvenTorytype())
                                .setUnitinv(materialDto.getUnitinv());
                createMrpDetail(mrpDetail);
                respVO.getCreateDetails().add(mrpDetail.getMatrlno());
            } catch (Exception e){
                respVO.getFailureDetails().put(mrpDetail.getMatrlno(),e.getMessage());
            }
        });
        return respVO;
    }
}

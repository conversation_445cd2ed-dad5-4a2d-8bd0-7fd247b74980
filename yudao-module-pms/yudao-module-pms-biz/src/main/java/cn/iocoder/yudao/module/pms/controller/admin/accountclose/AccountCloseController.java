package cn.iocoder.yudao.module.pms.controller.admin.accountclose;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;
import cn.iocoder.yudao.module.pms.controller.admin.accountclose.vo.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.accountclose.AccountCloseDO;
import cn.iocoder.yudao.module.pms.service.accountclose.AccountCloseService;

@Tag(name = "管理后台 - 关账日期")
@RestController
@RequestMapping("/pms/account-close")
@Validated
public class AccountCloseController {

    @Resource
    private AccountCloseService accountCloseService;

    @PostMapping("/create")
    @Operation(summary = "创建关账日期")
    @PreAuthorize("@ss.hasPermission('pms:account-close:create')")
    public CommonResult<Long> createAccountClose(@Valid @RequestBody AccountCloseSaveReqVO createReqVO) {
        return success(accountCloseService.createAccountClose(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新关账日期")
    @PreAuthorize("@ss.hasPermission('pms:account-close:update')")
    public CommonResult<Boolean> updateAccountClose(@Valid @RequestBody AccountCloseSaveReqVO updateReqVO) {
        accountCloseService.updateAccountClose(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除关账日期")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:account-close:delete')")
    public CommonResult<Boolean> deleteAccountClose(@RequestParam("id") Long id) {
        accountCloseService.deleteAccountClose(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得关账日期")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:account-close:query')")
    public CommonResult<AccountCloseRespVO> getAccountClose(@RequestParam("id") Long id) {
        AccountCloseDO accountClose = accountCloseService.getAccountClose(id);
        return success(BeanUtils.toBean(accountClose, AccountCloseRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得关账日期分页")
    @PreAuthorize("@ss.hasPermission('pms:account-close:query')")
    public CommonResult<PageResult<AccountCloseRespVO>> getAccountClosePage(@Valid AccountClosePageReqVO pageReqVO) {
        PageResult<AccountCloseDO> pageResult = accountCloseService.getAccountClosePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AccountCloseRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出关账日期 Excel")
    @PreAuthorize("@ss.hasPermission('pms:account-close:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAccountCloseExcel(@Valid AccountClosePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AccountCloseDO> list = accountCloseService.getAccountClosePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "关账日期.xls", "数据", AccountCloseRespVO.class,
                        BeanUtils.toBean(list, AccountCloseRespVO.class));
    }

    @GetMapping("/confirm")
    @Operation(summary = "确认关账")
    @PreAuthorize("@ss.hasPermission('pms:account-close:update')")
    public CommonResult<Boolean> confirmAccountClose(@RequestParam("id") Long id) {
        accountCloseService.confirmAccountClose(id);
        return success(true);
    }

}
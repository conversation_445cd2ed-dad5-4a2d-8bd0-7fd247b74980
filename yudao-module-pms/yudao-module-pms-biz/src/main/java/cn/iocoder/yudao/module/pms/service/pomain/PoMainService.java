package cn.iocoder.yudao.module.pms.service.pomain;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.pms.api.pomain.dto.PoMainReqDTO;
import cn.iocoder.yudao.module.pms.api.pomain.dto.PoMainRespDTO;
import cn.iocoder.yudao.module.pms.controller.admin.pomain.vo.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.other.Tbso011DO;
import cn.iocoder.yudao.module.pms.dal.dataobject.other.Tbso012DO;
import cn.iocoder.yudao.module.pms.dal.dataobject.pomain.PoDetailDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.pomain.PoMainDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.pomain.PoTocDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 合同信息 Service 接口
 *
 * <AUTHOR>
 */
public interface PoMainService {

    /**
     * 创建合同信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPoMain(@Valid PoMainSaveReqVO createReqVO);

    /**
     * 更新合同信息
     *
     * @param updateReqVO 更新信息
     */
    void updatePoMain(@Valid PoMainSaveReqVO updateReqVO);

    /**
     * 删除合同信息
     *
     * @param id 编号
     */
    void deletePoMain(Long id);

    /**
     * 获得合同信息
     *
     * @param id 编号
     * @return 合同信息
     */
    PoMainDO getPoMain(Long id);

    /**
     * 获得合同信息
     *
     * @param pono 根据合同编号获得合同信息
     * @return 合同信息
     */
    PoMainDO getPoMain(String pono);


    /**
     * 获得合同信息分页
     *
     * @param pageReqVO 分页查询
     * @return 合同信息分页
     */
    PageResult<PoMainDO> getPoMainPage(PoMainPageReqVO pageReqVO);

    void continuePoMainProcess(Long id);

    // ==================== 子表（合同明细） ====================

    /**
     * 获得合同明细分页
     *
     * @param pageReqVO 分页查询
     * @return 合同明细分页
     */
    PageResult<PoDetailDO> getPoDetailPage(PoDetailPageReqVO pageReqVO);

    /**
     * 创建合同明细
     *
     * @param poDetail 创建信息
     * @return 编号
     */
    Long createPoDetail(@Valid PoDetailDO poDetail);

    /**
     * 更新合同明细
     *
     * @param poDetail 更新信息
     */
    void updatePoDetail(@Valid PoDetailDO poDetail);

    /**
     * 删除合同明细
     *
     * @param id 编号
     */
    void deletePoDetail(Long id);

    /**
     * 获得合同明细
     *
     * @param id 编号
     * @return 合同明细
     */
    PoDetailDO getPoDetail(Long id);

    // ==================== 子表（合同条款内容） ====================

    /**
     * 获得合同条款内容分页
     *
     * @param pageReqVO 分页查询
     * @param parentId  父节点编号
     * @return 合同条款内容分页
     */
    PageResult<PoTocDO> getPoTocPage(PageParam pageReqVO, Long parentId);

    /**
     * 创建合同条款内容
     *
     * @param poToc 创建信息
     * @return 编号
     */
    Long createPoToc(@Valid PoTocDO poToc);

    /**
     * 更新合同条款内容
     *
     * @param poToc 更新信息
     */
    void updatePoToc(@Valid PoTocDO poToc);

    /**
     * 删除合同条款内容
     *
     * @param id 编号
     */
    void deletePoToc(Long id);

    /**
     * 获得合同条款内容
     *
     * @param id 编号
     * @return 合同条款内容
     */
    PoTocDO getPoToc(Long id);

    void validatePoMainExists(Long id);

    List<PoMainRespDTO> getPoMainPageByVendor(PoMainReqDTO reqDTO);

    PageResult<PoDetailDO> getPoDetailPageByParams(PoDetailPageReqVO pageReqVO);


    PageResult<PoDetailRespVO> getShipPoDetailPage(PoDetailPageReqVO pageReqVO);

    /**
     * 根据料号和合同号 获得合同明细记录，要求主表状态大于‘F’
     */
    PoDetailDO getDetailByMatrlnoAndPono(String matrlno, String contractno);

    String getPoTocCNTotalAmt(Long parentId);

    void batchInsertPoDetailsByPurId(PoDetailsBatchInsertReqVO reqVO);

    void batchInsertPoDetailsByMatrlId(PoDetailsBatchInsertByMatrlReqVO reqVO);

    PageResult<PoDetailRespVO> getPoDetailPage2(PoDetailPageReqVO pageReqVO);

    PageResult<PoDetailRespVO> getPoDetailConfirmPage(PoDetailPageReqVO pageReqVO);

    PageResult<PoDetailRespVO> getPoDetailYPage(PoDetailPageReqVO pageReqVO);

    String createChildPo(Long id);

    List<PoDetailDO> getPoDetailPageFee(String pono);

    void endCasePo(Long id, String flag);

    PageResult<PoPayInfoRespVO> getPayInfo(PoDetailPageReqVO pageReqVO);

    PageResult<PoPayInfoRespVO> getPayInfoY(PoDetailPageReqVO pageReqVO);

    List<PoMonthlyRespVO> getMonthly(String startDate, String endDate);

    List<PoUpComingRespVO> getUpcoming();

    List<PoUpComingRespVO> getOneDayPayInfo(String day);

    Tbso011DO getOrderInfo(Long parentId);

    Long createOrderInfo(Tbso011DO tbso011DO);

    void updateOrderInfo(Tbso011DO tbso011DO);

    Tbso012DO getOrderDetail(Long parentId);

    Long createOrderDetail(Tbso012DO tbso012DO);

    void updateOrderDetail(Tbso012DO tbso012DO);

    PageResult<Tbso012DO> getOrderDetailPage(PageParam pageReqVO, Long parentId);

    void batchInsertPoDetailByInq(PoDetailsBatchInsertReqVO reqVO);

    void pushErpPo(Long id);

    void batchInserOrderDetail(PoDetailsBatchInsertReqVO reqVO);

    void deleteOrderDetail(Long id);

    PageResult<PoDetailRespVO> getDetailPage(PoDetailPageReqVO pageReqVO);
}

package cn.iocoder.yudao.module.pms.controller.admin.paramsinfo.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 配置信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ParamsInfoPageReqVO extends PageParam {

    private String paramId;
    @Schema(description = "第B栏内容")
    private String valuea;

    @Schema(description = "第A栏内容")
    private String valueb;

    @Schema(description = "第C栏内容")
    private String valuec;

    @Schema(description = "第D栏内容")
    private String valued;

    @Schema(description = "第E栏内容")
    private String valuee;

    @Schema(description = "第F栏内容")
    private String valuef;

    @Schema(description = "第G栏内容")
    private String valueg;

    @Schema(description = "第H栏内容")
    private String valueh;

    @Schema(description = "第I栏内容")
    private String valuei;

    @Schema(description = "第J栏内容")
    private String valuej;

    @Schema(description = "第K栏内容")
    private String valuek;

    @Schema(description = "第L栏内容")
    private String valuel;

    /** 请求参数 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Map<String, String> order;

}
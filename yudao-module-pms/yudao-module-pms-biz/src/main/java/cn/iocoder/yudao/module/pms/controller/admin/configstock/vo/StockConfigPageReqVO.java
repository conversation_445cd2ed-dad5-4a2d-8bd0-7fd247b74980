package cn.iocoder.yudao.module.pms.controller.admin.configstock.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 库存配置管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class StockConfigPageReqVO extends PageParam {

    @Schema(description = "编号")
    private Long id;

    @Schema(description = "项目号")
    private String project;

    @Schema(description = "公司别")
    private String compid;

    @Schema(description = "物料类别")
    private String inventoryType;

    @Schema(description = "料号")
    private String matrlno;

    @Schema(description = "是否批次")
    private String isBatch;

    @Schema(description = "是否合同")
    private String isPo;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createDate;

    @Schema(description = "创建人")
    private String createEmpno;

    @Schema(description = "创建者")
    private String creator;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
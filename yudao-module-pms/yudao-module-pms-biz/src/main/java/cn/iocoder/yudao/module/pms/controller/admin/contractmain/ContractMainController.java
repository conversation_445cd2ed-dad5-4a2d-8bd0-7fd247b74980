package cn.iocoder.yudao.module.pms.controller.admin.contractmain;

import com.alibaba.fastjson.JSONObject;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.security.PermitAll;
import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.pms.controller.admin.contractmain.vo.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.contractmain.ContractMainDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.contractmain.ContractSealDO;
import cn.iocoder.yudao.module.pms.service.contractmain.ContractMainService;

@Tag(name = "管理后台 - 法务合同基本信息")
@RestController
@RequestMapping("/pms/contract-main")
@Validated
public class ContractMainController {

    @Resource
    private ContractMainService contractMainService;

    @PostMapping("/create")
    @Operation(summary = "创建法务合同基本信息")
    @PreAuthorize("@ss.hasPermission('pms:contract-main:create')")
    public CommonResult<ContractMainRespVO> createContractMain(@Valid @RequestBody ContractMainSaveReqVO createReqVO) {
        return success(BeanUtils.toBean(contractMainService.createContractMain(createReqVO), ContractMainRespVO.class));
    }

    @PutMapping("/update")
    @Operation(summary = "更新法务合同基本信息")
    @PreAuthorize("@ss.hasPermission('pms:contract-main:update')")
    public CommonResult<ContractMainRespVO> updateContractMain(@Valid @RequestBody ContractMainSaveReqVO updateReqVO) {
        return success(BeanUtils.toBean(contractMainService.updateContractMain(updateReqVO), ContractMainRespVO.class));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除法务合同基本信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:contract-main:delete')")
    public CommonResult<Boolean> deleteContractMain(@RequestParam("id") Long id) {
        contractMainService.deleteContractMain(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得法务合同基本信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:contract-main:query')")
    public CommonResult<ContractMainRespVO> getContractMain(@RequestParam(value = "id",required = false) Long id,@RequestParam(value = "poNo",required = false) String poNo,@RequestParam(value = "poVer",required = false) String poVer) {
        ContractMainDO contractMain;
        if(id!=null) {
            contractMain = contractMainService.getContractMain(id);
        } else {
            contractMain = contractMainService.getContractMain(poNo,poVer);
        }
        return success(BeanUtils.toBean(contractMain, ContractMainRespVO.class));
    }

    @GetMapping("/getAut")
    @Operation(summary = "获得授权信息")
    @PreAuthorize("@ss.hasPermission('pms:contract-main:query')")
    public CommonResult<Map<String,String>> getAut(@RequestParam(value = "userId",required = false) Long userId) {

        return success( contractMainService.selectAut(userId));
    }
    @GetMapping("/getSeal")
    @Operation(summary = "获得授权信息")
    @PreAuthorize("@ss.hasPermission('pms:contract-main:query')")
    public CommonResult<List<SealRespVO>> getSeal(@RequestParam(value = "userId",required = false) Long userId) {

        return success( contractMainService.selectSeal(userId));
    }
    @PostMapping("/sendFw")
    @Operation(summary = "抛送法务")
    @PreAuthorize("@ss.hasPermission('pms:contract-main:create')")
    public CommonResult<Boolean> sendFw(@Valid @RequestBody ContractMainSaveReqVO createReqVO) {
        contractMainService.sendFw(createReqVO);
        return success(true);
    }
    @PostMapping("/sendPerform")
    @Operation(summary = "履行完成")
    @PreAuthorize("@ss.hasPermission('pms:contract-main:create')")
    public CommonResult<Boolean> sendPerform(@Valid @RequestBody ContractMainSaveReqVO createReqVO) {
        contractMainService.sendPerform(createReqVO.getContractIdName(),createReqVO.getStus());
        return success(true);
    }

    public static final String access_token="OGZ6aHZaS3kqY2ZXdk1yM1VWSkIwMFljd3JZcUNS1R9sFWJHMypea25mS1AlcDJwEX5PSWTrZ3VQUjcqcX5wR21AaTZsdHooV3RfSEtAbHBoVTZRUXVxa01rMGY5dXpQS3RxdVNOUiN6a3lZTXhrS2FjcURqKWUhTGdBIXJWS0g=";

    /**
     * {
     *     "code": 100000,
     *     "data": {
     *         "thirdVersion": "00",
     *         "thirdContractNo": "OA124120001",
     *         "contractId": "9fef21c5c5c84a10a7136e5664083d5e",
     *         "contractCode": "A20000022XS02241200012V00",
     *         "contractTakeEffectDate": "2024-12-01",
     *         "fileId": "objectKey.pdf",
     *     },
     *     "message": "success"
     * }
     * @param fwResultReqVO
     * @return
     */
    @PermitAll
    @PostMapping("/updatePoIdByGet")
    @Operation(summary = "接收法务结果")
    public CommonResult<JSONObject> updatePoIdByGet(@RequestHeader("x-access-token") String token,@Valid @RequestBody FwResultReqVO fwResultReqVO) {

        JSONObject result = new JSONObject();
        try{
            if(!access_token.equals(token)){
                throw new IllegalArgumentException("token值失效");
            }
            contractMainService.updatePoIdByGet(fwResultReqVO);
            result.put("code",100000);
            result.put("data","推送成功！");
            result.put("message","success");
        } catch (Exception e){
            result.put("CODE",100001);
            result.put("message","failure");
            result.put("data",e.getMessage());
        }


        return success(result);
    }
    // ==================== 子表（合同签章子） ====================

    @GetMapping("/contract-seal/page")
    @Operation(summary = "获得合同签章子分页")
    @Parameter(name = "mainId", description = "主表主键")
    @PreAuthorize("@ss.hasPermission('pms:contract-main:query')")
    public CommonResult<PageResult<ContractSealDO>> getContractSealPage(PageParam pageReqVO,
                                                                                        @RequestParam("mainId") Long mainId) {
        return success(contractMainService.getContractSealPage(pageReqVO, mainId));
    }

    @PostMapping("/contract-seal/create")
    @Operation(summary = "创建合同签章子")
    @PreAuthorize("@ss.hasPermission('pms:contract-main:create')")
    public CommonResult<Long> createContractSeal(@Valid @RequestBody ContractSealDO contractSeal) {
        return success(contractMainService.createContractSeal(contractSeal));
    }

    @PutMapping("/contract-seal/update")
    @Operation(summary = "更新合同签章子")
    @PreAuthorize("@ss.hasPermission('pms:contract-main:update')")
    public CommonResult<Boolean> updateContractSeal(@Valid @RequestBody ContractSealDO contractSeal) {
        contractMainService.updateContractSeal(contractSeal);
        return success(true);
    }

    @DeleteMapping("/contract-seal/delete")
    @Parameter(name = "id", description = "编号", required = true)
    @Operation(summary = "删除合同签章子")
    @PreAuthorize("@ss.hasPermission('pms:contract-main:delete')")
    public CommonResult<Boolean> deleteContractSeal(@RequestParam("id") Long id) {
        contractMainService.deleteContractSeal(id);
        return success(true);
    }

	@GetMapping("/contract-seal/get")
	@Operation(summary = "获得合同签章子")
	@Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:contract-main:query')")
	public CommonResult<ContractSealDO> getContractSeal(@RequestParam("id") Long id) {
	    return success(contractMainService.getContractSeal(id));
	}

}

package cn.iocoder.yudao.module.pms.service.apmain;

import javax.validation.*;
import cn.iocoder.yudao.module.pms.controller.admin.apmain.vo.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.apmain.ApMainDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.apmain.ApDetailDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.apmain.ApDetail2DO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 报支记录主档 Service 接口
 *
 * <AUTHOR>
 */
public interface ApMainService {

    /**
     * 创建报支记录主档
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createApMain(@Valid ApMainSaveReqVO createReqVO);

    /**
     * 更新报支记录主档
     *
     * @param updateReqVO 更新信息
     */
    void updateApMain(@Valid ApMainSaveReqVO updateReqVO);

    /**
     * 删除报支记录主档
     *
     * @param id 编号
     */
    void deleteApMain(Long id);

    /**
     * 获得报支记录主档
     *
     * @param id 编号
     * @return 报支记录主档
     */
    ApMainDO getApMain(Long id);

    /**
     * 获得报支记录主档分页
     *
     * @param pageReqVO 分页查询
     * @return 报支记录主档分页
     */
    PageResult<ApMainDO> getApMainPage(ApMainPageReqVO pageReqVO);

    // ==================== 子表（报支记录明细档） ====================

    /**
     * 获得报支记录明细档分页
     *
     * @param pageReqVO 分页查询
     * @param parentid PARENTID
     * @return 报支记录明细档分页
     */
    PageResult<ApDetailDO> getApDetailPage(PageParam pageReqVO, Long parentid);

    /**
     * 创建报支记录明细档
     *
     * @param apDetail 创建信息
     * @return 编号
     */
    Long createApDetail(@Valid ApDetailDO apDetail);

    /**
     * 更新报支记录明细档
     *
     * @param apDetail 更新信息
     */
    void updateApDetail(@Valid ApDetailDO apDetail);

    /**
     * 删除报支记录明细档
     *
     * @param id 编号
     */
    void deleteApDetail(Long id);

	/**
	 * 获得报支记录明细档
	 *
	 * @param id 编号
     * @return 报支记录明细档
	 */
    ApDetailDO getApDetail(Long id);

    // ==================== 子表（报支记录凭证档） ====================

    /**
     * 获得报支记录凭证档分页
     *
     * @param pageReqVO 分页查询
     * @param parentid PARENTID
     * @return 报支记录凭证档分页
     */
    PageResult<ApDetail2DO> getApDetail2Page(PageParam pageReqVO, Long parentid);

    /**
     * 创建报支记录凭证档
     *
     * @param apDetail2 创建信息
     * @return 编号
     */
    Long createApDetail2(@Valid ApDetail2DO apDetail2);

    /**
     * 更新报支记录凭证档
     *
     * @param apDetail2 更新信息
     */
    void updateApDetail2(@Valid ApDetail2DO apDetail2);

    /**
     * 删除报支记录凭证档
     *
     * @param id 编号
     */
    void deleteApDetail2(Long id);

	/**
	 * 获得报支记录凭证档
	 *
	 * @param id 编号
     * @return 报支记录凭证档
	 */
    ApDetail2DO getApDetail2(Long id);

    void batchOperateApDetail2(ApMainDetail2BatchOperateReqVO apDetail2);

    void batchOperateApDetail1(ApMainDetail1BatchOperateReqVO apDetail1);

    void confirmApMain(Long id);

    String getApDetailTotal(Long parentid);

    void batchOperateApDetail1YF(ApMainDetail1BatchOperateReqVO apDetail1);

    void confirmApMainYF(Long id);

    void cancelConfirmApMain(Long id);

    void cancelConfirmApMainYF(Long id);

    ApDetailDO getApPayInfo(Long id);

    Long createApPayInfo(ApPayInfoSaveReqVO reqVO);

    void updateApPayInfo(ApPayInfoSaveReqVO reqVO);
}
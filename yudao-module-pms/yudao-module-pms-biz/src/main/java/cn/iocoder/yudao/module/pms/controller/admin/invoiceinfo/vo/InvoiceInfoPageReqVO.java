package cn.iocoder.yudao.module.pms.controller.admin.invoiceinfo.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 发票信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InvoiceInfoPageReqVO extends PageParam {

    @Schema(description = "申请单号")
    private String issueNo;

    @Schema(description = "发票号")
    private String voucherNo;

    @Schema(description = "发票日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] voucherDate;

    @Schema(description = "发票性质")
    private String voucherType;

    @Schema(description = "厂商编号")
    private String vendorNo;

    @Schema(description = "合同编号")
    private String poNo;

    @Schema(description = "装运通知单")
    private String inputTransNo;

    @Schema(description = "厂商名称")
    private String vendorName;

    @Schema(description = "状态")
    private String poStus;

    @Schema(description = "新增人员")
    private String createEmpNo;

    @Schema(description = "新增日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] createDate;

    @Schema(description = "修改人员")
    private String updateEmpNo;

    @Schema(description = "修改日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] updateDate;

    @Schema(description = "新增时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
package cn.iocoder.yudao.module.pms.controller.admin.locdetailmoncumular.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 验收批次月累计收发新增/修改 Request VO")
@Data
public class LocDetailMonCumularSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "项目号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "项目号不能为空")
    private String project;

    @Schema(description = "公司别", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "公司别不能为空")
    private String compid;

    @Schema(description = "库存年月", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "库存年月不能为空")
    private String yearmo;

    @Schema(description = "料号")
    private String matrlno;

    @Schema(description = "品级(备件属性)")
    private String matrlgrade;

    @Schema(description = "品别")
    private String inventorytype;

    @Schema(description = "储位")
    private String locno;

    @Schema(description = "批次")
    private String batch;

    @Schema(description = "期末库存量")
    private BigDecimal endqty;

    @Schema(description = "期末库存金额")
    private BigDecimal endamt;

    @Schema(description = "月初库存量")
    private BigDecimal mobeginqty;

    @Schema(description = "月初库存金额")
    private BigDecimal mobeginamt;

    @Schema(description = "月累积入库量")
    private BigDecimal moaccurecvqty;

    @Schema(description = "月累积入库金额")
    private BigDecimal moaccurecvamt;

    @Schema(description = "月累积出库量")
    private BigDecimal moaccuissuqty;

    @Schema(description = "月累积出库金额")
    private BigDecimal moaccuissuamt;

    @Schema(description = "月调整入库量")
    private BigDecimal moaccuadjrecvqty;

    @Schema(description = "月调整入库金额")
    private BigDecimal moaccuadjrecvamt;

    @Schema(description = "月调整出库量")
    private BigDecimal moaccuadjissuqty;

    @Schema(description = "月调整出库金额")
    private BigDecimal moaccuadjissuamt;

    @Schema(description = "年初库存量")
    private BigDecimal yrbeginqty;

    @Schema(description = "年初库存金额")
    private BigDecimal yrbeginamt;

    @Schema(description = "年累积入库量")
    private BigDecimal yraccurecvqty;

    @Schema(description = "年累积入库金额")
    private BigDecimal yraccurecvamt;

    @Schema(description = "年累积出库量")
    private BigDecimal yraccuissuqty;

    @Schema(description = "年累积出库金额")
    private BigDecimal yraccuissuamt;

    @Schema(description = "年调整入库量")
    private BigDecimal yraccuadjrecvqty;

    @Schema(description = "年调整入库金额")
    private BigDecimal yraccuadjrecvamt;

    @Schema(description = "年调整出库量")
    private BigDecimal yraccuadjissuqty;

    @Schema(description = "年调整出库金额")
    private BigDecimal yraccuadjissuamt;

    @Schema(description = "备注")
    private String remark;

}
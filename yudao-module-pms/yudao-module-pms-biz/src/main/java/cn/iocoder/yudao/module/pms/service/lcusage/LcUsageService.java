package cn.iocoder.yudao.module.pms.service.lcusage;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.pms.controller.admin.lcusage.vo.LcUsagePageReqVO;
import cn.iocoder.yudao.module.pms.controller.admin.lcusage.vo.LcUsageSaveReqVO;
import cn.iocoder.yudao.module.pms.dal.dataobject.lcusage.LcUsageDO;

import javax.validation.Valid;

/**
 * 信用证使用情况 Service 接口
 *
 * <AUTHOR>
 */
public interface LcUsageService {

    /**
     * 创建信用证使用情况
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createLcUsage(@Valid LcUsageSaveReqVO createReqVO);

    /**
     * 更新信用证使用情况
     *
     * @param updateReqVO 更新信息
     */
    void updateLcUsage(@Valid LcUsageSaveReqVO updateReqVO);

    /**
     * 删除信用证使用情况
     *
     * @param id 编号
     */
    void deleteLcUsage(Long id);

    /**
     * 获得信用证使用情况
     *
     * @param id 编号
     * @return 信用证使用情况
     */
    LcUsageDO getLcUsage(Long id);

    /**
     * 获得信用证使用情况分页
     *
     * @param pageReqVO 分页查询
     * @return 信用证使用情况分页
     */
    PageResult<LcUsageDO> getLcUsagePage(LcUsagePageReqVO pageReqVO);

}
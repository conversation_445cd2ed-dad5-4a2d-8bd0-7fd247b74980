package cn.iocoder.yudao.module.pms.controller.admin.mpp.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 集采信息主表新增/修改 Request VO")
@Data
public class ProcurementCollectionSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "集采编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "JC202401001")
    @NotEmpty(message = "集采编号不能为空")
    private String collectionNo;

    @Schema(description = "公司别", requiredMode = Schema.RequiredMode.REQUIRED, example = "001")
    @NotEmpty(message = "公司别不能为空")
    private String companyCode;

    @Schema(description = "版本号", example = "1.0")
    private String versionNo;

    @Schema(description = "建立人职工编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "EMP001")
    @NotEmpty(message = "建立人职工编号不能为空")
    private String creatorEmpNo;

    @Schema(description = "建立日期")
    private LocalDateTime createDate;

    @Schema(description = "修改人职工编号", example = "EMP002")
    private String updaterEmpNo;

    @Schema(description = "修改日期")
    private LocalDateTime updateDate;

    @Schema(description = "结案时间")
    private LocalDateTime closeTime;

    @Schema(description = "生效日期")
    private LocalDateTime effectiveDate;

    @Schema(description = "生效状态", example = "1")
    private Integer effectiveStatus;

    @Schema(description = "备注", example = "这是一个集采项目")
    private String remark;
}

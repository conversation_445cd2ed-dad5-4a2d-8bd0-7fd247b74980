package cn.iocoder.yudao.module.pms.controller.admin.demandplanaudit.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 需求计划申报工作流关联分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DemandPlanAuditPageReqVO extends PageParam {

    @Schema(description = "需求计划申报表 ID", example = "2388")
    private Long parentId;

    @Schema(description = "计划行号", example = "15909")
    private String mrLineId;

    @Schema(description = "作业部计划员ID", example = "7665")
    private String applyUserId;

    @Schema(description = "作业部计划员姓名", example = "赵六")
    private String applyUserName;

    @Schema(description = "流程实例的编号", example = "2055")
    private String processInstanceId;

    @Schema(description = "新增时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
package cn.iocoder.yudao.module.pms.controller.admin.mpp.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 集采信息主表分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProcurementCollectionPageReqVO extends PageParam {

    @Schema(description = "集采编号", example = "JC202401001")
    private String collectionNo;

    @Schema(description = "公司别", example = "001")
    private String companyCode;

    @Schema(description = "生效状态", example = "1")
    private Integer effectiveStatus;

    @Schema(description = "建立日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createDate;

    @Schema(description = "生效日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] effectiveDate;

    @Schema(description = "建立人职工编号", example = "EMP001")
    private String creatorEmpNo;

    @Schema(description = "版本号", example = "1.0")
    private String versionNo;
}

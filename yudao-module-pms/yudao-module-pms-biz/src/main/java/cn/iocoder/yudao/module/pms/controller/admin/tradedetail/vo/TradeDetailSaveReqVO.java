package cn.iocoder.yudao.module.pms.controller.admin.tradedetail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 交易申请明细新增/修改 Request VO")
@Data
public class TradeDetailSaveReqVO {

    @Schema(description = "库存ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "项目号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "项目号不能为空")
    private String project;

    @Schema(description = "公司别", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "公司别不能为空")
    private String compid;

    @Schema(description = "交易单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "交易单号不能为空")
    private String issuetallyno;

    @Schema(description = "项次序号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "项次序号不能为空")
    private String seqno;

    @Schema(description = "验收单")
    private String trantallyno;

    @Schema(description = "合同编号")
    private String contractno;

    @Schema(description = "计划单号")
    private String plantallyno;

    @Schema(description = "供应商代码")
    private String supplierno;

    @Schema(description = "储位")
    private String locno;

    @Schema(description = "批号")
    private String lotno;

    @Schema(description = "料号")
    private String matrlno;

    @Schema(description = "品级")
    private String matrlgrade;

    @Schema(description = "品别")
    private String inventorytype;

    @Schema(description = "交易种类")
    private String issuetype;

    @Schema(description = "交易数量")
    private BigDecimal issueqty;

    @Schema(description = "交易金额")
    private BigDecimal issueamt;

    @Schema(description = "销售金额")
    private BigDecimal salesamt;

    @Schema(description = "移入储区")
    private String importlocno;

    @Schema(description = "调整入批号")
    private String importlotno;

    @Schema(description = "状况码")
    private String stus;

    @Schema(description = "会计科目")
    private String acctcode;

    @Schema(description = "成本科目")
    private String costcode;

    @Schema(description = "会计科目")
    private String acctcodeDr;

    @Schema(description = "是否抛成本系统")
    private String isthrowac;

    @Schema(description = "库存量")
    private BigDecimal inventoryqty;

    @Schema(description = "库存金额")
    private BigDecimal inventoryamt;

    @Schema(description = "盘点量")
    private BigDecimal checkqty;

    @Schema(description = "盘点金额")
    private BigDecimal checkamt;

    @Schema(description = "库存单位")
    private String unitinv;

    @Schema(description = "调整前料号")
    private String preadjustmtrlid;

    @Schema(description = "调整后料号")
    private String aftadjustmtrlid;

    @Schema(description = "尚末发料量")
    private BigDecimal accupretranqty;

    @Schema(description = "残值比")
    private String remnantper;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建人职工编号")
    private String createEmpno;

    @Schema(description = "创建日期")
    private String createDate;

    @Schema(description = "修改人职工编号")
    private String updateEmpno;

    @Schema(description = "修改日期")
    private String updateDate;

}
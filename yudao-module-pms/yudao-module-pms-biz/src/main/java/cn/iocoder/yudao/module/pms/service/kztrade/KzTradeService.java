package cn.iocoder.yudao.module.pms.service.kztrade;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.pms.controller.admin.kztrade.vo.*;
import cn.iocoder.yudao.module.pms.controller.admin.mpp.vo.MppDetailImportRespVO;
import cn.iocoder.yudao.module.pms.controller.admin.mpp.vo.MppDetailImportVO;
import cn.iocoder.yudao.module.pms.dal.dataobject.kztrade.KzTradeDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.kztrade.KzTradeDetailDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 期初开账 Service 接口
 *
 * <AUTHOR>
 */
public interface KzTradeService {

    /**
     * 创建期初开账
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createKzTrade(@Valid KzTradeSaveReqVO createReqVO);

    /**
     * 更新期初开账
     *
     * @param updateReqVO 更新信息
     */
    void updateKzTrade(@Valid KzTradeSaveReqVO updateReqVO);
    void updateKzTradeStus(@Valid KzTradeSaveReqVO updateReqVO);
    /**
     * 删除期初开账
     *
     * @param id 编号
     */
    void deleteKzTrade(Long id);

    /**
     * 获得期初开账
     *
     * @param id 编号
     * @return 期初开账
     */
    KzTradeDO getKzTrade(Long id);

    /**
     * 获得期初开账分页
     *
     * @param pageReqVO 分页查询
     * @return 期初开账分页
     */
    PageResult<KzTradeDO> getKzTradePage(KzTradePageReqVO pageReqVO);

    // ==================== 子表（开账管理明细） ====================

    /**
     * 获得开账管理明细分页
     *
     * @param pageReqVO 分页查询
     * @param parentId 主表ID
     * @return 开账管理明细分页
     */
    PageResult<KzTradeDetailDO> getKzTradeDetailPage(PageParam pageReqVO, Long parentId);

    /**
     * 创建开账管理明细
     *
     * @param kzTradeDetail 创建信息
     * @return 编号
     */
    Long createKzTradeDetail(@Valid KzTradeDetailDO kzTradeDetail);

    /**
     * 更新开账管理明细
     *
     * @param kzTradeDetail 更新信息
     */
    void updateKzTradeDetail(@Valid KzTradeDetailDO kzTradeDetail);
    void updateKzTradeDetailConfirm(@Valid KzTradeDetailDO kzTradeDetail);
    /**
     * 删除开账管理明细
     *
     * @param id 编号
     */
    void deleteKzTradeDetail(Long id);

	/**
	 * 获得开账管理明细
	 *
	 * @param id 编号
     * @return 开账管理明细
	 */
    KzTradeDetailDO getKzTradeDetail(Long id);
    KzTradeDetailImportRespVO importKzTradeDetailList(List<KzTradeDetailImportVO> list, Long parentId, Boolean updateSupport);
}
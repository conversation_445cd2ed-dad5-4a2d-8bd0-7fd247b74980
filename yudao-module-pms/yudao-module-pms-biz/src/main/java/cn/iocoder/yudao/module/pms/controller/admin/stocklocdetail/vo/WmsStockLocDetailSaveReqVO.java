package cn.iocoder.yudao.module.pms.controller.admin.stocklocdetail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 实时库存明细新增/修改 Request VO")
@Data
public class WmsStockLocDetailSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "24189")
    private Long id;

    @Schema(description = "项目号")
    private String project;

    @Schema(description = "公司别", example = "19605")
    private String compid;

    @Schema(description = "料号")
    private String matrlno;

    @Schema(description = "品级（备件属性）")
    private String matrlgrade;

    @Schema(description = "品别", example = "2")
    private String inventorytype;

    @Schema(description = "储位")
    private String locno;

    @Schema(description = "批次")
    private String batch;

    @Schema(description = "期末库存量")
    private BigDecimal endqty;

    @Schema(description = "期末库存金额")
    private BigDecimal endamt;

    @Schema(description = "单价", example = "5035")
    private BigDecimal unitprice;

    @Schema(description = "已申请数量")
    private BigDecimal issueqty;

    @Schema(description = "在途量")
    private BigDecimal routeqty;

    @Schema(description = "计划单号")
    private String plantallyno;

    @Schema(description = "厂商编号")
    private String supplierno;

    @Schema(description = "合同编号")
    private String contractno;

    @Schema(description = "帐龄天数")
    private String debtagedays;

    @Schema(description = "帐龄类别", example = "2")
    private String debtagetype;

    @Schema(description = "报警天数")
    private String prewarnmonth;

    @Schema(description = "批号")
    private String lotno;

    @Schema(description = "仓库")
    private String storageno;

    @Schema(description = "仓库保管员")
    private String stgempno;

    @Schema(description = "入库日期")
    private LocalDateTime indate;

    @Schema(description = "入库操作人")
    private String inempno;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "备用字段")
    private String fields1;

    @Schema(description = "备用字段")
    private String fields2;

    @Schema(description = "备用字段")
    private String fields3;

    @Schema(description = "备用字段")
    private String fields4;

    @Schema(description = "备用字段")
    private String fields5;

    @Schema(description = "备用字段")
    private Integer fields6;

    @Schema(description = "备用字段")
    private Integer fields7;

    @Schema(description = "备用字段")
    private Integer fields8;

    @Schema(description = "备用字段")
    private LocalDateTime fields9;

    @Schema(description = "备用字段")
    private LocalDateTime fields10;

    @Schema(description = "备用字段")
    private LocalDateTime fields11;

    @Schema(description = "备用字段")
    private LocalDateTime fields12;

    @Schema(description = "备用字段")
    private BigDecimal fields13;

    @Schema(description = "备用字段")
    private BigDecimal fields14;

    @Schema(description = "备用字段")
    private BigDecimal fields15;

}
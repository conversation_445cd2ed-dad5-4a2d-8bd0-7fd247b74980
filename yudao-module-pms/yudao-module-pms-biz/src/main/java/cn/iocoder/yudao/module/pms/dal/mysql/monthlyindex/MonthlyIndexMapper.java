package cn.iocoder.yudao.module.pms.dal.mysql.monthlyindex;

import java.math.BigDecimal;
import java.time.LocalDate;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.MPJLambdaWrapperX;
import cn.iocoder.yudao.module.pms.dal.dataobject.monthlyindex.MonthlyIndexDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.pms.controller.admin.monthlyindex.vo.*;



/**
 * 日月指数 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MonthlyIndexMapper extends BaseMapperX<MonthlyIndexDO> {

    default PageResult<MonthlyIndexDO> selectPage(MonthlyIndexPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MonthlyIndexDO>()
                .betweenIfPresent(MonthlyIndexDO::getRecordDate, reqVO.getRecordDate())
                .eqIfPresent(MonthlyIndexDO::getIndexType, reqVO.getIndexType())
                .likeIfPresent(MonthlyIndexDO::getTypeName, reqVO.getTypeName())
                .betweenIfPresent(MonthlyIndexDO::getStartDate, reqVO.getStartDate())
                .betweenIfPresent(MonthlyIndexDO::getEndDate, reqVO.getEndDate())
                .eqIfPresent(MonthlyIndexDO::getIndexValue1, reqVO.getIndexValue1())
                .eqIfPresent(MonthlyIndexDO::getIndexValue2, reqVO.getIndexValue2())
                .eqIfPresent(MonthlyIndexDO::getIndexValue3, reqVO.getIndexValue3())
                .eqIfPresent(MonthlyIndexDO::getIndexValue4, reqVO.getIndexValue4())
                .eqIfPresent(MonthlyIndexDO::getIndexValue5, reqVO.getIndexValue5())
                .eqIfPresent(MonthlyIndexDO::getIndexValue6, reqVO.getIndexValue6())
                .eqIfPresent(MonthlyIndexDO::getIndexValue7, reqVO.getIndexValue7())
                .eqIfPresent(MonthlyIndexDO::getIndexValue8, reqVO.getIndexValue8())
                .eqIfPresent(MonthlyIndexDO::getIndexValue9, reqVO.getIndexValue9())
                .eqIfPresent(MonthlyIndexDO::getIndexValue10, reqVO.getIndexValue10())
                .eqIfPresent(MonthlyIndexDO::getIndexValue11, reqVO.getIndexValue11())
                .eqIfPresent(MonthlyIndexDO::getIndexValue12, reqVO.getIndexValue12())
                .eqIfPresent(MonthlyIndexDO::getIndexValue13, reqVO.getIndexValue13())
                .eqIfPresent(MonthlyIndexDO::getIndexValue14, reqVO.getIndexValue14())
                .eqIfPresent(MonthlyIndexDO::getIndexValue15, reqVO.getIndexValue15())
                .eqIfPresent(MonthlyIndexDO::getIndexValue16, reqVO.getIndexValue16())
                .eqIfPresent(MonthlyIndexDO::getIndexValue17, reqVO.getIndexValue17())
                .eqIfPresent(MonthlyIndexDO::getIndexValue18, reqVO.getIndexValue18())
                .eqIfPresent(MonthlyIndexDO::getIndexValue19, reqVO.getIndexValue19())
                .eqIfPresent(MonthlyIndexDO::getIndexValue20, reqVO.getIndexValue20())
                .eqIfPresent(MonthlyIndexDO::getBackup1, reqVO.getBackup1())
                .eqIfPresent(MonthlyIndexDO::getBackup2, reqVO.getBackup2())
                .eqIfPresent(MonthlyIndexDO::getBackup3, reqVO.getBackup3())
                .eqIfPresent(MonthlyIndexDO::getBackup4, reqVO.getBackup4())
                .eqIfPresent(MonthlyIndexDO::getBackup5, reqVO.getBackup5())
                .eqIfPresent(MonthlyIndexDO::getBackup6, reqVO.getBackup6())
                .eqIfPresent(MonthlyIndexDO::getBackup7, reqVO.getBackup7())
                .eqIfPresent(MonthlyIndexDO::getBackup8, reqVO.getBackup8())
                .eqIfPresent(MonthlyIndexDO::getBackup9, reqVO.getBackup9())
                .eqIfPresent(MonthlyIndexDO::getBackup10, reqVO.getBackup10())
                .betweenIfPresent(MonthlyIndexDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(MonthlyIndexDO::getRecordDate));
    }

//    default MonthlyIndexDO selectDayIndexByDate(LocalDate recordDate){
//        return selectOne(new LambdaQueryWrapperX<MonthlyIndexDO>()
//                .eq(MonthlyIndexDO::getRecordDate,recordDate)
//                .eq(MonthlyIndexDO::getIndexType,1));
//    };
    default MonthlyIndexDO selectMonthIndexByDate(LocalDate recordDate,Integer indexType){
        return selectOne(new LambdaQueryWrapperX<MonthlyIndexDO>()
                .eq(MonthlyIndexDO::getRecordDate,recordDate)
                .eq(MonthlyIndexDO::getIndexType,indexType));
    };

    default MonthlyIndexDO calcAvg(MonthlyIndexPageReqVO reqVO){
        return selectOne(new MPJLambdaWrapperX<MonthlyIndexDO>()
                .selectAvg(MonthlyIndexDO::getIndexValue1 ,"index_value1")
                .selectAvg(MonthlyIndexDO::getIndexValue2 ,"index_value2")
                .selectAvg(MonthlyIndexDO::getIndexValue3 ,"index_value3")
                .selectAvg(MonthlyIndexDO::getIndexValue4 ,"index_value4")
                .selectAvg(MonthlyIndexDO::getIndexValue5 ,"index_value5")
                .selectAvg(MonthlyIndexDO::getIndexValue6 ,"index_value6")
                .selectAvg(MonthlyIndexDO::getIndexValue7 ,"index_value7")
                .selectAvg(MonthlyIndexDO::getIndexValue8 ,"index_value8")
                .selectAvg(MonthlyIndexDO::getIndexValue9 ,"index_value9")
                .selectAvg(MonthlyIndexDO::getIndexValue10,"index_value10")
                .selectAvg(MonthlyIndexDO::getIndexValue11,"index_value11")
                .selectAvg(MonthlyIndexDO::getIndexValue12,"index_value12")
                .selectAvg(MonthlyIndexDO::getIndexValue13,"index_value13")
                .selectAvg(MonthlyIndexDO::getIndexValue14,"index_value14")
                .selectAvg(MonthlyIndexDO::getIndexValue15,"index_value15")
                .selectAvg(MonthlyIndexDO::getIndexValue16,"index_value16")
                .selectAvg(MonthlyIndexDO::getIndexValue17,"index_value17")
                .selectAvg(MonthlyIndexDO::getIndexValue18,"index_value18")
                .selectAvg(MonthlyIndexDO::getIndexValue19,"index_value19")
                .selectAvg(MonthlyIndexDO::getIndexValue20,"index_value20")
                .selectMax(MonthlyIndexDO::getTypeName,"type_name")
                .selectCount(MonthlyIndexDO::getRecordDate,"backup1")
                .betweenIfPresent(MonthlyIndexDO::getRecordDate, reqVO.getRecordDate())
                .eqIfPresent(MonthlyIndexDO::getIndexType, reqVO.getIndexType())
                .likeIfPresent(MonthlyIndexDO::getTypeName, reqVO.getTypeName())
                .betweenIfPresent(MonthlyIndexDO::getStartDate, reqVO.getStartDate())
                .betweenIfPresent(MonthlyIndexDO::getEndDate, reqVO.getEndDate())
                .eqIfPresent(MonthlyIndexDO::getIndexValue1, reqVO.getIndexValue1())
                .eqIfPresent(MonthlyIndexDO::getIndexValue2, reqVO.getIndexValue2())
                .eqIfPresent(MonthlyIndexDO::getIndexValue3, reqVO.getIndexValue3())
                .eqIfPresent(MonthlyIndexDO::getIndexValue4, reqVO.getIndexValue4())
                .eqIfPresent(MonthlyIndexDO::getIndexValue5, reqVO.getIndexValue5())
                .eqIfPresent(MonthlyIndexDO::getIndexValue6, reqVO.getIndexValue6())
                .eqIfPresent(MonthlyIndexDO::getIndexValue7, reqVO.getIndexValue7())
                .eqIfPresent(MonthlyIndexDO::getIndexValue8, reqVO.getIndexValue8())
                .eqIfPresent(MonthlyIndexDO::getIndexValue9, reqVO.getIndexValue9())
                .eqIfPresent(MonthlyIndexDO::getIndexValue10, reqVO.getIndexValue10())
                .eqIfPresent(MonthlyIndexDO::getIndexValue11, reqVO.getIndexValue11())
                .eqIfPresent(MonthlyIndexDO::getIndexValue12, reqVO.getIndexValue12())
                .eqIfPresent(MonthlyIndexDO::getIndexValue13, reqVO.getIndexValue13())
                .eqIfPresent(MonthlyIndexDO::getIndexValue14, reqVO.getIndexValue14())
                .eqIfPresent(MonthlyIndexDO::getIndexValue15, reqVO.getIndexValue15())
                .eqIfPresent(MonthlyIndexDO::getIndexValue16, reqVO.getIndexValue16())
                .eqIfPresent(MonthlyIndexDO::getIndexValue17, reqVO.getIndexValue17())
                .eqIfPresent(MonthlyIndexDO::getIndexValue18, reqVO.getIndexValue18())
                .eqIfPresent(MonthlyIndexDO::getIndexValue19, reqVO.getIndexValue19())
                .eqIfPresent(MonthlyIndexDO::getIndexValue20, reqVO.getIndexValue20())
                .eqIfPresent(MonthlyIndexDO::getBackup1, reqVO.getBackup1())
                .eqIfPresent(MonthlyIndexDO::getBackup2, reqVO.getBackup2())
                .eqIfPresent(MonthlyIndexDO::getBackup3, reqVO.getBackup3())
                .eqIfPresent(MonthlyIndexDO::getBackup4, reqVO.getBackup4())
                .eqIfPresent(MonthlyIndexDO::getBackup5, reqVO.getBackup5())
                .eqIfPresent(MonthlyIndexDO::getBackup6, reqVO.getBackup6())
                .eqIfPresent(MonthlyIndexDO::getBackup7, reqVO.getBackup7())
                .eqIfPresent(MonthlyIndexDO::getBackup8, reqVO.getBackup8())
                .eqIfPresent(MonthlyIndexDO::getBackup9, reqVO.getBackup9())
                .eqIfPresent(MonthlyIndexDO::getBackup10, reqVO.getBackup10())
                .betweenIfPresent(MonthlyIndexDO::getCreateTime, reqVO.getCreateTime())
        );
    }
}
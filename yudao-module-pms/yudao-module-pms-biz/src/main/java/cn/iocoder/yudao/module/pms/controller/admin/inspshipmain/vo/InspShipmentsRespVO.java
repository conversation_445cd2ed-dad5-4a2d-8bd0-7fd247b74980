package cn.iocoder.yudao.module.pms.controller.admin.inspshipmain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;


@Schema(description = "管理后台 - 船运验收入储主档 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InspShipmentsRespVO {
    @Schema(description = "接收日期")
    private String recvDate;

    @Schema(description = "物料号")
    private String matrlNo;

    @Schema(description = "合同号")
    private String contractNo;

    @Schema(description = "车号")
    private String carNo;

    @Schema(description = "重量单号")
    private String wgtListNo;

    @Schema(description = "内部接收部门")
    private String innerRecvDept;

    @Schema(description = "装载数量")
    private BigDecimal loadNum;

    @Schema(description = "过磅数量")
    private BigDecimal scaleNum;

    @Schema(description = "运输数量")
    private BigDecimal transNum;

    @Schema(description = "发运理货号")
    private String issueTallyNo;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "合同号（关联表）")
    private String tContractNo;

    @Schema(description = "船名")
    private String shipName;

    @Schema(description = "船名简拼")
    private String shipnameJp;

}
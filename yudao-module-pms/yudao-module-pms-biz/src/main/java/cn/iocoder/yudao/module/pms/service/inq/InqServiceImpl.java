package cn.iocoder.yudao.module.pms.service.inq;

import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.pms.controller.admin.inq.vo.InqDetailBatchOperateReqVO;
import cn.iocoder.yudao.module.pms.controller.admin.inq.vo.InqPageReqVO;
import cn.iocoder.yudao.module.pms.controller.admin.inq.vo.InqSaveReqVO;
import cn.iocoder.yudao.module.pms.dal.dataobject.demandplan.DemandPlanDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.inq.InqDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.inq.InqDetailDO;
import cn.iocoder.yudao.module.pms.dal.mysql.demandplan.DemandPlanMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.inq.InqDetailMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.inq.InqMapper;
import cn.iocoder.yudao.module.pms.dal.redis.no.PmsNoRedisDAO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.*;
import static cn.iocoder.yudao.module.pms.enums.ErrorCodeConstants.*;

/**
 * 采购案主档管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InqServiceImpl implements InqService {

    @Resource
    private InqMapper inqMapper;
    @Resource
    private InqDetailMapper inqDetailMapper;
    @Resource
    private PmsNoRedisDAO noRedisDAO;
    @Resource
    private DemandPlanMapper demandPlanMapper;

    @Override
    public Long createInq(InqSaveReqVO createReqVO) {
        // 插入
        InqDO inq = BeanUtils.toBean(createReqVO, InqDO.class);
        inq.setProject(getLoginUserTopDeptId().toString());
        inq.setCompId(getLoginUserTenantId().toString());
        inq.setInqId(noRedisDAO.generate(noRedisDAO.INQ_ID_PREFIX));
        inq.setCreateEmpNo(getLoginUserNickname().toString());
        inq.setUpdateEmpNo(getLoginUserNickname().toString());
        inq.setDeptId(getLoginUserDeptId().toString());
        inq.setDeptName(getLoginUserDeptName().toString());
        inqMapper.insert(inq);
        // 返回
        return inq.getId();
    }

    @Override
    public void updateInq(InqSaveReqVO updateReqVO) {
        // 校验存在
        validateInqExists(updateReqVO.getId());
        // 更新
        InqDO updateObj = BeanUtils.toBean(updateReqVO, InqDO.class);
        inqMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteInq(Long id) {
        // 校验存在
        validateInqExists(id);
        // 删除
        inqMapper.deleteById(id);

        // 删除子表
        deleteInqDetailByParentid(id);
    }

    private InqDO validateInqExists(Long id) {
        InqDO inqDO = inqMapper.selectById(id);
        if (inqDO == null) {
            throw exception(INQ_NOT_EXISTS);
        }
        return inqDO;
    }

    @Override
    public InqDO getInq(Long id) {
        return inqMapper.selectById(id);
    }

    @Override
    public PageResult<InqDO> getInqPage(InqPageReqVO pageReqVO) {
        return inqMapper.selectPage(pageReqVO);
    }

    // ==================== 子表（采购案项次档管理） ====================

    @Override
    public PageResult<InqDetailDO> getInqDetailPage(PageParam pageReqVO, Long parentid) {
        return inqDetailMapper.selectPage(pageReqVO, parentid);
    }

    @Override
    public Long createInqDetail(InqDetailDO inqDetail) {
        inqDetailMapper.insert(inqDetail);
        return inqDetail.getId();
    }

    @Override
    public void updateInqDetail(InqDetailDO inqDetail) {
        // 校验存在
        validateInqDetailExists(inqDetail.getId());
        // 更新
        inqDetailMapper.updateById(inqDetail);
    }

    @Override
    public void deleteInqDetail(Long id) {
        // 校验存在
        validateInqDetailExists(id);
        // 删除
        inqDetailMapper.deleteById(id);
    }

    @Override
    public InqDetailDO getInqDetail(Long id) {
        return inqDetailMapper.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchOperate(InqDetailBatchOperateReqVO detailReqVO) {
        List<InqDetailDO> items = detailReqVO.getItems();
        if (CollectionUtil.isEmpty(items)) {
            throw exception(INQ_DETAIL_ITEM_NOT_EXISTS);
        }
        InqDO inqDO = inqMapper.selectById(detailReqVO.getParentid());
        if (!"01".equals(inqDO.getStatus())) {
            throw exception(INQ_ERROR_STATUS);
        }
        // 批量删除无需下面的校验，直接删除即可
        if ("delete".equals(detailReqVO.getType())) {
            for(InqDetailDO temp : items){
                DemandPlanDO demandPlanDO = demandPlanMapper.selectByMrLineId(temp.getMrLineId());
                // 删除时，需求计划申报的状态只能为已创建采购案
                if(!"07".equals(demandPlanDO.getStatus())){
                    throw exception(DEMAND_PLAN_ERROR_STATUS);
                }
                // 将挑选的计划，还原状态为已审核通过
                demandPlanMapper.updateById(new DemandPlanDO().setId(demandPlanDO.getId()).setStatus("06"));
                // 删除采购案项次档
                inqDetailMapper.deleteById(temp.getId());
            }
        }

        // 校验
        List<InqDetailDO> doList = inqDetailMapper.selectByParentId(detailReqVO.getParentid());
        if (CollectionUtil.isNotEmpty(doList)) {
            StringBuffer sbMrLineId = new StringBuffer();
            StringBuffer sbBidId = new StringBuffer();
            for (InqDetailDO item1 : doList) {
                for (InqDetailDO item2 : items) {
                    if (item1.getMrLineId().equals(item2.getMrLineId())) {
                        if (item1.getId() != item2.getId()) {
                            sbMrLineId.append(item1.getMrLineId() + ",");
                        }
                    }
                    if (item1.getBidId().equals(item2.getBidId())) {
                        if (item1.getId() != item2.getId()) {
                            sbBidId.append(item1.getBidId() + ",");
                        }
                    }
                }
            }
            if (sbMrLineId.length() > 0) {
                sbMrLineId.deleteCharAt(sbMrLineId.length() - 1);
                throw new ServiceException(1_011_400_999, "该采购案项次档下，已存在相同计划行号为【" + sbMrLineId + "】的项次档，不允许该操作！");
            }
            if (sbBidId.length() > 0) {
                sbBidId.deleteCharAt(sbBidId.length() - 1);
                throw new ServiceException(1_011_400_999, "该采购案项次档下，已存在相同标段号为【" + sbBidId + "】的项次档，不允许该操作！");
            }
        }
        if ("add".equals(detailReqVO.getType())) {
            Integer maxInqLineId = inqDetailMapper.getMaxInqLineId(detailReqVO.getParentid());
            for(InqDetailDO temp : items){
                DemandPlanDO demandPlanDO = demandPlanMapper.selectByMrLineId(temp.getMrLineId());
                // 新增时，需求计划申报的状态只能为已审核通过
                if(!"06".equals(demandPlanDO.getStatus())){
                    throw exception(DEMAND_PLAN_ERROR_STATUS);
                }
                // 将挑选的计划，修改状态
                demandPlanMapper.updateById(new DemandPlanDO().setId(demandPlanDO.getId()).setStatus("07"));
                // 新增采购案项次档
                temp.setCreateEmpNo(getLoginUserNickname().toString());
                temp.setUpdateEmpNo(getLoginUserNickname().toString());
                temp.setDeptId(inqDO.getDeptId());
                temp.setDeptName(inqDO.getDeptName());
                maxInqLineId = maxInqLineId + 1;
                temp.setInqLineId(String.format("%04d", maxInqLineId));
                inqDetailMapper.insert(temp);
            }
        } else if ("edit".equals(detailReqVO.getType())) {
            for(InqDetailDO temp : items){
                DemandPlanDO demandPlanDO = demandPlanMapper.selectByMrLineId(temp.getMrLineId());
                // 修改时，需求计划申报的状态只能为已创建采购案
                if(!"07".equals(demandPlanDO.getStatus())){
                    throw exception(DEMAND_PLAN_ERROR_STATUS);
                }
                // 修改采购案项次档
                temp.setUpdateEmpNo(getLoginUserNickname().toString());
                temp.setUpdater(getLoginUserId().toString());
                temp.setUpdateTime(LocalDateTime.now());
                inqDetailMapper.updateById(temp);
            }
        }
    }

    @Override
    public void submitInq(Long id) {
        InqDO inqDO = validateInqExists(id);
        // 提交时，状态只能为草稿
        if(!inqDO.getStatus().equals("01")){
            throw exception(INQ_ERROR_STATUS);
        }
        // 更新状态为已委托
        inqMapper.updateById(new InqDO().setId(inqDO.getId()).setStatus("02"));
    }

    @Override
    public void cancelSubmitInq(Long id) {
        InqDO inqDO = validateInqExists(id);
        // 撤销时，状态只能为已委托
        if(!inqDO.getStatus().equals("02")){
            throw exception(INQ_ERROR_STATUS);
        }
        // 更新状态为草稿
        inqMapper.updateById(new InqDO().setId(inqDO.getId()).setStatus("01"));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitPonoInq(Long id) {
        InqDO inqDO = validateInqExists(id);
        // 转已签合同时，状态只能为已委托
        if(!inqDO.getStatus().equals("02")){
            throw exception(INQ_ERROR_STATUS);
        }
        // 更新状态为已签合同
        inqMapper.updateById(new InqDO().setId(inqDO.getId()).setStatus("04"));

        List<InqDetailDO> doList = inqDetailMapper.selectByParentId(inqDO.getId());
        if(CollectionUtil.isEmpty(doList)){
            throw exception(INQ_DETAIL_ITEM_NOT_EXISTS);
        }
        for(InqDetailDO temp : doList){
            DemandPlanDO demandPlanDO = demandPlanMapper.selectByMrLineId(temp.getMrLineId());
            // 计划状态必须为 已创建采购案
            if(!"07".equals(demandPlanDO.getStatus())){
                throw exception(DEMAND_PLAN_ERROR_STATUS);
            }
            // 计划状态更新为已签合同
            demandPlanMapper.updateById(new DemandPlanDO().setId(demandPlanDO.getId()).setStatus("08"));
        }
    }

    @Override
    public void invalidateInq(Long id) {
        InqDO inqDO = validateInqExists(id);
        // 作废时，状态只能为已签合同
        if(!inqDO.getStatus().equals("04")){
            throw exception(INQ_ERROR_STATUS);
        }
        // 更新状态为已中止
        inqMapper.updateById(new InqDO().setId(inqDO.getId()).setStatus("05"));

        List<InqDetailDO> doList = inqDetailMapper.selectByParentId(inqDO.getId());
        if(CollectionUtil.isEmpty(doList)){
            throw exception(INQ_DETAIL_ITEM_NOT_EXISTS);
        }
        for(InqDetailDO temp : doList){
            DemandPlanDO demandPlanDO = demandPlanMapper.selectByMrLineId(temp.getMrLineId());
            // 计划状态必须为 已签合同
            if(!"08".equals(demandPlanDO.getStatus())){
                throw exception(DEMAND_PLAN_ERROR_STATUS);
            }
            // 计划状态更新为 已中止
            demandPlanMapper.updateById(new DemandPlanDO().setId(demandPlanDO.getId()).setStatus("09"));
        }
    }

    @Override
    public PageResult<InqDetailDO> getInqDetailPage2(PageParam pageReqVO, String purno) {
        return inqDetailMapper.selectPage2(pageReqVO, purno);
    }

    private void validateInqDetailExists(Long id) {
        if (inqDetailMapper.selectById(id) == null) {
            throw exception(INQ_DETAIL_NOT_EXISTS);
        }
    }

    private void deleteInqDetailByParentid(Long parentid) {
        inqDetailMapper.deleteByParentid(parentid);
    }

}
package cn.iocoder.yudao.module.pms.controller.admin.wmstrademain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 物料交易申请主档分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WmsTradeMainPageReqVO extends PageParam {

    @Schema(description = "项目号")
    private String project;

    @Schema(description = "公司别")
    private String compId;

    @Schema(description = "申请单号")
    private String issueTallyNo;

    @Schema(description = "交易种类")
    private String issueType;

    @Schema(description = "用途别")
    private String purposeId;

    @Schema(description = "品别")
    private String inventoryType;

    @Schema(description = "计划交易日")
    private String[] planTranDate;

    @Schema(description = "申请人职工编号")
    private String issueTranEmpNo;

    @Schema(description = "申请部门")
    private String issueTranDeptNo;

    @Schema(description = "申请日期")
    private String[] issueDate;

    @Schema(description = "申请人联系电话")
    private String issuePhone;

    @Schema(description = "状况码")
    private String[] stus;

    @Schema(description = "结案人")
    private String finalEmpno;

    @Schema(description = "结案日期")
    private String[] finalDate;

    @Schema(description = "结案时间")
    private String[] finalTime;

    @Schema(description = "验收单")
    private String tranTallyNo;

    @Schema(description = "计划单号")
    private String planTallyNo;

    @Schema(description = "原申请单号")
    private String oldIssueTallyNo;

    @Schema(description = "领料单号")
    private String showTallyNo;

    @Schema(description = "关联交易公司别")
    private String affilCompId;

    @Schema(description = "关联交易单号")
    private String affilIssueTallyNo;

    @Schema(description = "关联交易领料单号")
    private String affilShowTallyNo;

    @Schema(description = "红冲单号")
    private String apprvId;

    @Schema(description = "系统别")
    private String systemId;

    @Schema(description = "程式代码")
    private String appId;

    @Schema(description = "供应商代码")
    private String supplierNo;

    @Schema(description = "合同编号")
    private String contractNo;

    @Schema(description = "成本中心")
    private String costCenter;

    @Schema(description = "工程项目编号")
    private String engineerNo;

    @Schema(description = "工程预算编号")
    private String hitBudgetNo;

    @Schema(description = "工程合同编号")
    private String hitContractNo;

    @Schema(description = "设备编号")
    private String equipmentsNo;

    @Schema(description = "固定资产编号")
    private String fixedAssetsNo;

    @Schema(description = "工单编号")
    private String workOrder;

    @Schema(description = "参数1Id")
    private String refIdA;

    @Schema(description = "备用栏位A")
    private String refNoA;

    @Schema(description = "参数2Id")
    private String refIdB;

    @Schema(description = "备用栏位B")
    private String refNoB;

    @Schema(description = "参数3Id")
    private String refIdC;

    @Schema(description = "备用栏位C")
    private String refNoC;

    @Schema(description = "参数4Id")
    private String refIdD;

    @Schema(description = "备用栏位D")
    private String refNoD;

    @Schema(description = "是否配送")
    private String isDeliver;

    @Schema(description = "配送地点")
    private String deliverPlace;

    @Schema(description = "发料仓库")
    private String storeHouse;

    @Schema(description = "传票号码")
    private String vchrNo;

    @Schema(description = "传票日期")
    private String[] vchrDate;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "资料建立人员")
    private String createEmpNo;

    @Schema(description = "资料建立日期")
    private String[] createDate;

    @Schema(description = "资料异动人员")
    private String updateEmpNo;

    @Schema(description = "资料异动日期")
    private String[] updateDate;

    @Schema(description = "资料建立时间")
    private String[] createTime;

    @Schema(description = "创建人名称")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "流程编号")
    private String processId;

}

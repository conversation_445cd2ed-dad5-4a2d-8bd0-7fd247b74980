package cn.iocoder.yudao.module.pms.service.stock;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.yudao.module.pms.controller.admin.moncumular.vo.WmsMonCumularRespVO;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.pms.controller.admin.stock.vo.WmsStockPageReqVO;
import cn.iocoder.yudao.module.pms.controller.admin.stock.vo.WmsStockRespVO;
import cn.iocoder.yudao.module.pms.controller.admin.stock.vo.WmsStockSaveReqVO;
import cn.iocoder.yudao.module.pms.dal.dataobject.stock.WmsStockDO;
import cn.iocoder.yudao.module.pms.dal.mysql.stock.WmsStockMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import java.util.HashMap;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.pms.enums.ErrorCodeConstants.WMS_STOCK_NOT_EXISTS;

/**
 * 料号实时库存 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WmsStockServiceImpl implements WmsStockService {

    @Resource
    private WmsStockMapper wmsStockMapper;

    @Override
    public Long createWmsStock(WmsStockSaveReqVO createReqVO) {
        // 插入
        WmsStockDO wmsStock = BeanUtils.toBean(createReqVO, WmsStockDO.class);
        wmsStockMapper.insert(wmsStock);
        // 返回
        return wmsStock.getId();
    }

    @Override
    public void updateWmsStock(WmsStockSaveReqVO updateReqVO) {
        // 校验存在
        validateWmsStockExists(updateReqVO.getId());
        // 更新
        WmsStockDO updateObj = BeanUtils.toBean(updateReqVO, WmsStockDO.class);
        wmsStockMapper.updateById(updateObj);
    }

    @Override
    public void deleteWmsStock(Long id) {
        // 校验存在
        validateWmsStockExists(id);
        // 删除
        wmsStockMapper.deleteById(id);
    }

    private void validateWmsStockExists(Long id) {
        if (wmsStockMapper.selectById(id) == null) {
            throw exception(WMS_STOCK_NOT_EXISTS);
        }
    }

    @Override
    public WmsStockDO getWmsStock(Long id) {
        return wmsStockMapper.selectById(id);
    }

    @Override
    public PageResult<WmsStockRespVO> getWmsStockPage(WmsStockPageReqVO pageReqVO) {
        IPage<WmsStockRespVO> pageResult = wmsStockMapper.selectPageStock(MyBatisUtils.buildPage(pageReqVO),
                pageReqVO.getProject(),
                pageReqVO.getCompid(),
                pageReqVO.getMatrlno(),
                pageReqVO.getMatrlname(),
                pageReqVO.getLotno(),
                pageReqVO.getIsmy(),
                pageReqVO.getInventorytype()
        );
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }

    @Override
    public WmsStockDO selectStockByKz(HashMap map) {
        return wmsStockMapper.selectStockByKz(map);
    }

    @Override
    /** 通过料号和(合同号)获取库存数据 */
    public WmsStockDO getWmsStockByMatrlnoAndPono(String matrlno, String lotno) {
        return wmsStockMapper.selectOne(new LambdaQueryWrapperX<WmsStockDO>().eq(WmsStockDO::getMatrlno, matrlno)
            .eq(WmsStockDO::getLotno, lotno).eq(WmsStockDO::getDeleted, false), false);
    }

    @Override
    public WmsStockDO getEndQtySumByMatrlno(String matrlno) {
        return wmsStockMapper.getEndQtySumByMatrlno(matrlno);
    }

}

package cn.iocoder.yudao.module.pms.controller.admin.inspectionbatch.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 验收批次档 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InspectionBatchRespVO {

    @Schema(description = "ID", example = "9416")
    @ExcelProperty("ID")
    private Long id;

    @Schema(description = "项目")
    @ExcelProperty("项目")
    private String project;

    @Schema(description = "公司别", example = "7747")
    @ExcelProperty("公司别")
    private String compid;

    @Schema(description = "合同号")
    @ExcelProperty("合同号")
    private String contractno;
    
    @Schema(description = "检验批号")
    @ExcelProperty("检验批号")
    private String chkno;

    @Schema(description = "手工批号")
    @ExcelProperty("手工批号")
    private String manualchkno;

    @Schema(description = "检验类别", example = "2")
    @ExcelProperty("检验类别")
    private String chktype;

    @Schema(description = "类别", example = "2")
    @ExcelProperty("类别")
    private String type;

    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "供应商代码")
    @ExcelProperty("供应商代码")
    private String supplierno;

    @Schema(description = "供应商", example = "张三")
    @ExcelProperty("供应商")
    private String supplyname;

    @Schema(description = "检验人")
    @ExcelProperty("检验人")
    private String tester;

    @Schema(description = "主检人")
    @ExcelProperty("主检人")
    private String mjtester;

    @Schema(description = "发站")
    @ExcelProperty("发站")
    private String sendstation;

    @Schema(description = "发站说明")
    @ExcelProperty("发站说明")
    private String sendstationdesc;

    @Schema(description = "原料料号")
    @ExcelProperty("原料料号")
    private String matrlno;

    @Schema(description = "是否已品质验收")
    @ExcelProperty("是否已品质验收")
    private String isqtyacpt;

    @Schema(description = "是否已重量验收")
    @ExcelProperty("是否已重量验收")
    private String ismasacpt;

    @Schema(description = "品质检验结果")
    @ExcelProperty("品质检验结果")
    private String qtyover;

    @Schema(description = "化验室")
    @ExcelProperty("化验室")
    private String testroom;

    @Schema(description = "检验日期")
    @ExcelProperty("检验日期")
    private String testdate;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
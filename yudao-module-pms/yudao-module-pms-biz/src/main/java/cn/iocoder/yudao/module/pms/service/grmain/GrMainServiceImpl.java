package cn.iocoder.yudao.module.pms.service.grmain;

import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.yudao.framework.common.util.collection.SetUtils;
import cn.iocoder.yudao.framework.common.util.date.DateUtil;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.pms.api.pomain.dto.GetEmplByPostReqDTO;
import cn.iocoder.yudao.module.pms.api.pomain.dto.PoMainCheckSaveReqDTO;
import cn.iocoder.yudao.module.pms.api.pomain.dto.PoMainInspSaveReqDTO;
import cn.iocoder.yudao.module.pms.api.pomain.dto.PoMainReqDTO;
import cn.iocoder.yudao.module.pms.controller.admin.stock.vo.WmsStockPageReqVO;
import cn.iocoder.yudao.module.pms.controller.admin.wmstrade.vo.WmsTradeParamReqVO;
import cn.iocoder.yudao.module.pms.controller.app.grmain.vo.AppGrDetailCheckSaveVO;
import cn.iocoder.yudao.module.pms.controller.app.grmain.vo.AppGrDetailFileRelationVO;
import cn.iocoder.yudao.module.pms.dal.dataobject.domain.DoDetailDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.domain.DoMainDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.epdetail.EpDetailDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.estimate.EstimateDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.grmain.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.other.StorageDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.pomain.PoDetailDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.pomain.PoMainDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.pomainv.PoDetailVDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.pomainv.PoMainVDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.stock.WmsStockDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.wmstrade.WmsTradeDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.wrstradedetail2.WrsTradeDetail2DO;
import cn.iocoder.yudao.module.pms.dal.mysql.domain.DoDetailMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.domain.DoMainMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.epdetail.EpDetailMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.grmain.*;
import cn.iocoder.yudao.module.pms.dal.mysql.other.StorageMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.pomain.PoDetailMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.pomain.PoMainMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.pomainv.PoDetailVMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.pomainv.PoMainVMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.stock.WmsStockMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.wmstrade.WmsTradeMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.wrstradedetail2.WrsTradeDetail2Mapper;
import cn.iocoder.yudao.module.pms.dal.redis.no.PmsNoRedisDAO;
import cn.iocoder.yudao.module.pms.service.wmstrade.WmsTradeServiceImpl;
import cn.iocoder.yudao.module.pms.service.wrstradedetail2.WrsTradeDetail2ServiceImpl;
import cn.iocoder.yudao.module.system.api.dept.DeptApi;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fhs.common.utils.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;
import cn.iocoder.yudao.module.pms.controller.admin.grmain.vo.*;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertSet;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.*;
import static cn.iocoder.yudao.module.pms.enums.ErrorCodeConstants.*;

import java.text.ParseException;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 物料验收 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class GrMainServiceImpl implements GrMainService {

    @Resource
    private GrMainMapper grMainMapper;
    @Resource
    private GrDetailMapper grDetailMapper;
    @Resource
    private PpDetail1Mapper ppDetail1Mapper;
    @Resource
    private PpDetail2Mapper ppDetail2Mapper;
    @Resource
    private PpDetail3Mapper ppDetail3Mapper;
    @Resource
    private DeptApi deptApi;
    @Resource
    private WmsTradeServiceImpl wmsTradeService;
    @Resource
    private PoMainMapper poMainMapper;
    @Resource
    private PoDetailMapper poDetailMapper;
    @Resource
    private PmsNoRedisDAO noRedisDAO;
    @Resource
    private AdminUserApi adminUserApi;
    @Resource
    private WrsTradeDetail2Mapper wrsTradeDetail2Mapper;
    @Resource
    private WrsTradeDetail2ServiceImpl wrsTradeDetail2Service;
    @Resource
    private PoMainVMapper poMainVMapper;
    @Resource
    private PoDetailVMapper poDetailVMapper;
    @Resource
    private WmsTradeMapper wmsTradeMapper;
    @Resource
    private WmsStockMapper wmsStockMapper;
    @Resource
    private GrDetailFileRelationMapper grDetailFileRelationMapper;
    @Resource
    private StorageMapper storageMapper;
    @Resource
    private DoMainMapper doMainMapper;
    @Resource
    private DoDetailMapper doDetailMapper;
    @Resource
    private EpDetailMapper epDetailMapper;
    @Resource
    private SettlementDetailMapper settlementDetailMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createGrMain(GrMainSaveReqVO createReqVO) {
        String appid = createReqVO.getAppid();
        String businessNo = null;

        GrMainDO grMain = BeanUtils.toBean(createReqVO, GrMainDO.class);
        grMain.setStus("N");
        if ("grmain".equals(appid)) {
            businessNo = noRedisDAO.generate(PmsNoRedisDAO.GR_MAIN_NO_PREFIX);
            grMain.setRecvDate(DateUtil.getDate());
            grMain.setRecvEmpl(getLoginUserNickname());
            grMain.setRecvEmplNo(getLoginUserId().toString());
        } else if ("ppmain".equals(appid)) {
            businessNo = noRedisDAO.generate(PmsNoRedisDAO.PP_MAIN_NO_PREFIX);
        } else if ("grmainm".equals(appid)) {
            businessNo = noRedisDAO.generate(PmsNoRedisDAO.PP_MAIN_NO_PREFIX);
        } else if ("grmainy".equals(appid)) {
            businessNo = noRedisDAO.generate(PmsNoRedisDAO.GR_MAIN_Y_NO_PREFIX);
        } else if ("xiaochengxu".equals(appid)) {
            grMain.setStus("A");
            businessNo = noRedisDAO.generate(PmsNoRedisDAO.GR_MAIN_NO_PREFIX);
        }
        if (grMainMapper.selectByInspno(businessNo) != null) {
            if ("grmain".equals(appid)) {
                throw exception(GR_NO_EXISTS);
            } else {
                throw exception(PP_MAIN_NO_EXISTS);
            }
        }
        grMain.setInspno(businessNo);
        grMain.setProject(getLoginUserTopDeptId().toString());
        grMain.setCompid(getLoginUserTenantId().toString());
        grMain.setCreateEmpno(getLoginUserNickname());
        grMain.setUpdateEmpno(getLoginUserNickname());
        grMain.setCreator(String.valueOf(getLoginUserId()));
        grMain.setUpdater(String.valueOf(getLoginUserId()));
        grMain.setCreateDate(DateUtil.getDate());
        grMain.setUpdateDate(DateUtil.getDate());
        grMainMapper.insert(grMain);

        if ("grmainm".equals(appid)) {
            String orginspno = grMain.getOrginspno();
            if (StringUtils.isBlank(orginspno)) {
                throw exception(GR_NO_IS_EMPTY);
            }
            List<GrDetailDO> list = grDetailMapper.selectList(GrDetailDO::getInspno, orginspno);
            for (GrDetailDO grDetailDO : list) {
                GrDetailDO newGrDetailDO = new GrDetailDO();
                newGrDetailDO.setProject(getLoginUserTopDeptId().toString());
                newGrDetailDO.setCompid(getLoginUserTenantId().toString());
                newGrDetailDO.setParentId(grMain.getId());
                newGrDetailDO.setInspno(businessNo);
                newGrDetailDO.setInspitemno(grDetailDO.getInspitemno());
                newGrDetailDO.setPono(grDetailDO.getPono());
                newGrDetailDO.setPoitemno(grDetailDO.getPoitemno());
                newGrDetailDO.setMatrlno(grDetailDO.getMatrlno());
                newGrDetailDO.setChnname(grDetailDO.getChnname());
                newGrDetailDO.setCreateEmpno(getLoginUserNickname());
                newGrDetailDO.setUpdateEmpno(getLoginUserNickname());
                newGrDetailDO.setCreator(String.valueOf(getLoginUserId()));
                newGrDetailDO.setUpdater(String.valueOf(getLoginUserId()));
                newGrDetailDO.setCreateDate(DateUtil.getDate());
                newGrDetailDO.setUpdateDate(DateUtil.getDate());
                grDetailMapper.insert(newGrDetailDO);
            }
        }
        return grMain.getId();
    }

    @Override
    public void updateGrMain(GrMainSaveReqVO updateReqVO) {
        SimpleDateFormat originalFormat = new SimpleDateFormat(DateUtil.FORMAT_YYYY_MM_DD);
        SimpleDateFormat targetFormat = new SimpleDateFormat(DateUtil.FORMAT_YYYYMMDD);
        // 校验存在
        validateGrMainExists(updateReqVO.getId());
        PoMainDO poMainDO = poMainMapper.selectByPono(updateReqVO.getPono());
        if (poMainDO != null && ("grmmain".equals(updateReqVO.getAppid()) || "xiaochengxu".equals(updateReqVO.getAppid()))) {
            try {
                String inspDateStr = updateReqVO.getInspDate();
                String startDateStr = poMainDO.getStartDate();
                String endDateStr = poMainDO.getEndDate();

                String inspDateFormatted = targetFormat.format(originalFormat.parse(inspDateStr));
                String startDateFormatted = targetFormat.format(originalFormat.parse(startDateStr));
                String endDateFormatted = targetFormat.format(originalFormat.parse(endDateStr));

                Integer startDate = Integer.valueOf(startDateFormatted);
                Integer endDate = Integer.valueOf(endDateFormatted);
                Integer inspDate = Integer.valueOf(inspDateFormatted);

                if (inspDate < startDate || inspDate > endDate) {
                    throw exception(INSPDATE_CHECK_EFFECTTIVE);
                }
            } catch (ParseException e) {
                // 处理日期解析异常
                throw new RuntimeException("日期解析异常", e);
            }
        }
        // 更新
        GrMainDO updateObj = BeanUtils.toBean(updateReqVO, GrMainDO.class);
        updateObj.setUpdateEmpno(getLoginUserNickname());
        updateObj.setUpdater(String.valueOf(getLoginUserId()));
        updateObj.setUpdateDate(DateUtil.getDate());
        grMainMapper.updateById(updateObj);
    }

    @Override
    public void updateChecker(PoMainCheckSaveReqDTO reqDTO) {
        TenantContextHolder.setTenantId(Long.valueOf(reqDTO.getTenantId()));
        GrMainDO grMainDO = grMainMapper.selectByInspno(reqDTO.getInspno());
        // 校验存在
        if (grMainDO == null) {
            throw exception(GR_NO_NOT_EXISTS);
        }
        // 校验是否已确认
        if ("Y".equals(grMainDO.getStus())) {
            throw exception(GR_MIAN_CHECK_BEFORM_CONFIRM);
        }
        grMainDO.setCheckEmpl(reqDTO.getChecker());
        grMainMapper.updateById(grMainDO);
    }

    @Override
    public void updateInspEmpl(PoMainInspSaveReqDTO reqDTO) {
        TenantContextHolder.setTenantId(Long.valueOf(reqDTO.getTenantId()));
        GrMainDO grMainDO = grMainMapper.selectByInspno(reqDTO.getInspno());
        // 校验存在
        if (grMainDO == null) {
            throw exception(GR_NO_NOT_EXISTS);
        }
        // 校验是否已确认
        if ("Y".equals(grMainDO.getStus())) {
            throw exception(GR_MIAN_INSP_BEFORM_CONFIRM);
        }
        grMainDO.setInspEmpl(reqDTO.getInspEmpl());
        grMainMapper.updateById(grMainDO);
    }

    @Override
    public List<GrMainDO> getGrMainByVendor(PoMainReqDTO reqDTO) {
        List<GrMainDO> grLists = new ArrayList<>();
        TenantContextHolder.setTenantId(Long.valueOf(reqDTO.getTenantId()));
        List<PoMainDO> mainDOS = poMainMapper.selectListByVendor(reqDTO.getVendorno());
        if (mainDOS.size() > 0) {
            for (PoMainDO mainDO : mainDOS) {
                List<GrMainDO> grList = grMainMapper.selectListByPono(mainDO.getPono());
                if (grList.size() > 0) {
                    grLists.addAll(grList);
                }
            }
        }
        return grLists;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteGrMain(Long id) {
        // 校验存在
        validateGrMainExists(id);
        // 删除
        grMainMapper.deleteById(id);

        // 删除子表
        deleteGrDetailByParentId(id);
    }

    private void validateGrMainExists(Long id) {
        if (grMainMapper.selectById(id) == null) {
            throw exception(GR_MAIN_NOT_EXISTS);
        }
    }

    @Override
    public GrMainDO selectByInspno(String inspno) {
        return grMainMapper.selectByInspno(inspno);
    }

    @Override
    public GrMainDO getGrMain(Long id) {
        return grMainMapper.selectById(id);
    }

    @Override
    public GrMainRespVO getGrMain2(Long id) {
        return grMainMapper.selectGrMainById(id);
    }

    @Override
    public PageResult<GrMainRespVO> getGrMainPage(GrMainPageReqVO pageReqVO) {
        String[] appid = {"grmain", "xiaochengxu"};
        pageReqVO.setAppid(appid);
        IPage<GrMainRespVO> pageResult = grMainMapper.selectGrMainMPage(MyBatisUtils.buildPage(pageReqVO), pageReqVO);
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
        //return grMainMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<GrMainRespVO> getAppPage(GrMainPageReqVO pageReqVO) {
        String[] appid = {"xiaochengxu"};
        pageReqVO.setAppid(appid);
        IPage<GrMainRespVO> pageResult = grMainMapper.selectGrMainMPage(MyBatisUtils.buildPage(pageReqVO), pageReqVO);
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
        //return grMainMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<GrMainRespVO> getGrMainMPage(GrMainPageReqVO pageReqVO) {
        IPage<GrMainRespVO> pageResult = grMainMapper.selectGrMainMPage(MyBatisUtils.buildPage(pageReqVO), pageReqVO);
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String batchSplitCase(GrDetailSplitSaveReqVO reqVO) {
        StringBuffer message = new StringBuffer("");
        Long parentId = reqVO.getParentId();
        List<GrDetailDO> items = reqVO.getItems();
        // 1.校验主档状态 只有审核通过时 可以拆分
        GrMainDO grMainDO = grMainMapper.selectById(parentId);
        if (!"Z".equals(grMainDO.getStus()) && !"Y".equals(grMainDO.getStus()) && !"K".equals(grMainDO.getStus())) {
            throw exception(GR_MAIN_CAN_NOT_SPLIT_CASE);
        }

        // 2.生成交易申请主档
        GrMainDO newGrMainDO = BeanUtils.toBean(grMainDO, GrMainDO.class);
        // 生成交易申请单号 校验唯一
        String businessNo = noRedisDAO.generate(PmsNoRedisDAO.GR_MAIN_NO_PREFIX);
        if (grMainMapper.selectByInspno(businessNo) != null) {
            throw exception(GR_NO_EXISTS);
        }
        newGrMainDO.setId(null);
        newGrMainDO.setProject(getLoginUserTopDeptId().toString());
        newGrMainDO.setCompid(getLoginUserTenantId().toString());
        newGrMainDO.setInspno(businessNo);
        newGrMainDO.setOrginspno(grMainDO.getInspno());
        newGrMainDO.setMemo("本单为分拆过的单子，原始单号为：" + grMainDO.getInspno());
        newGrMainDO.setCreateEmpno(String.valueOf(getLoginUserId()));
        newGrMainDO.setCreator(getLoginUserNickname());
        newGrMainDO.setCreateDate(DateUtil.getDate());
        newGrMainDO.setUpdater(getLoginUserNickname());
        newGrMainDO.setUpdateEmpno(String.valueOf(getLoginUserId()));
        newGrMainDO.setUpdateDate(DateUtil.getDate());
        grMainMapper.insert(newGrMainDO);

        Long newParentId = newGrMainDO.getId();

        Set<Long> ids = convertSet(items, GrDetailDO::getId);
        // 4.生成交易申请明细
        List<GrDetailDO> grDetailDOS = grDetailMapper.selectListById(ids);
        List<GrDetailDO> newGrDetailDOS = BeanUtils.toBean(grDetailDOS, GrDetailDO.class);
        // 4.1拆分交易申请明细
        newGrDetailDOS.forEach(grDetailDO -> {
            items.forEach(item -> {
                if (grDetailDO.getId().equals(item.getId())) {
                    String inspitemno = "0000";
                    String serialNo = grDetailMapper.getMaxGrItemNoByParentId(newParentId);
                    if (serialNo != null) {
                        inspitemno = String.format("%04d", Integer.valueOf(serialNo) + 1);
                    }
                    // b.插入交易明细
                    grDetailDO.setId(null);
                    grDetailDO.setParentId(newParentId);
                    grDetailDO.setProject(getLoginUserTopDeptId().toString());
                    grDetailDO.setCompid(getLoginUserTenantId().toString());
                    grDetailDO.setInspno(businessNo);
                    grDetailDO.setInspitemno(inspitemno);
                    grDetailDO.setInspqty(item.getInspqty());
                    grDetailDO.setCreateEmpno(String.valueOf(getLoginUserId()));
                    grDetailDO.setCreator(getLoginUserNickname());
                    grDetailDO.setCreateDate(DateUtil.getDate());
                    grDetailDO.setUpdater(getLoginUserNickname());
                    grDetailDO.setUpdateEmpno(String.valueOf(getLoginUserId()));
                    grDetailDO.setUpdateDate(DateUtil.getDate());
                    grDetailMapper.insert(grDetailDO);
                }
            });
        });


        // 4.2 交易数量为0时，删除旧数据
        grDetailDOS.forEach(grDetailDO -> {
            items.forEach(item -> {
                if (grDetailDO.getId().equals(item.getId())) {
                    if (item.getInspqty().compareTo(grDetailDO.getInspqty()) > 0) {
                        message.append("料号：" + grDetailDO.getMatrlno() + "拆分验收数量不能大于原验收数量!");
                    }
                    grDetailDO.setInspqty(grDetailDO.getInspqty().subtract(item.getInspqty()));
                    grDetailMapper.updateById(grDetailDO);
                }
            });
            if (grDetailDO.getInspqty().compareTo(new BigDecimal(0)) == 0) {
                grDetailMapper.deleteById(grDetailDO.getId());
            }
        });
        if (!StringUtil.isEmpty(message.toString())) {
            throw exception(GR_MAIN_SPLIT_ERROR, message);
        }
        return businessNo;
    }

    @Override
    public PageResult<GrMainQueryRespVO> getGrMainPageQuery(GrMainQueryReqVO pageReqVO) {
        IPage<GrMainQueryRespVO> pageQuery = grMainMapper.getGrMainPageQuery(MyBatisUtils.buildPage(pageReqVO), pageReqVO);
        return new PageResult<>(pageQuery.getRecords(), pageQuery.getTotal());
    }

    // ==================== 子表（物料验收明细） ====================

    @Override
    public PageResult<GrDetailDO> getGrDetailPage(PageParam pageReqVO, Long parentId) {
        return grDetailMapper.selectPage(pageReqVO, parentId);
    }


    //验收完成修改主表

    @Override
    public void updateMainStatus(Long parentId) {
        // 查询子表中所有记录的 checkstus 字段
        List<GrDetailDO> detailList = grDetailMapper.selectByParentId(parentId);

        // 检查所有 checkstus 是否都为“完成验收”
        boolean allCompleted = detailList.stream()
                .allMatch(detail -> "1".equals(detail.getCheckstus()));

        GrMainDO mainDO = new GrMainDO();
        mainDO.setId(parentId);
        mainDO.setAuditEmpl(String.valueOf(getLoginUserId()));
        // mainDO.setStus("Y");
        if (allCompleted) {
            mainDO.setIspass("Y");
        } else {
            mainDO.setIspass("N");
        }
        grMainMapper.updateById(mainDO);
    }


    @Override
    public Long createGrDetail(GrDetailDO grDetail) {
        Long parentId = grDetail.getParentId();
        validateGrMainExists(parentId);
        GrMainDO mainDO = this.getGrMain(parentId);
        // 验收单提交审核后，不允许新增！
        if ("grmain".equals(mainDO.getAppid())) {
            String[] stusArr = {"D", "K", "Z", "Y", "R"};
            for (String stus : stusArr) {
                if (stus.equals(mainDO.getStus())) {
                    throw exception(GR_MAIN_AFTER_AUDIT_CAN_NOT_CREATE);
                }
            }
            PoMainDO poMain = poMainMapper.selectByPono(mainDO.getPono());
            BigDecimal unitprice = new BigDecimal(0);
            // 1.查询有效期内 最新的合同
            List<PoMainVDO> mainVList = poMainVMapper.selectEffectListByParentId(poMain.getId(), mainDO.getInspDate());
            if (mainVList == null || mainVList.size() == 0) {
                throw exception(ISSUEDATE_CHECK_EFFECTTIVE);
            }

            PoMainVDO mainVDO = mainVList.get(0);
            if (mainVDO.getTotalQty().compareTo(BigDecimal.ZERO) == 0) {
                throw exception(TEADE_COMMON_ERROR, "项目号【" + grDetail.getProject() + "】公司别【" + grDetail.getCompid() + "】料号【" + grDetail.getMatrlno() + "】批号【" + grDetail.getPono() + "】的合同订购数量不能为0");
            }

            // 2.合同明细版本
            List<PoDetailVDO> poDetailVDOList = poDetailVMapper.selectListByParentId(mainVDO.getId(), grDetail.getMatrlno());
            if (poDetailVDOList == null || poDetailVDOList.size() == 0) {
                throw exception(TEADE_COMMON_ERROR, "没有找到项目号【" + grDetail.getProject() + "】公司别【" + grDetail.getCompid() + "】料号【" + grDetail.getMatrlno() + "】批号【" + grDetail.getPono() + "】的合同信息。");
            } else {
                for (PoDetailVDO poDetailVDO : poDetailVDOList) {
                    if (grDetail.getMatrlno().equals(poDetailVDO.getMatrlno()) && grDetail.getPoitemno().equals(poDetailVDO.getPoitemno())) {
                        unitprice = poDetailVDO.getUnitprice();
                    }
                }
            }
            if (grDetail.getInspamt() == null) {
                grDetail.setInspamt(grDetail.getInspqty().multiply(unitprice));
            }
        }
        grDetail.setProject(mainDO.getProject());
        grDetail.setCompid(mainDO.getCompid());
        grDetail.setInspno(mainDO.getInspno());
//        grDetail.setCheckstus("验收中");
        String serialNo = grDetailMapper.getMaxGrItemNoByParentId(parentId);
        String inspitemno = "0000";
        if (serialNo != null) {
            inspitemno = String.format("%04d", Integer.valueOf(serialNo) + 1);
        }
        grDetail.setInspitemno(inspitemno);
        grDetail.setCreateEmpno(String.valueOf(getLoginUserId()));
        grDetail.setCreator(getLoginUserNickname());
        grDetail.setCreateDate(DateUtil.getDate());
        grDetail.setUpdater(getLoginUserNickname());
        grDetail.setUpdateEmpno(String.valueOf(getLoginUserId()));
        grDetail.setUpdateDate(DateUtil.getDate());
        grDetailMapper.insert(grDetail);
        return grDetail.getId();
    }

    @Override
    public void updateGrDetail(GrDetailDO grDetail) {
        // 校验存在
        validateGrDetailExists(grDetail.getId());
        Long parentId = grDetail.getParentId();
        validateGrMainExists(parentId);
        GrMainDO mainDO = this.getGrMain(parentId);
        String[] stusArr = {"D", "R"};
        for (String stus : stusArr) {
            if (stus.equals(mainDO.getStus())) {
                throw exception(GR_MAIN_AFTER_AUDIT_CAN_NOT_CREATE);
            }
        }

        if ("Y".equals(mainDO.getStus()) && StringUtils.isBlank(grDetail.getLocno())) {
            throw exception(GR_DETAIL_LOCNO_CAN_NOT_EMPTY);
        } else if ("Y".equals(mainDO.getStus()) && StringUtils.isNotBlank(grDetail.getLocno())) {
            if (storageMapper.selectOne(StorageDO::getLayerid, grDetail.getLocno(), StorageDO::getLayernum, "4") == null) {
                throw exception(WMS_TRADE_LOCNO_NOT_EXISTS);
            }
        }
        grDetail.setUpdateEmpno(getLoginUserNickname());
        grDetail.setUpdater(String.valueOf(getLoginUserId()));
        grDetail.setUpdateDate(DateUtil.getDate());
        // 更新
        grDetailMapper.updateById(grDetail);

        // 更新后检查并更新主表状态
        updateMainStatus(grDetail.getParentId());
    }


    @Override
    public void deleteGrDetail(Long id) {
        // 校验存在
        validateGrDetailExists(id);
        // 删除
        grDetailMapper.deleteById(id);
    }

    @Override
    public GrDetailRespVO getGrDetail(Long id) {
        return grDetailMapper.getGrDetailDOById(id);
    }

    @Override
    @Transactional
    public void cancelConfirmGrMain(Long id) {
        GrMainDO grMainDO = getGrMain(id);
        if ("grmain".equals(grMainDO.getAppid())) {
            grMainDO.setStus("C");
            List<GrDetailDO> grDetailDOS = grDetailMapper.selectListByParentId(id);
            // 1.1 校验验收单金额、数量
            checkQtyAndAmt(grMainDO);
            for (GrDetailDO grDetailDO : grDetailDOS) {
                int i = 1;
                WmsTradeParamReqVO reqVO = new WmsTradeParamReqVO();
                reqVO.setProject(grMainDO.getProject())
                        .setCompid(grMainDO.getCompid())
                        .setPono(grMainDO.getPono())
                        .setInspno(grMainDO.getInspno())
                        .setBatch(grDetailDO.getPoitemno())
                        .setMatrlno(grDetailDO.getMatrlno())
                        .setLocno(grDetailDO.getLocno())
                        .setStogcode(grDetailDO.getStogcode())
                        .setQty(grDetailDO.getInspqty().negate())
                        .setAmt(grDetailDO.getInspamt().negate())
                        .setVchrdate(DateUtil.getDate())
                        .setSeqno(String.valueOf(i))
                        .setIsConfirm("N")
                        .setSystemid("WMS").setPurposeid("10A");
                wmsTradeService.updateStock(reqVO);
                i++;
            }
        }
        if ("ppmain".equals(grMainDO.getAppid()) || "myppmain".equals(grMainDO.getAppid())) {
            grMainDO.setStus("N");
            List<PpDetail3DO> detailDOS = ppDetail3Mapper.selectList(PpDetail3DO::getParentId, id);
            List<PpDetail1DO> detail1DOList = ppDetail1Mapper.selectList(PpDetail1DO::getParentId, id);
            if (detail1DOList == null || detail1DOList.size() == 0) {
                throw exception(PP_DETAIL1_NOT_EXISTS);
            }

            BigDecimal totalAmtExTax = detailDOS.get(0).getTotalAmtExTax();
            String issueTallyNo = detail1DOList.get(0).getIssueTallyNo();
            WmsTradeDO wmsTradeDO = wmsTradeMapper.selectByIssuetallyno(issueTallyNo);
            BigDecimal cha = totalAmtExTax.subtract(wmsTradeDO.getTransAmt());
            if (cha.compareTo(BigDecimal.ZERO) != 0) {
                WmsTradeParamReqVO reqVO = new WmsTradeParamReqVO();
                reqVO.setProject(grMainDO.getProject())
                        .setCompid(grMainDO.getCompid())
                        .setPono(grMainDO.getPono())
                        .setInspno(grMainDO.getInspno())
                        .setMatrlno(wmsTradeDO.getMatrlNo())
                        .setLocno(wmsTradeDO.getStgNo())
                        .setQty(new BigDecimal(0))
                        .setAmt(cha.negate())
                        .setVchrdate(DateUtil.getDate())
                        .setSeqno(String.valueOf(1))
                        .setSubmitFlag("N")
                        .setSystemid("WRS").setPurposeid("12A");
                if ("myppmain".equals(grMainDO.getAppid())) {
                    reqVO.setIsMy("Y");
                }
                wmsTradeService.updateStock(reqVO);
            }
            grMainDO.setTotalAmt(detailDOS.get(0).getTotalAmt());
            grMainDO.setTaxamt(detailDOS.get(0).getSettleGoodAmt());
        }
        if ("grmainy".equals(grMainDO.getAppid())) {
            grMainDO.setStus("N");
            List<GrDetailDO> detailDOS = grDetailMapper.selectList(GrDetailDO::getParentId, id);
            if (detailDOS == null || detailDOS.size() == 0) {
                throw exception(PP_DETAIL1_NOT_EXISTS);
            }
            List<SettlementDetailDO> settlementDetailList = settlementDetailMapper.selectList(SettlementDetailDO::getParentId, id);

            List<String> issueTallyNoList = new ArrayList<>();
            if (settlementDetailList != null && settlementDetailList.size() > 0) {
                for (SettlementDetailDO settlementDetailDO : settlementDetailList) {
                    issueTallyNoList.add(settlementDetailDO.getIssueTallyNo());
                }
            }

            if (issueTallyNoList != null && issueTallyNoList.size() > 0) {
                for (String issueTallyNo : issueTallyNoList) {
                    WmsTradeDO wmsTradeC = wmsTradeMapper.selectByIssuetallyno(issueTallyNo);
                    String newIssuetallyno = issueTallyNo + "C";
                    WmsTradeDO wmsTradeCC = wmsTradeMapper.selectByIssuetallyno(newIssuetallyno);
                    Boolean isAdd = true;
                    if (wmsTradeCC != null && "Y".equals(wmsTradeCC.getStus())) {
                        isAdd = false;
                    }

                    BigDecimal totalAmt = BigDecimal.ZERO;
                    for (SettlementDetailDO settlementDetailDO : settlementDetailList) {
                        if (issueTallyNo.equals(settlementDetailDO.getIssueTallyNo())) {
                            totalAmt = totalAmt.add(settlementDetailDO.getActualFee().subtract(wmsTradeC.getTransAmt()));
                        }
                    }
                    if (wmsTradeCC != null) {
                        wmsTradeCC.setContractNo(wmsTradeC.getContractNo());
                        wmsTradeCC.setTranTallyNo(wmsTradeC.getTranTallyNo());
                        wmsTradeCC.setSupplierNo(wmsTradeC.getSupplierNo());
                        wmsTradeCC.setMatrlNo(wmsTradeC.getMatrlNo());
                        wmsTradeCC.setStgNo(wmsTradeC.getStgNo());
                        wmsTradeCC.setLotNo(wmsTradeC.getContractNo());
                        wmsTradeCC.setStus("N");
                        wmsTradeCC.setVchrDate(wmsTradeC.getVchrDate());
                        wmsTradeCC.setUpdateEmpNo(getLoginUserNickname());
                        wmsTradeCC.setUpdater(getLoginUserId().toString());
                        wmsTradeCC.setUpdateDate(DateUtil.getDate());
                        wmsTradeCC.setFinalDate(LocalDate.now());
                        wmsTradeCC.setFinalTime(LocalTime.now());
                        wmsTradeCC.setFinalEmpNo(getLoginUserNickname());
                        wmsTradeMapper.updateById(wmsTradeCC);
                    }
                    WmsTradeParamReqVO req = new WmsTradeParamReqVO();
                    req.setIssuetallyno(newIssuetallyno)
                            .setPono(req.getPono())
                            .setAmt(totalAmt)
                            .setQty(BigDecimal.ZERO)
                            .setProject(wmsTradeC.getProject())
                            .setCompid(wmsTradeC.getCompId())
                            .setPono(wmsTradeC.getLotNo())
                            .setLotno(wmsTradeC.getLotNo())
                            .setMatrlno(wmsTradeC.getMatrlNo())
                            .setStogcode(wmsTradeC.getSendStgNo())
                            .setAcptstgno(wmsTradeC.getStgNo())
                            .setLocno(wmsTradeC.getStgNo())
                            .setIssuetallyno(newIssuetallyno)
                            .setQty(BigDecimal.ZERO)
                            .setAmt(isAdd ? totalAmt : totalAmt.negate())
                            .setPurposeid("12C")
                            .setIsMy(wmsTradeC.getIsMy())
                            .setInspno(wmsTradeC.getTranTallyNo())
                            .setSeqno("0001")
                            .setSystemid(wmsTradeC.getSystemId())
                            .setVchrdate(DateUtil.getDate())
                            .setIssuetype(wmsTradeC.getIssueType());

                    wmsTradeService.updateStock(req);
                }
            }

            for (GrDetailDO detailDO : detailDOS) {
                EpDetailDO epDetail = epDetailMapper.selectBySrlno(detailDO.getInvoiceno());
                if (epDetail != null) {
                    detailDO.setInvoiceno(null);
                    detailDO.setUpdateEmpno(getLoginUserNickname());
                    detailDO.setUpdater(String.valueOf(getLoginUserId()));
                    detailDO.setUpdateDate(DateUtil.getDate());
                    grDetailMapper.updateById(detailDO);
                    epDetail.setStus("N");
                    epDetail.setUpdateEmpno(getLoginUserNickname());
                    epDetail.setUpdater(String.valueOf(getLoginUserId()));
                    epDetail.setUpdateDate(DateUtil.getDate());
                    epDetailMapper.updateById(epDetail);
                }
            }
        }
        grMainDO.setConfirmDate(null);
        grMainDO.setUpdateEmpno(getLoginUserNickname());
        grMainDO.setUpdater(String.valueOf(getLoginUserId()));
        grMainDO.setUpdateDate(DateUtil.getDate());
        grMainMapper.updateById(grMainDO);
    }

    @Override
    public List<AdminUserRespDTO> getUserListByPostIds(GetEmplByPostReqDTO reqDTO) {
        Set<Long> postIds = Collections.emptySet();
        if ("check".equals(reqDTO.getPost())) {
            postIds = SetUtils.asSet(8L);
        } else if ("insp".equals(reqDTO.getPost())) {
            postIds = SetUtils.asSet(7L);
        }
        TenantContextHolder.setTenantId(Long.valueOf(reqDTO.getTenantId()));
        return adminUserApi.getUserListByPostIds(postIds).getCheckedData();
    }

    private void validateGrDetailExists(Long id) {
        if (grDetailMapper.selectById(id) == null) {
            throw exception(GR_DETAIL_NOT_EXISTS);
        }
    }

    private void deleteGrDetailByParentId(Long parentId) {
        grDetailMapper.deleteByParentId(parentId);
    }

    // ==================== 子表（原料结算明细） ====================

    @Override
    public PageResult<PpDetail1DO> getPpDetail1Page(PageParam pageReqVO, Long parentId) {
        return ppDetail1Mapper.selectPage(pageReqVO, parentId);
    }

    @Override
    public Long createPpDetail1(PpDetail1DO ppDetail1) {
        Long parentId = ppDetail1.getParentId();
        validateGrMainExists(parentId);
        GrMainDO mainDO = this.getGrMain(parentId);
        ppDetail1.setProject(mainDO.getProject());
        ppDetail1.setCompId(mainDO.getCompid());
        ppDetail1.setInspNo(mainDO.getInspno());
        ppDetail1.setCreateEmpNo(getLoginUserNickname());
        ppDetail1.setUpdateEmpNo(getLoginUserNickname());
        ppDetail1.setCreator(String.valueOf(getLoginUserId()));
        ppDetail1.setUpdater(String.valueOf(getLoginUserId()));
        ppDetail1.setCreateDate(DateUtil.getDate());
        ppDetail1.setUpdateDate(DateUtil.getDate());
        ppDetail1Mapper.insert(ppDetail1);
        return ppDetail1.getId();
    }

    @Override
    public void updatePpDetail1(PpDetail1DO ppDetail1) {
        // 校验存在
        validatePpDetail1Exists(ppDetail1.getId());
        // 更新
        ppDetail1Mapper.updateById(ppDetail1);
    }

    @Override
    @Transactional
    public void deletePpDetail1(Long id) {
        // 校验存在
        validatePpDetail1Exists(id);
        //删除时 清空交易表结算单号
        PpDetail1DO ppDetail1DO = ppDetail1Mapper.selectById(id);
        wrsTradeDetail2Mapper.updateTranTallyNoNull(ppDetail1DO.getIssueTallyNo());
        // 删除
        ppDetail1Mapper.deleteById(id);
    }

    @Override
    public PpDetail1DO getPpDetail1(Long id) {
        return ppDetail1Mapper.selectById(id);
    }

    private void validatePpDetail1Exists(Long id) {
        if (ppDetail1Mapper.selectById(id) == null) {
            throw exception(PP_DETAIL1_NOT_EXISTS);
        }
    }

    private void deletePpDetail1ByParentId(Long parentId) {
        ppDetail1Mapper.deleteByParentId(parentId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInsertTradeList(ppDetail1BatchImportVO reqVO) {
        GrMainDO grmainDO = getGrMain(reqVO.getParentId());
        reqVO.getList().forEach(updateVO -> {
            PpDetail1DO detail1DO = new PpDetail1DO();
            String serialNo = ppDetail1Mapper.getMaxPpDetail1SeqNoByParentId(reqVO.getParentId());
            String seqNo = "0000";
            if (serialNo != null) {
                seqNo = String.format("%04d", Integer.valueOf(serialNo) + 1);
            }
            detail1DO.setSeqNo(seqNo);
            detail1DO.setParentId(reqVO.getParentId());
            detail1DO.setInspNo(grmainDO.getInspno());
            detail1DO.setIssueTallyNo(updateVO.getIssueTallyNo());
            detail1DO.setChkNo(updateVO.getChkNo());
            detail1DO.setCarNo(updateVO.getCarNo());
            detail1DO.setSettleNum(updateVO.getTransNum());
            detail1DO.setChkAmt(updateVO.getTransAmt());
            detail1DO.setWeighedNum(updateVO.getScaleNum());
            detail1DO.setManualChkNo(updateVO.getManualChkNo());
            detail1DO.setAcptNum(updateVO.getTransNum());

            detail1DO.setProject(getLoginUserTopDeptId().toString());
            detail1DO.setCompId(getLoginUserTenantId().toString());
            detail1DO.setCreateDate(DateUtil.getDate());
            detail1DO.setUpdateDate(DateUtil.getDate());
            detail1DO.setCreateEmpNo(getLoginUserNickname());
            detail1DO.setUpdateEmpNo(getLoginUserNickname());
            detail1DO.setCreator(String.valueOf(getLoginUserId()));
            detail1DO.setUpdater(String.valueOf(getLoginUserId()));
            ppDetail1Mapper.insert(detail1DO);
            WrsTradeDetail2DO detail2DO = wrsTradeDetail2Service.getTradeDetail2(updateVO.getId());
            detail2DO.setTranTallyNo(grmainDO.getInspno());
            wrsTradeDetail2Mapper.updateById(detail2DO);
        });
    }

    @Override
    @Transactional
    public void calSettle(Long id) {
        ppDetail3Mapper.deleteByParentId(id);
        GrMainDO grMainDO = getGrMain(id);
        PoMainDO poMainDO = poMainMapper.selectByPono(grMainDO.getPono());
        List<PoMainVDO> mainVList = poMainVMapper.selectListByParentId(poMainDO.getId());
        List<PpDetail1DO> detail1DOList = ppDetail1Mapper.selectList(PpDetail1DO::getParentId, id);
        List<PpDetail2DO> detail2DOList = ppDetail2Mapper.selectList(PpDetail2DO::getParentId, id);

        if (detail1DOList == null || detail1DOList.size() == 0) {
            throw exception(PP_DETAIL1_NOT_EXISTS);
        }
        if (detail2DOList == null || detail2DOList.size() == 0) {
            throw exception(PP_DETAIL2_NOT_EXISTS);
        }
        if (mainVList == null || mainVList.size() == 0) {
            throw exception(PO_MAIN_V_NOT_EXISTS);
        }

        PoMainVDO mainVDO = mainVList.get(0);
        List<PoDetailVDO> detailVDOList = poDetailVMapper.selectListByParentId(mainVDO.getId());
        List<PoDetailVDO> details = new ArrayList<>();
        if (detailVDOList == null || detailVDOList.size() == 0) {
            throw exception(PO_DETAIL_V_NOT_EXISTS);
        }

        String matrlno = detailVDOList.get(0).getMatrlno();

        for (PoDetailVDO detailVDO : detailVDOList) {
            if (details != null && details.size() > 0) {
                boolean flag = true;
                for (PoDetailVDO d1 : details) {
                    if (detailVDO.getMatrlno().equals(d1.getMatrlno())) {
                        d1.setQty(detailVDO.getQty().add(d1.getQty()));
                        d1.setAmt(detailVDO.getAmt().add(d1.getAmt()));
                        flag = false;
                    }
                }
                if (flag) {
                    PoDetailVDO d1 = new PoDetailVDO();
                    d1.setMatrlno(detailVDO.getMatrlno());
                    d1.setAmt(detailVDO.getAmt());
                    d1.setQty(detailVDO.getQty());
                    details.add(d1);
                }
            } else {
                PoDetailVDO d1 = new PoDetailVDO();
                d1.setMatrlno(detailVDO.getMatrlno());
                d1.setAmt(detailVDO.getAmt());
                d1.setQty(detailVDO.getQty());
                details.add(d1);
            }
        }
        BigDecimal deductedAmt = BigDecimal.ZERO;
        for (PpDetail2DO detail2DO : detail2DOList) {
            deductedAmt = deductedAmt.add(detail2DO.getDeductedAmt());
        }
        Collection<String> nos = new ArrayList<>();
        for (PpDetail1DO detail1DO : detail1DOList) {
            nos.add(detail1DO.getIssueTallyNo());
        }
        List<WrsTradeDetail2DO> wrsTradeDetail2s = wrsTradeDetail2Mapper.selectByIssueTallyNos(nos);
        int i = 1;
        for (PoDetailVDO detailVDO : details) {
            BigDecimal settleNum = BigDecimal.ZERO;
            for (WrsTradeDetail2DO detail2DO : wrsTradeDetail2s) {
                for (PpDetail1DO detail1DO : detail1DOList) {
                    if (detail2DO.getMatrlNo().equals(detailVDO.getMatrlno()) && detail1DO.getIssueTallyNo().equals(detail2DO.getIssueTallyNo())) {
                        settleNum = settleNum.add(detail1DO.getSettleNum());
                    }
                }
            }

            BigDecimal amt = detailVDO.getAmt();
            BigDecimal qty = detailVDO.getQty();
            BigDecimal taxRate = mainVDO.getTaxrate();
            BigDecimal rate1 = (taxRate.add(new BigDecimal(100))).divide(new BigDecimal(100));
            BigDecimal rate2 = (new BigDecimal(100).subtract(taxRate)).divide(new BigDecimal(100));
            BigDecimal price = amt.divide(qty, 4, BigDecimal.ROUND_HALF_UP);

            PpDetail3DO ppDetail3 = new PpDetail3DO();
            ppDetail3.setProject(getLoginUserTopDeptId().toString());
            ppDetail3.setCompId(getLoginUserTenantId().toString());
            ppDetail3.setParentId(id);
            ppDetail3.setPoNo(grMainDO.getPono());
            ppDetail3.setInspNo(grMainDO.getInspno());
            ppDetail3.setVersion(mainVDO.getPover());
            ppDetail3.setMatrlNo(matrlno);
            ppDetail3.setDeductedAmt(deductedAmt);
            ppDetail3.setTaxRate(taxRate);
            ppDetail3.setSettleWgt(settleNum);
            ppDetail3.setIsTax(mainVDO.getIstax());
            if ("Y".equals(mainVDO.getIstax())) {
                ppDetail3.setSettleUnitPrice(price);
                ppDetail3.setSettleGoodAmtExTax(price.divide(rate1, 4, BigDecimal.ROUND_HALF_UP));
            } else {
                ppDetail3.setSettleGoodAmtExTax(price);
                ppDetail3.setSettleUnitPrice(price.divide(rate2, 4, BigDecimal.ROUND_HALF_UP));
            }
            ppDetail3.setGoodAmt(settleNum.multiply(ppDetail3.getSettleGoodAmtExTax()).setScale(4, BigDecimal.ROUND_HALF_UP));
            ppDetail3.setTotalAmtExTax(ppDetail3.getGoodAmt().add(deductedAmt));
            ppDetail3.setTotalAmt(ppDetail3.getTotalAmtExTax().multiply(rate1));
            ppDetail3.setSettleGoodAmt(ppDetail3.getTotalAmt().subtract(ppDetail3.getTotalAmtExTax()));
            ppDetail3.setCreateDate(DateUtil.getDate());
            ppDetail3.setUpdateDate(DateUtil.getDate());
            ppDetail3.setCreateEmpNo(getLoginUserNickname());
            ppDetail3.setUpdateEmpNo(getLoginUserNickname());
            ppDetail3.setCreator(String.valueOf(getLoginUserId()));
            ppDetail3.setUpdater(String.valueOf(getLoginUserId()));
            ppDetail3Mapper.insert(ppDetail3);
            i++;
        }
    }

    @Override
    public void continueGrMain(Long id, String stus) {
        // 校验存在
        validateGrMainExists(id);
        GrMainDO grMainDO = grMainMapper.selectById(id);
        if ("grmain".equals(grMainDO.getAppid()) && "F".equals(stus)) {
            grMainDO.setRecvDate(DateUtil.getDate());
            grMainDO.setRecvEmpl(getLoginUserNickname());
            grMainDO.setRecvEmplNo(getLoginUserId().toString());
        }
        if ("xiaochengxu".equals(grMainDO.getAppid()) && "F".equals(stus)) {
            grMainDO.setRecvDate(DateUtil.getDate());
            grMainDO.setRecvEmpl(getLoginUserNickname());
            grMainDO.setRecvEmplNo(getLoginUserId().toString());
        }
        grMainDO.setStus(stus);
        grMainDO.setUpdater(getLoginUserId().toString());
        grMainDO.setUpdateEmpno(getLoginUserNickname());
        grMainMapper.updateById(grMainDO);
    }

    /**
     * 移动端检查操作 上传照片并标记为已检查
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void checkGrDetail(AppGrDetailCheckSaveVO checkSaveVO) {
        // detail记录数据变更
        GrDetailDO detailDO = grDetailMapper.selectById(checkSaveVO.getId());
        if (detailDO == null || detailDO.getDeleted()) {
            // detail记录不存在
            throw exception(PP_DETAIL1_NOT_EXISTS);
        }

        detailDO.setDrawCheck(checkSaveVO.getDrawCheck());
        detailDO.setInstCheck(checkSaveVO.getInstCheck());
        detailDO.setNameCheck(checkSaveVO.getNameCheck());
        detailDO.setCertCheck(checkSaveVO.getCertCheck());
        detailDO.setSpecCheck(checkSaveVO.getSpecCheck());
        detailDO.setMemoCheck(StringUtils.isBlank(checkSaveVO.getMemoCheck()) ? "" : checkSaveVO.getMemoCheck());

        int checkCount = Stream
                .of(checkSaveVO.getDrawCheck(), checkSaveVO.getInstCheck(), checkSaveVO.getNameCheck(),
                        checkSaveVO.getCertCheck(), checkSaveVO.getSpecCheck())
                .mapToInt(item -> Optional.ofNullable(item).orElse(0)).sum();
        if (checkCount == 5) {
            // 所有项目已检查 TODO 已检查状态
            // detailDO.setCheckstus("已检查")
        }

        // 关联记录变更
        Map<String, List<AppGrDetailFileRelationVO>> filesMap = checkSaveVO.getFilesMap();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String createDate = sdf.format(new Date());
        String updateDate = createDate;
        String createEmpNo = String.valueOf(getLoginUserId());
        String updateEmpNo = createEmpNo;
        String creator = getLoginUserNickname();
        String updater = creator;
        LocalDateTime now = LocalDateTime.now();
        if (filesMap != null && !filesMap.isEmpty()) {
            // 处理文件数据
            List<AppGrDetailFileRelationVO> drawCheckFiles =
                    Optional.ofNullable(filesMap.get("drawCheck")).orElse(new ArrayList<>());
            List<AppGrDetailFileRelationVO> instCheckFiles =
                    Optional.ofNullable(filesMap.get("instCheck")).orElse(new ArrayList<>());
            List<AppGrDetailFileRelationVO> nameCheckFiles =
                    Optional.ofNullable(filesMap.get("nameCheck")).orElse(new ArrayList<>());
            List<AppGrDetailFileRelationVO> certCheckFiles =
                    Optional.ofNullable(filesMap.get("certCheck")).orElse(new ArrayList<>());
            List<AppGrDetailFileRelationVO> specCheckFiles =
                    Optional.ofNullable(filesMap.get("specCheck")).orElse(new ArrayList<>());
            // 获取已经存在的files内容
            Map<String, List<GrDetailFileRelationDO>> existsFilesMap =
                    grDetailFileRelationMapper.selectCheckMapByDetailId(checkSaveVO.getId());
            List<GrDetailFileRelationDO> existsDrawCheckFiles =
                    Optional.ofNullable(existsFilesMap.get("drawCheck")).orElse(new ArrayList<>());
            List<GrDetailFileRelationDO> existsInstCheckFiles =
                    Optional.ofNullable(existsFilesMap.get("instCheck")).orElse(new ArrayList<>());
            List<GrDetailFileRelationDO> existsNameCheckFiles =
                    Optional.ofNullable(existsFilesMap.get("nameCheck")).orElse(new ArrayList<>());
            List<GrDetailFileRelationDO> existsCertCheckFiles =
                    Optional.ofNullable(existsFilesMap.get("certCheck")).orElse(new ArrayList<>());
            List<GrDetailFileRelationDO> existsSpecCheckFiles =
                    Optional.ofNullable(existsFilesMap.get("specCheck")).orElse(new ArrayList<>());
            // 对于（旧-有，新-无）—— 执行删除操作
            // 对于（旧-有，新-有）—— 不执行任何操作
            // 对于（旧-无，新-无）—— 不执行任何操作
            // 对于（旧-无，新-有）—— 执行新增操作
            // 逻辑删除 修改的list
            List<GrDetailFileRelationDO> updateRelations = new ArrayList<>();
            // 新增的list
            List<GrDetailFileRelationDO> saveRelations = new ArrayList<>();
            // 对于（旧-有，新-无）—— 执行删除操作
            updateRelations
                    .addAll(
                            existsDrawCheckFiles.stream()
                                    .filter(item -> !drawCheckFiles.stream().map(AppGrDetailFileRelationVO::getInfraFileId)
                                            .collect(Collectors.toList()).contains(item.getInfraFileId()))
                                    .collect(Collectors.toList()));
            updateRelations
                    .addAll(
                            existsInstCheckFiles.stream()
                                    .filter(item -> !instCheckFiles.stream().map(AppGrDetailFileRelationVO::getInfraFileId)
                                            .collect(Collectors.toList()).contains(item.getInfraFileId()))
                                    .collect(Collectors.toList()));
            updateRelations
                    .addAll(
                            existsNameCheckFiles.stream()
                                    .filter(item -> !nameCheckFiles.stream().map(AppGrDetailFileRelationVO::getInfraFileId)
                                            .collect(Collectors.toList()).contains(item.getInfraFileId()))
                                    .collect(Collectors.toList()));
            updateRelations
                    .addAll(
                            existsCertCheckFiles.stream()
                                    .filter(item -> !certCheckFiles.stream().map(AppGrDetailFileRelationVO::getInfraFileId)
                                            .collect(Collectors.toList()).contains(item.getInfraFileId()))
                                    .collect(Collectors.toList()));
            updateRelations
                    .addAll(
                            existsSpecCheckFiles.stream()
                                    .filter(item -> !specCheckFiles.stream().map(AppGrDetailFileRelationVO::getInfraFileId)
                                            .collect(Collectors.toList()).contains(item.getInfraFileId()))
                                    .collect(Collectors.toList()));
            // 对于（旧-无，新-有）—— 执行新增操作
            saveRelations.addAll(drawCheckFiles.stream()
                    .filter(item -> !existsDrawCheckFiles.stream().map(GrDetailFileRelationDO::getInfraFileId)
                            .collect(Collectors.toList()).contains(item.getInfraFileId()))
                    .map(item -> BeanUtils.toBean(item, GrDetailFileRelationDO.class)).collect(Collectors.toList()));
            saveRelations.addAll(instCheckFiles.stream()
                    .filter(item -> !existsInstCheckFiles.stream().map(GrDetailFileRelationDO::getInfraFileId)
                            .collect(Collectors.toList()).contains(item.getInfraFileId()))
                    .map(item -> BeanUtils.toBean(item, GrDetailFileRelationDO.class)).collect(Collectors.toList()));
            saveRelations.addAll(nameCheckFiles.stream()
                    .filter(item -> !existsNameCheckFiles.stream().map(GrDetailFileRelationDO::getInfraFileId)
                            .collect(Collectors.toList()).contains(item.getInfraFileId()))
                    .map(item -> BeanUtils.toBean(item, GrDetailFileRelationDO.class)).collect(Collectors.toList()));
            saveRelations.addAll(certCheckFiles.stream()
                    .filter(item -> !existsCertCheckFiles.stream().map(GrDetailFileRelationDO::getInfraFileId)
                            .collect(Collectors.toList()).contains(item.getInfraFileId()))
                    .map(item -> BeanUtils.toBean(item, GrDetailFileRelationDO.class)).collect(Collectors.toList()));
            saveRelations.addAll(specCheckFiles.stream()
                    .filter(item -> !existsSpecCheckFiles.stream().map(GrDetailFileRelationDO::getInfraFileId)
                            .collect(Collectors.toList()).contains(item.getInfraFileId()))
                    .map(item -> BeanUtils.toBean(item, GrDetailFileRelationDO.class)).collect(Collectors.toList()));

            // 执行删除操作
            grDetailFileRelationMapper.deleteRelationsByIds(
                    updateRelations.stream().map(GrDetailFileRelationDO::getInfraFileId).collect(Collectors.toList()));
            // 执行保存操作
            saveRelations.forEach(item -> {
                item.setDeleted(false);
            });
            grDetailFileRelationMapper.insertBatch(saveRelations);
        }

        GrDetailDO updateEntity = new GrDetailDO();
        updateEntity.setMemoCheck(detailDO.getMemoCheck());
        updateEntity.setCertCheck(detailDO.getCertCheck());
        updateEntity.setDrawCheck(detailDO.getDrawCheck());
        updateEntity.setSpecCheck(detailDO.getSpecCheck());
        updateEntity.setNameCheck(detailDO.getSpecCheck());
        updateEntity.setInstCheck(detailDO.getInstCheck());
        // detail更新 check的字段。
        grDetailMapper.update(updateEntity,
                new LambdaUpdateWrapper<GrDetailDO>().eq(GrDetailDO::getId, detailDO.getId())
                        .set(GrDetailDO::getMemoCheck, detailDO.getMemoCheck())
                        .set(GrDetailDO::getCertCheck, detailDO.getCertCheck())
                        .set(GrDetailDO::getDrawCheck, detailDO.getDrawCheck())
                        .set(GrDetailDO::getSpecCheck, detailDO.getSpecCheck())
                        .set(GrDetailDO::getNameCheck, detailDO.getNameCheck())
                        .set(GrDetailDO::getInstCheck, detailDO.getInstCheck()));
    }

    @Override
    public List<AppGrDetailFileRelationVO> selectRelationListByDetailId(Long detailId) {
        return grDetailFileRelationMapper.selectListByDetailId(detailId);
    }

    @Override
    public Map<Long, Long> countDetailByGrMainId(List<Long> grMainIds) {
        Map<Long, Long> result = new HashMap<>();
        if (CollectionUtil.isNotEmpty(grMainIds)) {
            List<GrDetailDO> detailList =
                    grDetailMapper.selectList(new LambdaQueryWrapperX<GrDetailDO>().in(GrDetailDO::getParentId, grMainIds));
            if (CollectionUtil.isNotEmpty(detailList)) {
                result =
                        detailList.stream().collect(Collectors.groupingBy(GrDetailDO::getParentId, Collectors.counting()));
            }
        }
        return result;
    }

    // ==================== 子表（结算金额调整） ====================

    @Override
    public PageResult<PpDetail2DO> getPpDetail2Page(PageParam pageReqVO, Long parentId) {
        return ppDetail2Mapper.selectPage(pageReqVO, parentId);
    }

    @Override
    public Long createPpDetail2(PpDetail2DO ppDetail2) {
        Long parentId = ppDetail2.getParentId();
        validateGrMainExists(parentId);
        GrMainDO mainDO = this.getGrMain(parentId);
        ppDetail2.setProject(mainDO.getProject());
        ppDetail2.setCompId(mainDO.getCompid());
        ppDetail2.setInspNo(mainDO.getInspno());
        String serialNo = ppDetail2Mapper.getMaxPpItemNoByParentId(parentId);
        String itemno = "0000";
        if (serialNo != null) {
            itemno = String.format("%04d", Integer.valueOf(serialNo) + 1);
        }
        ppDetail2.setItemNo(itemno);
        ppDetail2.setCreateEmpNo(getLoginUserNickname());
        ppDetail2.setUpdateEmpNo(getLoginUserNickname());
        ppDetail2.setCreator(String.valueOf(getLoginUserId()));
        ppDetail2.setUpdater(String.valueOf(getLoginUserId()));
        ppDetail2.setCreateDate(DateUtil.getDate());
        ppDetail2.setUpdateDate(DateUtil.getDate());
        ppDetail2Mapper.insert(ppDetail2);
        return ppDetail2.getId();
    }

    @Override
    public void updatePpDetail2(PpDetail2DO ppDetail2) {
        // 校验存在
        validatePpDetail2Exists(ppDetail2.getId());
        // 更新
        ppDetail2.setUpdateEmpNo(getLoginUserNickname());
        ppDetail2.setUpdater(String.valueOf(getLoginUserId()));
        ppDetail2.setUpdateDate(DateUtil.getDate());
        ppDetail2Mapper.updateById(ppDetail2);
    }

    @Override
    public void deletePpDetail2(Long id) {
        // 校验存在
        validatePpDetail2Exists(id);
        // 删除
        ppDetail2Mapper.deleteById(id);
    }

    @Override
    public PpDetail2DO getPpDetail2(Long id) {
        return ppDetail2Mapper.selectById(id);
    }

    private void validatePpDetail2Exists(Long id) {
        if (ppDetail2Mapper.selectById(id) == null) {
            throw exception(PP_DETAIL2_NOT_EXISTS);
        }
    }

    private void deletePpDetail2ByParentId(Long parentId) {
        ppDetail2Mapper.deleteByParentId(parentId);
    }

    // ==================== 子表（总结算） ====================

    @Override
    public PageResult<PpDetail3DO> getPpDetail3Page(PageParam pageReqVO, Long parentId) {
        return ppDetail3Mapper.selectPage(pageReqVO, parentId);
    }

    @Override
    public Long createPpDetail3(PpDetail3DO ppDetail3) {
        ppDetail3Mapper.insert(ppDetail3);
        return ppDetail3.getId();
    }

    @Override
    public void updatePpDetail3(PpDetail3DO ppDetail3) {
        // 校验存在
        validatePpDetail3Exists(ppDetail3.getId());
        // 更新
        ppDetail3Mapper.updateById(ppDetail3);
    }

    @Override
    public void deletePpDetail3(Long id) {
        // 校验存在
        validatePpDetail3Exists(id);
        // 删除
        ppDetail3Mapper.deleteById(id);
    }

    @Override
    public PpDetail3DO getPpDetail3(Long id) {
        return ppDetail3Mapper.selectById(id);
    }

    private void validatePpDetail3Exists(Long id) {
        if (ppDetail3Mapper.selectById(id) == null) {
            throw exception(PP_DETAIL3_NOT_EXISTS);
        }
    }

    private void deletePpDetail3ByParentId(Long parentId) {
        ppDetail3Mapper.deleteByParentId(parentId);
    }


    @Transactional
    @Override
    public void confirmGrMain(Long id) {
        Date date = new Date();
        GrMainDO grMainDO = getGrMain(id);
        grMainDO.setInspEmpl(getLoginUserNickname());
        grMainDO.setInspEmplNo(getLoginUserId().toString());
        grMainDO.setConfirmDate(DateUtil.getDate());
        if ("grmain".equals(grMainDO.getAppid())) {
            List<GrDetailDO> grDetailDOS = grDetailMapper.selectListByParentId(id);
            // 1.1 校验验收单金额、数量
            checkQtyAndAmt(grMainDO);
            for (GrDetailDO grDetailDO : grDetailDOS) {
                int i = 1;
                WmsTradeParamReqVO reqVO = new WmsTradeParamReqVO();
                reqVO.setProject(grMainDO.getProject())
                        .setCompid(grMainDO.getCompid())
                        .setPono(grMainDO.getPono())
                        .setInspno(grMainDO.getInspno())
                        .setBatch(grDetailDO.getPoitemno())
                        .setMatrlno(grDetailDO.getMatrlno())
                        .setLocno(grDetailDO.getLocno())
                        .setStogcode(grDetailDO.getStogcode())
                        .setQty(grDetailDO.getInspqty())
                        .setAmt(grDetailDO.getInspamt())
                        .setVchrdate(DateUtil.getDate())
                        .setSeqno(String.valueOf(i))
                        .setIsConfirm("Y")
                        .setSystemid("WMS").setPurposeid("10A");
                String issuetallyno = wmsTradeService.updateStock(reqVO);
                grMainDO.setMino(issuetallyno);
                i++;
            }
            grMainDO.setStus("R");
        } else if ("ppmain".equals(grMainDO.getAppid()) || "myppmain".equals(grMainDO.getAppid())) {
            List<PpDetail3DO> detailDOS = ppDetail3Mapper.selectList(PpDetail3DO::getParentId, id);
            List<PpDetail1DO> detail1DOList = ppDetail1Mapper.selectList(PpDetail1DO::getParentId, id);
            if (detail1DOList == null || detail1DOList.size() == 0) {
                throw exception(PP_DETAIL1_NOT_EXISTS);
            }
            BigDecimal totalAmtExTax = detailDOS.get(0).getTotalAmtExTax();
            String issueTallyNo = detail1DOList.get(0).getIssueTallyNo();
            WmsTradeDO wmsTradeDO = wmsTradeMapper.selectByIssuetallyno(issueTallyNo);
            BigDecimal cha = totalAmtExTax.subtract(wmsTradeDO.getTransAmt());
            if (cha.compareTo(BigDecimal.ZERO) != 0) {
                WmsTradeParamReqVO reqVO = new WmsTradeParamReqVO();
                reqVO.setProject(grMainDO.getProject())
                        .setCompid(grMainDO.getCompid())
                        .setInspno(grMainDO.getInspno())
                        .setMatrlno(wmsTradeDO.getMatrlNo())
                        .setLocno(wmsTradeDO.getStgNo())
                        .setPono(wmsTradeDO.getLotNo())
                        .setQty(new BigDecimal(0))
                        .setAmt(cha)
                        .setSubmitFlag("Y")
                        .setVchrdate(DateUtil.getDate())
                        .setSeqno(String.valueOf(1))
                        .setSystemid("WRS").setPurposeid("12A");
                if ("myppmain".equals(grMainDO.getAppid())) {
                    reqVO.setIsMy("Y");
                }
                wmsTradeService.updateStock(reqVO);
            }
            grMainDO.setTotalAmt(detailDOS.get(0).getTotalAmt());
            grMainDO.setTaxamt(detailDOS.get(0).getSettleGoodAmt());
            grMainDO.setStus("Y");
        } else if ("grmainm".equals(grMainDO.getAppid())) {
            List<GrDetailDO> detailDOS = grDetailMapper.selectList(GrDetailDO::getParentId, id);
            if (detailDOS == null || detailDOS.size() == 0) {
                throw exception(GR_DETAIL_NOT_EXISTS);
            }
            BigDecimal totalAmt = new BigDecimal(0);
            for (GrDetailDO detailDO : detailDOS) {
                BigDecimal amt = detailDO.getInspamt();
                if (amt != null && amt.compareTo(BigDecimal.ZERO) != 0) {
                    totalAmt = totalAmt.add(amt);
                    WmsTradeParamReqVO reqVO = new WmsTradeParamReqVO();
                    reqVO.setProject(grMainDO.getProject())
                            .setCompid(grMainDO.getCompid())
                            .setInspno(grMainDO.getInspno())
                            .setMatrlno(detailDO.getMatrlno())
                            .setLocno(detailDO.getLocno())
                            .setPono(detailDO.getPono())
                            .setQty(new BigDecimal(0))
                            .setAmt(amt)
                            .setSubmitFlag("Y")
                            .setVchrdate(DateUtil.getDate())
                            .setSeqno(String.valueOf(1))
                            .setSystemid("WMS").setPurposeid("12A");
                    wmsTradeService.updateStock(reqVO);
                }
            }
            grMainDO.setTotalAmt(totalAmt);
            grMainDO.setStus("Y");
        } else if ("grmainy".equals(grMainDO.getAppid())) {
            PoMainDO poMainDO = poMainMapper.selectByPono(grMainDO.getPono());
            List<GrDetailDO> detailDOS = grDetailMapper.selectList(GrDetailDO::getParentId, id);
            List<SettlementDetailDO> settlementDetailList = settlementDetailMapper.selectList(SettlementDetailDO::getParentId, id);
            if (detailDOS == null || detailDOS.size() == 0) {
                throw exception(PP_DETAIL1_NOT_EXISTS);
            }

            List<String> issueTallyNoList = new ArrayList<>();
            if (settlementDetailList != null && settlementDetailList.size() > 0) {
                for (SettlementDetailDO settlementDetailDO : settlementDetailList) {
                    issueTallyNoList.add(settlementDetailDO.getIssueTallyNo());
                }
            }

            if (issueTallyNoList != null && issueTallyNoList.size() > 0) {
                for (String issueTallyNo : issueTallyNoList) {
                    WmsTradeDO wmsTradeC = wmsTradeMapper.selectByIssuetallyno(issueTallyNo);
                    String newIssuetallyno = issueTallyNo + "C";
                    WmsTradeDO wmsTradeCC = wmsTradeMapper.selectByIssuetallyno(newIssuetallyno);
                    Boolean isAdd = true;
                    if (wmsTradeCC != null && "Y".equals(wmsTradeCC.getStus())) {
                        isAdd = false;
                    }

                    BigDecimal totalAmt = BigDecimal.ZERO;
                    for (SettlementDetailDO settlementDetailDO : settlementDetailList) {
                        if (issueTallyNo.equals(settlementDetailDO.getIssueTallyNo())) {
                            totalAmt = totalAmt.add(settlementDetailDO.getActualFee().subtract(wmsTradeC.getTransAmt()));
                        }
                    }
                    if (wmsTradeCC == null) {
                        WmsTradeDO newWmsTradeDO = new WmsTradeDO();
                        newWmsTradeDO.setProject(wmsTradeC.getProject());
                        newWmsTradeDO.setCompId(wmsTradeC.getCompId());
                        newWmsTradeDO.setParentId(wmsTradeC.getId());
                        newWmsTradeDO.setIssueTallyNo(newIssuetallyno);
                        newWmsTradeDO.setContractNo(wmsTradeC.getContractNo());
                        newWmsTradeDO.setInventoryType(wmsTradeC.getInventoryType());
                        newWmsTradeDO.setTranTallyNo(wmsTradeC.getChkNo());
                        newWmsTradeDO.setSupplierNo(wmsTradeC.getSupplierNo());
                        newWmsTradeDO.setMatrlNo(wmsTradeC.getMatrlNo());
                        newWmsTradeDO.setStgNo(wmsTradeC.getStgNo());
                        newWmsTradeDO.setLotNo(wmsTradeC.getContractNo());
                        newWmsTradeDO.setTransNum(BigDecimal.ZERO);
                        newWmsTradeDO.setScaleNum(wmsTradeC.getScaleNum());
                        newWmsTradeDO.setTransAmt(isAdd ? totalAmt : totalAmt.negate());
                        newWmsTradeDO.setPurposeId("12C");
                        newWmsTradeDO.setSystemId(wmsTradeC.getSystemId());
                        newWmsTradeDO.setAppId(wmsTradeC.getAppId());
                        newWmsTradeDO.setStus(isAdd ? "Y" : "N");
                        newWmsTradeDO.setInspType("A");
                        newWmsTradeDO.setIssueType("A");
                        newWmsTradeDO.setVchrDate(wmsTradeC.getVchrDate());
                        newWmsTradeDO.setCreateEmpNo(getLoginUserNickname());
                        newWmsTradeDO.setCreator(getLoginUserId().toString());
                        newWmsTradeDO.setCreateDate(DateUtil.getDate());
                        newWmsTradeDO.setUpdateEmpNo(getLoginUserNickname());
                        newWmsTradeDO.setUpdater(getLoginUserId().toString());
                        newWmsTradeDO.setUpdateDate(DateUtil.getDate());
                        newWmsTradeDO.setFinalDate(LocalDate.now());
                        newWmsTradeDO.setFinalTime(LocalTime.now());
                        newWmsTradeDO.setFinalEmpNo(getLoginUserNickname());
                        wmsTradeMapper.insert(newWmsTradeDO);
                    } else {
                        wmsTradeCC.setContractNo(wmsTradeC.getContractNo());
                        wmsTradeCC.setTranTallyNo(wmsTradeC.getTranTallyNo());
                        wmsTradeCC.setSupplierNo(wmsTradeC.getSupplierNo());
                        wmsTradeCC.setMatrlNo(wmsTradeC.getMatrlNo());
                        wmsTradeCC.setStgNo(wmsTradeC.getStgNo());
                        wmsTradeCC.setLotNo(wmsTradeC.getContractNo());
                        wmsTradeCC.setStus(isAdd ? "Y" : "N");
                        wmsTradeCC.setTransAmt(totalAmt);
                        wmsTradeCC.setVchrDate(wmsTradeC.getVchrDate());
                        wmsTradeCC.setUpdateEmpNo(getLoginUserNickname());
                        wmsTradeCC.setUpdater(getLoginUserId().toString());
                        wmsTradeCC.setUpdateDate(DateUtil.getDate());
                        wmsTradeCC.setFinalDate(LocalDate.now());
                        wmsTradeCC.setFinalTime(LocalTime.now());
                        wmsTradeCC.setFinalEmpNo(getLoginUserNickname());
                        wmsTradeMapper.updateById(wmsTradeCC);
                    }
                    WmsTradeParamReqVO req = new WmsTradeParamReqVO();
                    req.setIssuetallyno(newIssuetallyno)
                            .setPono(req.getPono())
                            .setAmt(totalAmt)
                            .setQty(BigDecimal.ZERO)
                            .setProject(wmsTradeC.getProject())
                            .setCompid(wmsTradeC.getCompId())
                            .setPono(wmsTradeC.getLotNo())
                            .setLotno(wmsTradeC.getLotNo())
                            .setMatrlno(wmsTradeC.getMatrlNo())
                            .setStogcode(wmsTradeC.getSendStgNo())
                            .setAcptstgno(wmsTradeC.getStgNo())
                            .setLocno(wmsTradeC.getStgNo())
                            .setIssuetallyno(newIssuetallyno)
                            .setQty(BigDecimal.ZERO)
                            .setAmt(isAdd ? totalAmt : totalAmt.negate())
                            .setPurposeid("12C")
                            .setIsMy(wmsTradeC.getIsMy())
                            .setInspno(wmsTradeC.getTranTallyNo())
                            .setSeqno("0001")
                            .setSystemid(wmsTradeC.getSystemId())
                            .setVchrdate(DateUtil.getDate())
                            .setIssuetype(wmsTradeC.getIssueType());

                    wmsTradeService.updateStock(req);
                }
            }


            for (GrDetailDO detailDO : detailDOS) {
                String srlno = noRedisDAO.generate(PmsNoRedisDAO.EP_DETAIL_NO_PREFIX);
                if (epDetailMapper.selectBySrlno(srlno) != null) {
                    throw exception(ISSUETALLY_NO_EXISTS);
                }
                PoDetailDO poDetailDO = poDetailMapper.selectOne(PoDetailDO::getPono, grMainDO.getPono(), PoDetailDO::getPoitemno, detailDO.getPoitemno());
                //应付账款暂估
                EpDetailDO epDetail = new EpDetailDO();
                epDetail.setProject(detailDO.getProject());
                epDetail.setCompid(detailDO.getCompid());
                epDetail.setSrlno(srlno);
                epDetail.setInspno(detailDO.getInspno());
                epDetail.setInspamt(detailDO.getPretaxamt());
                epDetail.setPono(detailDO.getProjectno());
                epDetail.setCrcy(poMainDO.getCrcy());
                epDetail.setCnyamt(grMainDO.getTotalAmt());
                epDetail.setExchangerate(poDetailDO.getTaxrate().multiply(detailDO.getPretaxamt()).setScale(2, BigDecimal.ROUND_HALF_UP));
                epDetail.setVendorno(poMainDO.getVendorno());
                epDetail.setScrapkind("Y");
                epDetail.setMatrlno(detailDO.getMatrlno());
                epDetail.setReqdept(getLoginUserDeptId().toString());
                epDetail.setRespempl(getLoginUserId().toString());
                epDetail.setStus("S");
                epDetail.setDomfrn(poMainDO.getDomfrn());
                epDetail.setPurcode(poMainDO.getPurcode());
                epDetail.setPurtype(poMainDO.getPotype());
                epDetail.setInspwgt(detailDO.getInspqty());
                epDetail.setCreateEmpno(getLoginUserNickname());
                epDetail.setUpdateEmpno(getLoginUserNickname());
                epDetail.setCreator(String.valueOf(getLoginUserId()));
                epDetail.setUpdater(String.valueOf(getLoginUserId()));
                epDetail.setCreateDate(DateUtil.getDate());
                epDetail.setUpdateDate(DateUtil.getDate());
                epDetailMapper.insert(epDetail);
                detailDO.setInvoiceno(srlno);
                grDetailMapper.updateById(detailDO);
            }
            grMainDO.setStus("Y");
        }
        grMainMapper.updateById(grMainDO);
    }


    private BigDecimal getUnitPrice(String project, String compid, String matrlno, String lotno) {
        WmsStockPageReqVO wmsStockReq = new WmsStockPageReqVO();
        wmsStockReq.setProject(project);
        wmsStockReq.setCompid(compid);
        wmsStockReq.setMatrlno(matrlno);
        wmsStockReq.setLotno(lotno);
        wmsStockReq.setPageSize(PageParam.PAGE_SIZE_NONE);
        PageResult<WmsStockDO> wmsStockDOList = wmsStockMapper.selectPage(wmsStockReq);
        if (wmsStockDOList.getTotal() == 0) {
            throw exception(TEADE_COMMON_ERROR, "没有找到项目号【" + project + "】公司别【" + compid + "】料号【" + matrlno + "】批号【" + lotno + "】的实时库存信息。");
        }
        WmsStockDO stockDO = wmsStockDOList.getList().get(0);
        if (stockDO.getEndqty().compareTo(BigDecimal.ZERO) == 0) {
            throw exception(TEADE_COMMON_ERROR, "项目号【" + project + "】公司别【" + compid + "】料号【" + matrlno + "】批号【" + lotno + "】的实时库存数量不能为0");
        }
        return stockDO.getEndamt().divide(stockDO.getEndqty(), 6, RoundingMode.HALF_DOWN);
    }

    // 校验验收单金额、数量
    private void checkQtyAndAmt(GrMainDO grMainDO) {
        // 本次验收单金额、数量
        BigDecimal totalqty = BigDecimal.ZERO;
        BigDecimal totalamt = BigDecimal.ZERO;
        StringBuffer message = new StringBuffer("");
        // 获取合同
        PoMainDO poMainDO = poMainMapper.selectOne(PoMainDO::getPono, grMainDO.getPono());
        // 获取合同明细
        List<PoDetailDO> poDetailDOs = poDetailMapper.selectListByParentId(poMainDO.getId());
        // 获取该合同下全部已验收确认的验收单
        List<GrMainDO> confirmList = grMainMapper.selectConfirmPono(grMainDO.getPono());
        // 获取该验收单下全部验收明细
        List<GrDetailDO> grDetailDOs = grDetailMapper.selectListByParentId(grMainDO.getId());
        List<GrMainDO> currentGrMainDO = confirmList.stream()
                .filter(p -> grMainDO.getInspno().equals(p.getInspno()))
                .collect(Collectors.toList());

        confirmList.removeAll(currentGrMainDO);
        for (GrDetailDO grDetailDO : grDetailDOs) {
            if (StringUtils.isBlank(grDetailDO.getLocno())) {
                message.append("验收项次：" + grDetailDO.getInspitemno() + "储位不能为空！");
            } else if (StringUtils.isNotBlank(grDetailDO.getLocno())) {
                if (storageMapper.selectOne(StorageDO::getLayerid, grDetailDO.getLocno(), StorageDO::getLayernum, "4") == null) {
                    message.append("验收项次：" + grDetailDO.getInspitemno() + "储位不存在，请重新选择！");
                }
            }
            if (grDetailDO.getInspqty().compareTo(grDetailDO.getRecvqty()) > 0) {
                message.append("验收项次：" + grDetailDO.getInspitemno() + "验收数量不能大于收货数量！");
            }
        }
        for (PoDetailDO poDetailDO : poDetailDOs) {
            // 订购明细金额、数量
            BigDecimal qty = poDetailDO.getQty();
            BigDecimal overrecvrate = poDetailDO.getOverrecvrate() != null ? poDetailDO.getOverrecvrate() : new BigDecimal(0);
            BigDecimal overqty = qty.multiply(overrecvrate).setScale(4, RoundingMode.HALF_UP);
            // 已验收金额、数量
            BigDecimal oldTotalqty = BigDecimal.ZERO;
            // 该项次验收单金额、数量
            BigDecimal itemqty = BigDecimal.ZERO;
            String poitemno = poDetailDO.getPoitemno();
            if (confirmList != null && confirmList.size() > 0) {
                for (GrMainDO mainDO : confirmList) {
                    List<GrDetailDO> details = grDetailMapper.selectListByParentId(mainDO.getId());
                    for (GrDetailDO detailDO : details) {
                        if (poitemno.equals(detailDO.getPoitemno())) {
                            oldTotalqty = oldTotalqty.add(detailDO.getInspqty() == null ? new BigDecimal(0) : detailDO.getInspqty());
                        }
                    }
                }
            }

            for (GrDetailDO grDetailDO : grDetailDOs) {
                if (grDetailDO.getInspqty().compareTo(grDetailDO.getRecvqty()) > 0) {
                    message.append("订购项次：" + poitemno + "验收数量大于收货数量:" + qty);
                }
                if (poitemno.equals(grDetailDO.getPoitemno())) {
                    itemqty = itemqty.add(grDetailDO.getInspqty());
                    totalqty = totalqty.add(grDetailDO.getInspqty());
                    totalamt = totalamt.add(grDetailDO.getInspamt());
                }
            }
            if ((qty.add(overqty)).compareTo(itemqty) < 0) {
                message.append("订购项次：" + poitemno + "验收数量不能大于订购数量:" + qty + "加上超收数量：" + overqty);
            }
            if ((qty.add(overqty)).compareTo(itemqty.add(oldTotalqty)) < 0) {
                message.append("订购项次：" + poitemno + "本次验收数量 + 已验收数量不能大于订购数量:" + qty + "加上超收数量：" + overqty);
            }
        }
        if (!StringUtil.isEmpty(message.toString())) {
            throw exception(GR_DETAIL_CHECK_AMT_QTY, message);
        }
        //回写验收主表总金额 数量
        grMainDO.setTotalQty(totalqty);
        grMainDO.setTotalAmt(totalamt);
        grMainDO.setCnyamt(totalamt);
    }

    @Override
    public boolean confirmCheckGrMain(Long id) {
        validateGrMainExists(id);
        StringBuffer message = new StringBuffer("");
        List<GrDetailDO> detailList = grDetailMapper.selectByParentId(id);
//        boolean isGrMainCanConfirm = detailList.stream().allMatch(detail ->
//                detail.getDrawCheck() == 1 && detail.getInstCheck() == 1 && detail.getNameCheck() == 1 &&
//                        detail.getCertCheck() == 1 && detail.getSpecCheck() == 1);
        boolean isGrMainCanConfirm = detailList.stream().allMatch(detail ->
                "01".equals(detail.getDrawPass()) && "01".equals(detail.getInstPass()) && "01".equals(detail.getNamePass())
                        && "01".equals(detail.getCertPass()) && "01".equals(detail.getSpecPass()));

        if (isGrMainCanConfirm) {
            GrMainDO grMainDO = getGrMain(id);
            grMainDO.setCheckstus("1");
            grMainDO.setStus("Y");
            grMainMapper.updateById(grMainDO);
        } else {
            for (GrDetailDO grDetailDO : detailList) {
                StringBuffer msg = new StringBuffer("");
                if (!"01".equals(grDetailDO.getDrawPass())) {
                    msg.append("图纸、");
                }
                if (!"01".equals(grDetailDO.getInstPass())) {
                    msg.append("说明书、");
                }
                if (!"01".equals(grDetailDO.getNamePass())) {
                    msg.append("铭牌、");
                }
                if (!"01".equals(grDetailDO.getCertPass())) {
                    msg.append("合格证、");
                }
                if (!"01".equals(grDetailDO.getSpecPass())) {
                    msg.append("规格型号、");
                }
                if (msg.length() > 0) {
                    message.append("验收项次：" + grDetailDO.getInspitemno() + "，料号：" + grDetailDO.getMatrlno() + "，" + msg.substring(0, msg.length() - 1) + "不合格；");
                }
            }
        }
        if (StringUtils.isNotBlank(message)) {
            throw exception(GR_DETAIL_CHECK_PASS, message.append("请拆分不合格项次或重新检查！"));
        }

        return isGrMainCanConfirm;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInsertGrDetails(GrDetailBatchInsertReqVO reqVO) {
        String receiveno = reqVO.getReceiveno();
        String parentId = reqVO.getParentId();
        GrMainDO grMainDO = grMainMapper.selectById(parentId);
        if (grMainDO == null) {
            throw exception(GR_MAIN_NOT_EXISTS);
        }
        List<GrDetailBatchInsertListReqVO> items = reqVO.getItems();
        DoMainDO doMainDO = doMainMapper.selectOne(DoMainDO::getReceiveno, receiveno);
        if (doMainDO == null) {
            throw exception(DO_MAIN_NOT_EXISTS);
        }
        String serialNo = grDetailMapper.getMaxGrItemNoByParentId(Long.valueOf(parentId));
        String inspitemno = "0000";
        if (serialNo != null) {
            inspitemno = String.format("%04d", Integer.valueOf(serialNo) + 1);
        }
        for (GrDetailBatchInsertListReqVO item : items) {
            DoDetailDO doDetailDO = doDetailMapper.selectOne(DoDetailDO::getReceiveno, receiveno, DoDetailDO::getReceiveitemno, item.getReceiveitemno());
            PoDetailDO poDetailDO = poDetailMapper.selectOne(PoDetailDO::getPono, doDetailDO.getPono(), PoDetailDO::getPoitemno, doDetailDO.getPoitemno());
            if (doDetailDO == null) {
                throw exception(DO_DETAIL_NOT_EXISTS_ITEM_NO, item.getReceiveitemno());
            }
            BigDecimal recvqty = item.getRecvqty() != null ? item.getRecvqty() : new BigDecimal(0);
            if (doDetailDO.getEstrecvqty() != null && recvqty.compareTo(doDetailDO.getEstrecvqty()) > 0) {
                throw exception(DO_DETAIL_RECVQTY_MORE_THAN, item.getReceiveitemno());
            }
            GrDetailDO grDetailDO = new GrDetailDO();
            grDetailDO.setProject(grMainDO.getProject());
            grDetailDO.setCompid(grMainDO.getCompid());
            grDetailDO.setParentId(grMainDO.getId());
            grDetailDO.setInspno(grMainDO.getInspno());
            grDetailDO.setInspitemno(inspitemno);
            grDetailDO.setPono(doDetailDO.getPono());
            grDetailDO.setPoitemno(doDetailDO.getPoitemno());
            grDetailDO.setMatrlno(doDetailDO.getMatrlno());
            grDetailDO.setChnname(item.getMatrlname());
            grDetailDO.setRecvqty(item.getRecvqty());
            grDetailDO.setInspqty(item.getRecvqty());
            grDetailDO.setInspamt(item.getRecvqty().multiply(poDetailDO.getUnitprice()));
            grDetailDO.setMemo(doDetailDO.getMemo());
            grDetailDO.setCreateEmpno(getLoginUserNickname());
            grDetailDO.setCreateDate(DateUtil.getDate());
            grDetailDO.setCreator(getLoginUserId().toString());
            grDetailDO.setUpdateEmpno(getLoginUserNickname());
            grDetailDO.setUpdateDate(DateUtil.getDate());
            grDetailDO.setUpdater(getLoginUserId().toString());
            grDetailMapper.insert(grDetailDO);
            inspitemno = String.format("%04d", Integer.valueOf(inspitemno) + 1);
        }
    }

    @Override
    public void batchSettleInsert(GrDetailSplitSaveReqVO reqVO) {
        Long parentId = reqVO.getParentId();
        GrMainDO grMainDO = grMainMapper.selectById(parentId);
        if (grMainDO == null) {
            throw exception(GR_MAIN_NOT_EXISTS);
        }
        List<GrDetailDO> items = reqVO.getItems();
        String serialNo = grDetailMapper.getMaxGrItemNoByParentId(Long.valueOf(parentId));
        String inspitemno = "0000";
        if (serialNo != null) {
            inspitemno = String.format("%04d", Integer.valueOf(serialNo) + 1);
        }
        for (GrDetailDO item : items) {
            GrDetailDO grDetailDO = BeanUtils.toBean(item, GrDetailDO.class);
            grDetailDO.setId(null);
            grDetailDO.setProject(grMainDO.getProject());
            grDetailDO.setCompid(grMainDO.getCompid());
            grDetailDO.setParentId(grMainDO.getId());
            grDetailDO.setInspno(grMainDO.getInspno());
            grDetailDO.setInspitemno(inspitemno);
            grDetailDO.setCreateEmpno(getLoginUserNickname());
            grDetailDO.setCreateDate(DateUtil.getDate());
            grDetailDO.setCreator(getLoginUserId().toString());
            grDetailDO.setUpdateEmpno(getLoginUserNickname());
            grDetailDO.setUpdateDate(DateUtil.getDate());
            grDetailDO.setUpdater(getLoginUserId().toString());
            grDetailMapper.insert(grDetailDO);
            inspitemno = String.format("%04d", Integer.valueOf(inspitemno) + 1);
        }
    }

    @Override
    public Long createSettlementDetail(SettlementDetailDO settlementDetailDO) {
        settlementDetailMapper.insert(settlementDetailDO);
        return settlementDetailDO.getId();
    }

    @Override
    public void updateSettlementDetail(SettlementDetailDO settlementDetailDO) {
        validateSettlementDetailExists(settlementDetailDO.getId());
        settlementDetailMapper.updateById(settlementDetailDO);
    }

    @Override
    public void deleteSettlementDetail(Long id) {
        validateSettlementDetailExists(id);
        settlementDetailMapper.deleteById(id);
    }

    @Override
    public SettlementDetailDO getSettlementDetail(Long id) {
        return settlementDetailMapper.selectById(id);
    }

    private void validateSettlementDetailExists(Long id) {
        if (settlementDetailMapper.selectById(id) == null) {
            throw exception(SETTLEMENT_DETAIL_NOT_EXISTS);
        }
    }

    @Override
    public PageResult<SettlementDetailDO> getSettlementDetailPage(PageParam pageReqVO, Long parentId) {
        return settlementDetailMapper.selectPage(pageReqVO, parentId);
    }

    @Override
    public void downloadSettlementDetail(Long parentId) {
        GrMainDO grMainDO = grMainMapper.selectById(parentId);
        List<GrDetailDO> grDetailList = grDetailMapper.selectByParentId(parentId);
        if (grDetailList != null && grDetailList.size() > 0) {
            for (GrDetailDO grDetailDO : grDetailList) {
                WrsTradeDetail2DO detail2DO = wrsTradeDetail2Mapper.selectWrsTradeDetail2(
                        grDetailDO.getMatrlno(),
                        grDetailDO.getProjectno(),
                        grDetailDO.getPuritemno());
                if (detail2DO != null) {
                    SettlementDetailDO oldSettlementDetail = settlementDetailMapper.selectOne(
                            SettlementDetailDO::getIssueTallyNo, detail2DO.getIssueTallyNo(),
                            SettlementDetailDO::getMatrlNo, grDetailDO.getMatrlno(),
                            SettlementDetailDO::getPoNo, grDetailDO.getProjectno(),
                            SettlementDetailDO::getSeqNo, grDetailDO.getPuritemno());
                    if (oldSettlementDetail == null) {
                        oldSettlementDetail = new SettlementDetailDO();
                        oldSettlementDetail.setCompId(grMainDO.getCompid())
                                .setParentId(parentId)
                                .setInspNo(grMainDO.getInspno())
                                .setIssueTallyNo(detail2DO.getIssueTallyNo())
                                .setMatrlNo(detail2DO.getMatrlNo())
                                .setPoNo(detail2DO.getContractNo())
                                .setSeqNo(detail2DO.getSeqNo())
                                .setCarNo(detail2DO.getCarNo())
                                .setSettleDate(detail2DO.getSettleDate())
                                .setChkNo(detail2DO.getChkNo())
                                .setAcptNum(grDetailDO.getInspqty())
                                .setTempFee(detail2DO.getTransAmt())
                                .setActualFee(grDetailDO.getTotalAmt())
                                .setDiffFee(grDetailDO.getTotalAmt().subtract(detail2DO.getTransAmt()));
                    } else {
                        oldSettlementDetail
                                .setCarNo(detail2DO.getCarNo())
                                .setSettleDate(detail2DO.getSettleDate())
                                .setChkNo(detail2DO.getChkNo())
                                .setAcptNum(grDetailDO.getInspqty())
                                .setTempFee(detail2DO.getTransAmt())
                                .setActualFee(grDetailDO.getTotalAmt())
                                .setDiffFee(grDetailDO.getTotalAmt().subtract(detail2DO.getTransAmt()));
                    }
                    settlementDetailMapper.insertOrUpdate(oldSettlementDetail);
                }
            }
        }
    }

    @Override
    public List<SettlementDetailDO> getTotalList(Long parentId) {
        return settlementDetailMapper.selectTotalList(parentId);
    }
}

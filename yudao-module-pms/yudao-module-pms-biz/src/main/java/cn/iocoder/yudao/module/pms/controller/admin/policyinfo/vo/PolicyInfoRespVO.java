package cn.iocoder.yudao.module.pms.controller.admin.policyinfo.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 保单基本信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PolicyInfoRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "父id")
    @ExcelProperty("父id")
    private Long parentId;

    @Schema(description = "申请单号")
    @ExcelProperty("申请单号")
    private String poNo;

    @Schema(description = "发票号")
    @ExcelProperty("发票号")
    private String voucherNo;

    @Schema(description = "信用证号")
    @ExcelProperty("信用证号")
    private String lcNo;

    @Schema(description = "发票金额")
    @ExcelProperty("发票金额")
    private BigDecimal voucherAmount;

    @Schema(description = "投保加成")
    @ExcelProperty("投保加成")
    private BigDecimal plus;

    @Schema(description = "货物类型")
    @ExcelProperty("货物类型")
    private String goodsCategory;

    @Schema(description = "湿重数量")
    @ExcelProperty("湿重数量")
    private BigDecimal quantity;

    @Schema(description = "品名")
    @ExcelProperty("品名")
    private String matrlName;

    @Schema(description = "保险金额")
    @ExcelProperty("保险金额")
    private BigDecimal policyAmt;

    @Schema(description = "币别")
    @ExcelProperty("币别")
    private String crcy;

    @Schema(description = "发港日期")
    @ExcelProperty("发港日期")
    private String actlArivDate;

    @Schema(description = "发港日期（英文）")
    @ExcelProperty("发港日期（英文）")
    private String actlArivDateEn;

    @Schema(description = "船名")
    @ExcelProperty("船名")
    private String shipName;

    @Schema(description = "船名（英文）")
    @ExcelProperty("船名（英文）")
    private String shipNameEn;

    @Schema(description = "发港名称")
    @ExcelProperty("发港名称")
    private String loadPort;

    @Schema(description = "发港名称（英文）")
    @ExcelProperty("发港名称（英文）")
    private String loadPortEn;

    @Schema(description = "中转港名称")
    @ExcelProperty("中转港名称")
    private String viaPort;

    @Schema(description = "中转港名称（英文）")
    @ExcelProperty("中转港名称（英文）")
    private String viaPortEn;

    @Schema(description = "卸港名称")
    @ExcelProperty("卸港名称")
    private String relsPort;

    @Schema(description = "卸港名称（英文）")
    @ExcelProperty("卸港名称（英文）")
    private String relsPortEn;

    @Schema(description = "提单号")
    @ExcelProperty("提单号")
    private String blNo;

    @Schema(description = "赔款偿付地点")
    @ExcelProperty("赔款偿付地点")
    private String claimPayableAt;

    @Schema(description = "赔款偿付地点（英文）")
    @ExcelProperty("赔款偿付地点（英文）")
    private String claimPayableAtEn;

    @Schema(description = "投保险别")
    @ExcelProperty("投保险别")
    private String insuranceConditions;

    @Schema(description = "集装箱种类")
    @ExcelProperty("集装箱种类")
    private String container;

    @Schema(description = "转运工具")
    @ExcelProperty("转运工具")
    private String transit;

    @Schema(description = "船籍")
    @ExcelProperty("船籍")
    private String shipRegistry;

    @Schema(description = "船龄")
    @ExcelProperty("船龄")
    private String shipAge;

    @Schema(description = "合同创建人")
    @ExcelProperty("合同创建人")
    private String poCreateEmp;

    @Schema(description = "投保日期")
    @ExcelProperty("投保日期")
    private String policyDate;

    @Schema(description = "电话")
    @ExcelProperty("电话")
    private String tel;

    @Schema(description = "地址")
    @ExcelProperty("地址")
    private String address;

    @Schema(description = "费率")
    @ExcelProperty("费率")
    private BigDecimal rate;

    @Schema(description = "保费")
    @ExcelProperty("保费")
    private BigDecimal premium;

    @Schema(description = "备注")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "经办人")
    @ExcelProperty("经办人")
    private String operator;

    @Schema(description = "核保人")
    @ExcelProperty("核保人")
    private String underwriter;

    @Schema(description = "负责人")
    @ExcelProperty("负责人")
    private String manager;

    @Schema(description = "新增时间")
    @ExcelProperty("新增时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    @ExcelProperty("修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建者")
    @ExcelProperty("创建者")
    private String creator;

    @Schema(description = "更新者")
    @ExcelProperty("更新者")
    private String updater;

    @Schema(description = "创建人姓名")
    @ExcelProperty("创建人姓名")
    private String createEmpNo;

    @Schema(description = "更新人姓名")
    @ExcelProperty("更新人姓名")
    private String updateEmpNo;

}
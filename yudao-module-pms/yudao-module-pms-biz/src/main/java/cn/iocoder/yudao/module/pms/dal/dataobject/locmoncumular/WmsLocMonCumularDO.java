package cn.iocoder.yudao.module.pms.dal.dataobject.locmoncumular;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 储位料号月累计收发 DO
 *
 * <AUTHOR>
 */
@TableName("wms_loc_mon_cumular")
@KeySequence("wms_loc_mon_cumular_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WmsLocMonCumularDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 项目号
     */
    private String project;
    /**
     * 公司别
     */
    private String compId;
    /**
     * 库存年月
     */
    private String yearMo;
    /**
     * 料号
     */
    private String matrlNo;
    /**
     * 储位
     */
    private String locNo;
    /**
     * 批号
     */
    private String lotNo;
    /**
     * 品别
     */
    private String inventoryType;

    /**
     * 期末库存量
     */
    private BigDecimal endQty;
    /**
     * 期末库存金额
     */
    private BigDecimal endAmt;
    /**
     * 湿重期末库存量
     */
    private BigDecimal endWetQty;
    /**
     * 湿重期末库存金额
     */
    private BigDecimal endWetAmt;
    /**
     * 备注
     */
    private String remark;
    /**
     * 品级(备件属性)
     */
    private String matrlGrade;
    /**
     * 月初库存量
     */
    private BigDecimal moBeginQty;
    /**
     * 月初库存金额
     */
    private BigDecimal moBeginAmt;
    /**
     * 月累积入库量
     */
    private BigDecimal moAccuRecvQty;
    /**
     * 月累积入库金额
     */
    private BigDecimal moAccuRecvAmt;
    /**
     * 月累积出库量
     */
    private BigDecimal moAccuIssuQty;
    /**
     * 月累积出库金额
     */
    private BigDecimal moAccuIssuAmt;
    /**
     * 月调整入库量
     */
    private BigDecimal moAccuAdjRecvQty;
    /**
     * 月调整入库金额
     */
    private BigDecimal moAccuAdjRecvAmt;
    /**
     * 月调整出库量
     */
    private BigDecimal moAccuAdjIssuQty;
    /**
     * 月调整出库金额
     */
    private BigDecimal moAccuAdjIssuAmt;
    /**
     * 年初库存量
     */
    private BigDecimal yrBeginQty;
    /**
     * 年初库存金额
     */
    private BigDecimal yrBeginAmt;
    /**
     * 年累积入库量
     */
    private BigDecimal yrAccuRecvQty;
    /**
     * 年累积入库金额
     */
    private BigDecimal yrAccuRecvAmt;
    /**
     * 年累积出库量
     */
    private BigDecimal yrAccuIssuQty;
    /**
     * 年累积出库金额
     */
    private BigDecimal yrAccuIssuAmt;
    /**
     * 年调整入库量
     */
    private BigDecimal yrAccuAdjRecvQty;
    /**
     * 年调整入库金额
     */
    private BigDecimal yrAccuAdjRecvAmt;
    /**
     * 年调整出库量
     */
    private BigDecimal yrAccuAdjIssuQty;
    /**
     * 年调整出库金额
     */
    private BigDecimal yrAccuAdjIssuAmt;
    /**
     * 备用字段1
     */
    private String fields1;
    /**
     * 备用字段2
     */
    private String fields2;
    /**
     * 备用字段3
     */
    private String fields3;
    /**
     * 备用字段4
     */
    private String fields4;
    /**
     * 备用字段5
     */
    private String fields5;
    /**
     * 备用字段6
     */
    private Integer fields6;
    /**
     * 备用字段7
     */
    private Integer fields7;
    /**
     * 备用字段8
     */
    private Integer fields8;
    /**
     * 备用字段9
     */
    private LocalDate fields9;
    /**
     * 备用字段10
     */
    private LocalDate fields10;
    /**
     * 备用字段11
     */
    private LocalDateTime fields11;
    /**
     * 备用字段12
     */
    private LocalDateTime fields12;
    /**
     * 备用字段13
     */
    private BigDecimal fields13;
    /**
     * 备用字段14
     */
    private BigDecimal fields14;
    /**
     * 备用字段15
     */
    private BigDecimal fields15;
    /**
     * 月初库存湿重量
     */
    private BigDecimal moBeginWetQty;
    /**
     * 月累积入库湿重量
     */
    private BigDecimal moAccuRecvWetQty;
    /**
     * 月累积出库湿重量
     */
    private BigDecimal moAccuIssuWetQty;
    /**
     * 月调整入库湿重量
     */
    private BigDecimal moAccuAdjRecvWetQty;
    /**
     * 月调整出库湿重量
     */
    private BigDecimal moAccuAdjIssuWetQty;
    /**
     * 年初库存湿重量
     */
    private BigDecimal yrBeginWetQty;
    /**
     * 年累积入库湿重量
     */
    private BigDecimal yrAccuRecvWetQty;
    /**
     * 年累积出库湿重量
     */
    private BigDecimal yrAccuIssuWetQty;
    /**
     * 年调整入库湿重量
     */
    private BigDecimal yrAccuAdjRecvWetQty;
    /**
     * 年调整出库湿重量
     */
    private BigDecimal yrAccuAdjIssuWetQty;
    /**
     * 月初库存湿重金额
     */
    private BigDecimal moBeginWetAmt;
    /**
     * 月累积入库湿重金额
     */
    private BigDecimal moAccuRecvWetAmt;
    /**
     * 月累积出库湿重金额
     */
    private BigDecimal moAccuIssuWetAmt;
    /**
     * 月调整入库湿重金额
     */
    private BigDecimal moAccuAdjRecvWetAmt;
    /**
     * 月调整出库湿重金额
     */
    private BigDecimal moAccuAdjIssuWetAmt;
    /**
     * 年初库存湿重金额
     */
    private BigDecimal yrBeginWetAmt;
    /**
     * 年累积入库湿重金额
     */
    private BigDecimal yrAccuRecvWetAmt;
    /**
     * 年累积出库湿重金额
     */
    private BigDecimal yrAccuIssuWetAmt;
    /**
     * 年调整入库湿重金额
     */
    private BigDecimal yrAccuAdjRecvWetAmt;
    /**
     * 年调整出库湿重金额
     */
    private BigDecimal yrAccuAdjIssuWetAmt;

}
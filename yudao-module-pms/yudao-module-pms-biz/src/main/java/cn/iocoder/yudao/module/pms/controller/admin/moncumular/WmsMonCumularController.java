package cn.iocoder.yudao.module.pms.controller.admin.moncumular;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.pms.controller.admin.moncumular.vo.WmsMonCumularPageReqVO;
import cn.iocoder.yudao.module.pms.controller.admin.moncumular.vo.WmsMonCumularRespVO;
import cn.iocoder.yudao.module.pms.controller.admin.moncumular.vo.WmsMonCumularSaveReqVO;
import cn.iocoder.yudao.module.pms.dal.dataobject.moncumular.WmsMonCumularDO;
import cn.iocoder.yudao.module.pms.service.moncumular.WmsMonCumularService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 料号月累计收发")
@RestController
@RequestMapping("/pms/wms-mon-cumular")
@Validated
public class WmsMonCumularController {

    @Resource
    private WmsMonCumularService wmsMonCumularService;

    @PostMapping("/create")
    @Operation(summary = "创建料号月累计收发")
    @PreAuthorize("@ss.hasPermission('pms:wms-mon-cumular:create')")
    public CommonResult<Long> createWmsMonCumular(@Valid @RequestBody WmsMonCumularSaveReqVO createReqVO) {
        return success(wmsMonCumularService.createWmsMonCumular(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新料号月累计收发")
    @PreAuthorize("@ss.hasPermission('pms:wms-mon-cumular:update')")
    public CommonResult<Boolean> updateWmsMonCumular(@Valid @RequestBody WmsMonCumularSaveReqVO updateReqVO) {
        wmsMonCumularService.updateWmsMonCumular(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除料号月累计收发")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:wms-mon-cumular:delete')")
    public CommonResult<Boolean> deleteWmsMonCumular(@RequestParam("id") Long id) {
        wmsMonCumularService.deleteWmsMonCumular(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得料号月累计收发")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:wms-mon-cumular:query')")
    public CommonResult<WmsMonCumularRespVO> getWmsMonCumular(@RequestParam("id") Long id) {
        WmsMonCumularDO wmsMonCumular = wmsMonCumularService.getWmsMonCumular(id);
        return success(BeanUtils.toBean(wmsMonCumular, WmsMonCumularRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得料号月累计收发分页")
    @PreAuthorize("@ss.hasPermission('pms:wms-mon-cumular:query')")
    public CommonResult<PageResult<WmsMonCumularRespVO>> getWmsMonCumularPage(@Valid WmsMonCumularPageReqVO pageReqVO) {
        PageResult<WmsMonCumularRespVO> pageResult = wmsMonCumularService.getWmsMonCumularPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, WmsMonCumularRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出料号月累计收发 Excel")
    @PreAuthorize("@ss.hasPermission('pms:wms-mon-cumular:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportWmsMonCumularExcel(@Valid WmsMonCumularPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<WmsMonCumularRespVO> list = wmsMonCumularService.getWmsMonCumularPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "料号月累计收发.xls", "数据", WmsMonCumularRespVO.class,
                        BeanUtils.toBean(list, WmsMonCumularRespVO.class));
    }

}
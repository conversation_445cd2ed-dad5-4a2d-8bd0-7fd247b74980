package cn.iocoder.yudao.module.pms.controller.admin.moncumular.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 料号月累计收发分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WmsMonCumularPageReqVO extends PageParam {

    @Schema(description = "主键", example = "10060")
    private Long id;

    @Schema(description = "公司别")
    private String compid;

    @Schema(description = "项目号")
    private String project;

    @Schema(description = "料号")
    private String matrlno;

    @Schema(description = "料号")
    private String matrlname;

    @Schema(description = "批号")
    private String lotno;

    @Schema(description = "月累计收发")
    private String yearmo;

    @Schema(description = "品级（备件属性）")
    private String matrlgrade;

    @Schema(description = "品别", example = "1")
    private String inventorytype;

    @Schema(description = "会计科目")
    private String acctcode;

    @Schema(description = "是否贸易")
    private String ismy;

}
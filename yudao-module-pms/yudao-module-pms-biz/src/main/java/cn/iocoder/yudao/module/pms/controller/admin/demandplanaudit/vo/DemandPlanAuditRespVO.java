package cn.iocoder.yudao.module.pms.controller.admin.demandplanaudit.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 需求计划申报工作流关联 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DemandPlanAuditRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10698")
    @ExcelProperty("ID")
    private Long id;

    @Schema(description = "需求计划申报表 ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2388")
    @ExcelProperty("需求计划申报表 ID")
    private Long parentId;

    @Schema(description = "计划行号", example = "15909")
    @ExcelProperty("计划行号")
    private String mrLineId;

    @Schema(description = "作业部计划员ID", example = "7665")
    @ExcelProperty("作业部计划员ID")
    private String applyUserId;

    @Schema(description = "作业部计划员姓名", example = "赵六")
    @ExcelProperty("作业部计划员姓名")
    private String applyUserName;

    @Schema(description = "流程实例的编号", example = "2055")
    @ExcelProperty("流程实例的编号")
    private String processInstanceId;

    @Schema(description = "新增时间")
    @ExcelProperty("新增时间")
    private LocalDateTime createTime;

}
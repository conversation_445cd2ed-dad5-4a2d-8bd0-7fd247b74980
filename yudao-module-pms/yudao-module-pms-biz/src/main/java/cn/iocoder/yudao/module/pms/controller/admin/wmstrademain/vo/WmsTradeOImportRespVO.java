package cn.iocoder.yudao.module.pms.controller.admin.wmstrademain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Schema(description = "管理后台 - 期初开账清单响应数据 Response VO")
@Data
@Builder
public class WmsTradeOImportRespVO {

    @Schema(description = "创建成功的序号", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> createSerialNoList;

    @Schema(description = "更新成功的序号", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> updateSerialNoList;

    @Schema(description = "导入失败的序号，key 为序号，value 为失败原因", requiredMode = Schema.RequiredMode.REQUIRED)
    private Map<String, String> failSerialNoList;

}

package cn.iocoder.yudao.module.pms.controller.admin.mineralprices.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 进口矿价格新增/修改 Request VO")
@Data
public class MineralPricesSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2326")
    private Long id;

    @Schema(description = "品种", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "品种不能为空")
    private String variety;

    @Schema(description = "货款计算基础指数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "货款计算基础指数不能为空")
    private List<String> paymentBaseIndexs;
    private String paymentBaseIndex;

    @Schema(description = "运费计算基础指数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "运费计算基础指数不能为空")
    private List<String> freightBaseIndexs;
    private String freightBaseIndex;

    @Schema(description = "指数月(YYYY-MM-01格式)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "指数月不能为空")
    @DateTimeFormat(pattern = "YYYY-MM-dd")
    private LocalDate indexMonth;

    @Schema(description = "系统计算DMTU")
    private BigDecimal systemCalculatedDmtu;

    @Schema(description = "市场公布DMTU")
    private BigDecimal marketPublishedDmtu;

    @Schema(description = "典型值1")
    private BigDecimal typical1;

    @Schema(description = "典型值2")
    private BigDecimal typical2;

    @Schema(description = "典型值3")
    private BigDecimal typical3;

    @Schema(description = "典型值4")
    private BigDecimal typical4;

    @Schema(description = "典型值5")
    private BigDecimal typical5;

    @Schema(description = "典型值6")
    private BigDecimal typical6;

    @Schema(description = "典型值7")
    private BigDecimal typical7;

    @Schema(description = "典型值8")
    private BigDecimal typical8;

    @Schema(description = "典型值9")
    private BigDecimal typical9;

    @Schema(description = "典型值10")
    private BigDecimal typical10;

    @Schema(description = "折扣方式")
    private String discountMethod;

    @Schema(description = "折扣/溢价")
    private BigDecimal discountPremium;

    @Schema(description = "DMTU计算公式")
    @NotEmpty(message = "DMTU计算公式不能为空")
    private String dmtuFormula;

    @Schema(description = "货值计算公式")
    @NotEmpty(message = "货值计算公式不能为空")
    private String cargoFormula;

    @Schema(description = "运费计算公式")
    @NotEmpty(message = "运费计算公式不能为空")
    private String freightFormula;

    @Schema(description = "折扣计算公式")
    @NotEmpty(message = "折扣计算公式不能为空")
    private String discountFormula;

    @Schema(description = "备用字段1")
    private String backup1;

    @Schema(description = "备用字段2")
    private String backup2;

    @Schema(description = "备用字段3")
    private String backup3;

    @Schema(description = "备用字段4")
    private String backup4;

    @Schema(description = "备用字段5")
    private String backup5;

    @Schema(description = "备用字段6")
    private String backup6;

    @Schema(description = "备用字段7")
    private String backup7;

    @Schema(description = "备用字段8")
    private String backup8;

    @Schema(description = "备用字段9")
    private String backup9;

    @Schema(description = "备用字段10")
    private String backup10;

    private BigDecimal qty;

    private LocalDate[] recordDate;

}
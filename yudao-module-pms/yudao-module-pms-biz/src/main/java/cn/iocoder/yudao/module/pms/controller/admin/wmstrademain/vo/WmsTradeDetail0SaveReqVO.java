package cn.iocoder.yudao.module.pms.controller.admin.wmstrademain.vo;

import cn.iocoder.yudao.module.pms.dal.dataobject.wmstrademain.WmsTradeDetail0DO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

@Schema(description = "管理后台 - 交货单明细新增 Request VO")
@Data
public class WmsTradeDetail0SaveReqVO {

    @Schema(description = "")
    private Long parentId;

    @Schema(description = "")
    private List<WmsTradeDetail0DO> items;

}
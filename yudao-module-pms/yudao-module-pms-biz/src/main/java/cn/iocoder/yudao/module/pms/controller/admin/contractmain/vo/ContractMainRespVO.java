package cn.iocoder.yudao.module.pms.controller.admin.contractmain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 法务合同基本信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ContractMainRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "30051")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "合同名称", example = "赵六")
    @ExcelProperty("合同名称")
    private String contractName;

    @Schema(description = "业务系统合同编码")
    @ExcelProperty("业务系统合同编码")
    private String thirdContractNo;

    @Schema(description = "业务系统版本号")
    @ExcelProperty("业务系统版本号")
    private String thirdVersion;

    @Schema(description = "系统来源")
    @ExcelProperty("系统来源")
    private String dataSource;

    @Schema(description = "系统来源编码")
    @ExcelProperty("系统来源编码")
    private String dataSourceCode;

    @Schema(description = "法务系统合同唯一标识", example = "7481")
    @ExcelProperty("法务系统合同唯一标识")
    private String contractId;

    @Schema(description = "法务系统合同唯一标识名称", example = "芋艿")
    @ExcelProperty("法务系统合同唯一标识名称")
    private String contractIdName;

    @Schema(description = "合同性质编码")
    @ExcelProperty("合同性质编码")
    private String dataTypeCode;

    @Schema(description = "合同分类编码")
    @ExcelProperty("合同分类编码")
    private String contractTypeCode;

    @Schema(description = "经办人", example = "13753")
    @ExcelProperty("经办人")
    private String orgId;

    @Schema(description = "立项决策编码", example = "4770")
    @ExcelProperty("立项决策编码")
    private String projectDecisionId;

    @Schema(description = "相对方确定方式编码")
    @ExcelProperty("相对方确定方式编码")
    private String relativeMethodCode;

    @Schema(description = "授权类型编码")
    @ExcelProperty("授权类型编码")
    private String authorizedSource;

    @Schema(description = "授权名称", example = "赵六")
    @ExcelProperty("授权名称")
    private String authorizedName;

    @Schema(description = "授权编码", example = "17288")
    @ExcelProperty("授权编码")
    private String authorizedId;

    @Schema(description = "是否集团重大合同编码(0-否,1-是)")
    @ExcelProperty("是否集团重大合同编码(0-否,1-是)")
    private String whetherGroupMajor;

    @Schema(description = "是否本单位重大合同编码(0-否,1-是)")
    @ExcelProperty("是否本单位重大合同编码(0-否,1-是)")
    private String whetherUnitMajor;

    @Schema(description = "金额类型编码")
    @ExcelProperty("金额类型编码")
    private String moneyTypeCode;

    @Schema(description = "是否含税(0-否,1-是)")
    @ExcelProperty("是否含税(0-否,1-是)")
    private String isTax;

    @Schema(description = "结算方式编码")
    @ExcelProperty("结算方式编码")
    private String settlementMethodCode;

    @Schema(description = "收支方向编码")
    @ExcelProperty("收支方向编码")
    private String revenueExpenditureCode;

    @Schema(description = "合同约定开始日期(YYYY-MM-DD HH:MM:SS)")
    @ExcelProperty("合同约定开始日期(YYYY-MM-DD HH:MM:SS)")
//    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime agreedStartTime;

    @Schema(description = "合同约定结束日期(YYYY-MM-DD HH:MM:SS)")
    @ExcelProperty("合同约定结束日期(YYYY-MM-DD HH:MM:SS)")
//    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime agreedEndTime;

    @Schema(description = "我方地位")
    @ExcelProperty("我方地位")
    private String ourPosition;

    @Schema(description = "我方签约主体", example = "张三")
    @ExcelProperty("我方签约主体")
    private String ourPartyName;

    @Schema(description = "我方签约主体编码")
    @ExcelProperty("我方签约主体编码")
    private String ourPartyList;

    @Schema(description = "对方签约主体", example = "芋艿")
    @ExcelProperty("对方签约主体")
    private String otherPartyName;

    @Schema(description = "对方签约主体编码")
    @ExcelProperty("对方签约主体编码")
    private String otherPartyList;

    @Schema(description = "是否范本(0-否,1-是)")
    @ExcelProperty("是否范本(0-否,1-是)")
    private String isItAtemplate;

    @Schema(description = "合同金额")
    @ExcelProperty("合同金额")
    private BigDecimal contractMoney;

    @Schema(description = "增值税率编码(可多选逗号隔开)")
    @ExcelProperty("增值税率编码(可多选逗号隔开)")
    private String valueAddedTaxRateCode;

    @Schema(description = "增值税率(可多选逗号隔开)")
    @ExcelProperty("增值税率(可多选逗号隔开)")
    private String valueAddedTaxRate;

    @Schema(description = "增值税额(元)")
    @ExcelProperty("增值税额(元)")
    private BigDecimal valueAddedTaxAmount;

    @Schema(description = "币种名称(主数据分发)")
    @ExcelProperty("币种名称(主数据分发)")
    private String currency;

    @Schema(description = "币种代码(主数据分发)")
    @ExcelProperty("币种代码(主数据分发)")
    private String currencyCode;

    @Schema(description = "汇率方式编码(见附表)")
    @ExcelProperty("汇率方式编码(见附表)")
    private String exchangeRateMethodCode;

    @Schema(description = "汇率")
    @ExcelProperty("汇率")
    private BigDecimal exchangeRate;

    @Schema(description = "原合同编号")
    @ExcelProperty("原合同编号")
    private String originalContractCode;

    @Schema(description = "金额变更类型编码(见附表)")
    @ExcelProperty("金额变更类型编码(见附表)")
    private String changeMoneyTypeCode;

    @Schema(description = "本次变更合同金额")
    @ExcelProperty("本次变更合同金额")
    private BigDecimal thisChangeMoney;

    @Schema(description = "本次变更增值税金额")
    @ExcelProperty("本次变更增值税金额")
    private BigDecimal thisChangeValueAddedTaxAmount;

    @Schema(description = "预留扩展字段1")
    @ExcelProperty("预留扩展字段1")
    private String extendedFiled1;

    @Schema(description = "预留扩展字段2")
    @ExcelProperty("预留扩展字段2")
    private String extendedFiled2;

    @Schema(description = "预留扩展字段3")
    @ExcelProperty("预留扩展字段3")
    private String extendedFiled3;

    @Schema(description = "预留扩展字段4")
    @ExcelProperty("预留扩展字段4")
    private String extendedFiled4;

    @Schema(description = "预留扩展字段5")
    @ExcelProperty("预留扩展字段5")
    private String extendedFiled5;

    @Schema(description = "预留扩展字段6")
    @ExcelProperty("预留扩展字段6")
    private String extendedFiled6;

    @Schema(description = "预留扩展字段7")
    @ExcelProperty("预留扩展字段7")
    private String extendedFiled7;

    @Schema(description = "预留扩展字段8")
    @ExcelProperty("预留扩展字段8")
    private String extendedFiled8;

    @Schema(description = "预留扩展字段9")
    @ExcelProperty("预留扩展字段9")
    private String extendedFiled9;

    @Schema(description = "预留扩展字段10")
    @ExcelProperty("预留扩展字段10")
    private String extendedFiled10;

    @Schema(description = "预留扩展字段11")
    @ExcelProperty("预留扩展字段11")
    private String extendedFiled11;

    @Schema(description = "预留扩展字段12")
    @ExcelProperty("预留扩展字段12")
    private String extendedFiled12;

    @Schema(description = "预留扩展字段13")
    @ExcelProperty("预留扩展字段13")
    private String extendedFiled13;

    @Schema(description = "预留扩展字段14")
    @ExcelProperty("预留扩展字段14")
    private String extendedFiled14;

    @Schema(description = "预留扩展字段15")
    @ExcelProperty("预留扩展字段15")
    private String extendedFiled15;

    @Schema(description = "预留扩展字段16")
    @ExcelProperty("预留扩展字段16")
    private String extendedFiled16;

    @Schema(description = "预留扩展字段17")
    @ExcelProperty("预留扩展字段17")
    private String extendedFiled17;

    @Schema(description = "预留扩展字段18")
    @ExcelProperty("预留扩展字段18")
    private String extendedFiled18;

    @Schema(description = "预留扩展字段19")
    @ExcelProperty("预留扩展字段19")
    private String extendedFiled19;

    @Schema(description = "预留扩展字段20")
    @ExcelProperty("预留扩展字段20")
    private String extendedFiled20;

    @Schema(description = "附件地址", example = "https://www.iocoder.cn")
    @ExcelProperty("附件地址")
    private String pdfUrl;

    @Schema(description = "初始版本合同ID")
    @ExcelProperty("初始版本合同ID")
    private String poidfirst;

    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
//    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

    //生效状态编码	takeEffectCode
    private String takeEffectCode;
    //生效时间（日期格式：yyyy-MM-dd HH:mm:ss）	contractTakeEffectDate
    private LocalDateTime contractTakeEffectDate;
    //对方盖章时间（日期格式：yyyy-MM-dd HH:mm:ss）	otherSealDate
    private LocalDateTime otherSealDate;
    //合同履行状态编码	performStateCode
    private String performStateCode;

}

package cn.iocoder.yudao.module.pms.controller.admin.stocklocdetail.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 实时库存明细分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WmsStockLocDetailPageReqVO extends PageParam {


    @Schema(description = "库存id")
    private Long id;

    @Schema(description = "公司别")
    private String compid;

    @Schema(description = "项目号")
    private String project;

    @Schema(description = "料号")
    private String matrlno;

    @Schema(description = "品级（备件属性）")
    private String matrlgrade;

    @Schema(description = "品别", example = "2")
    private String inventorytype;

    @Schema(description = "储位")
    private String locno;

    @Schema(description = "批号")
    private String lotno;

    @Schema(description = "批次")
    private String batch;

    @Schema(description = "计划单号")
    private String plantallyno;

    @Schema(description = "厂商编号")
    private String supplierno;

    @Schema(description = "合同编号")
    private String contractno;

    @Schema(description = "仓库")
    private String storageno;

    @Schema(description = "品名")
    private String matrlname;
}
package cn.iocoder.yudao.module.pms.service.domain;

import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.yudao.framework.common.util.date.DateUtil;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.pms.api.pomain.dto.DoDetailDTO;
import cn.iocoder.yudao.module.pms.api.pomain.dto.DoMainRespDTO;
import cn.iocoder.yudao.module.pms.api.pomain.dto.DoMainSaveReqDTO;
import cn.iocoder.yudao.module.pms.api.pomain.dto.PoMainReqDTO;
import cn.iocoder.yudao.module.pms.controller.admin.grmain.vo.GrMainSaveReqVO;
import cn.iocoder.yudao.module.pms.controller.app.domain.vo.AppGrDoShSaveVO;
import cn.iocoder.yudao.module.pms.dal.dataobject.grmain.GrDetailDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.pomain.PoDetailDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.pomain.PoMainDO;
import cn.iocoder.yudao.module.pms.dal.mysql.pomain.PoDetailMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.pomain.PoMainMapper;
import cn.iocoder.yudao.module.pms.dal.redis.no.PmsNoRedisDAO;
import cn.iocoder.yudao.module.pms.service.grmain.GrMainService;
import cn.iocoder.yudao.module.system.api.dept.DeptApi;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;
import cn.iocoder.yudao.module.pms.controller.admin.domain.vo.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.domain.DoMainDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.domain.DoDetailDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.pms.dal.mysql.domain.DoMainMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.domain.DoDetailMapper;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.*;
import static cn.iocoder.yudao.module.pms.enums.ErrorCodeConstants.*;

/**
 * 交货单主档 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DoMainServiceImpl implements DoMainService {

    @Resource
    private DoMainMapper doMainMapper;
    @Resource
    private DoDetailMapper doDetailMapper;
    @Resource
    private DeptApi deptApi;
    @Resource
    private PmsNoRedisDAO noRedisDAO;
    @Resource
    private PoMainMapper poMainMapper;
    @Resource
    private GrMainService grMainService;
    @Resource
    private PoDetailMapper poDetailMapper;

    @Override
    public Long createDoMain(DoMainSaveReqVO createReqVO) {
        // 生成交货单号 校验唯一
        String receiveno = noRedisDAO.generate(PmsNoRedisDAO.DO_MAIN_NO_PREFIX);
        if (doMainMapper.selectByReceiveno(receiveno) != null) {
            throw exception(DO_NO_EXISTS);
        }

        DoMainDO mainDO = BeanUtils.toBean(createReqVO, DoMainDO.class);
        mainDO.setProject(getLoginUserTopDeptId().toString());
        mainDO.setCompid(getLoginUserTenantId().toString());
        mainDO.setReceiveno(receiveno);
        mainDO.setStus("N");
        mainDO.setFromsys("A");
        mainDO.setCreateEmpno(getLoginUserNickname());
        mainDO.setCreateDate(DateUtil.getDate());
        mainDO.setCreator(getLoginUserId().toString());
        mainDO.setUpdateEmpno(getLoginUserNickname());
        mainDO.setUpdateDate(DateUtil.getDate());
        mainDO.setUpdater(getLoginUserId().toString());
        doMainMapper.insert(mainDO);
        // 返回
        return mainDO.getId();
    }

    @Override
    public void updateDoMain(DoMainSaveReqVO updateReqVO) {
        // 校验存在
        validateDoMainExists(updateReqVO.getId());
        // 更新
        DoMainDO updateObj = BeanUtils.toBean(updateReqVO, DoMainDO.class);
        updateObj.setUpdateEmpno(getLoginUserNickname());
        updateObj.setUpdateDate(DateUtil.getDate());
        updateObj.setUpdateTime(LocalDateTime.now());
        updateObj.setUpdater(getLoginUserId().toString());
        doMainMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDoMain(Long id) {
        // 校验存在
        validateDoMainExists(id);
        // 删除
        doMainMapper.deleteById(id);
        // 删除子表
        deleteDoDetailByParentId(id);
    }

    private void validateDoMainExists(Long id) {
        if (doMainMapper.selectById(id) == null) {
            throw exception(DO_MAIN_NOT_EXISTS);
        }
    }

    @Override
    public DoMainRespVO getDoMain(Long id) {
        return doMainMapper.selectVOById(id);
    }

    @Override
    public DoMainDO getDoMainByReceiveno(String receiveno) {
        return doMainMapper.selectOne(new LambdaQueryWrapperX<DoMainDO>().eq(DoMainDO::getReceiveno, receiveno));
    }

    @Override
    public PageResult<DoMainRespVO> getDoMainPage(DoMainPageReqVO pageReqVO) {
        return doMainMapper.selectPage(pageReqVO);
    }

    @Override
    @Transactional
    public void confirmDoMain(AppGrDoShSaveVO  confirmVO) {
//    public void confirmDoMain(Long id, String flag,List<FileRela....>) {
        DoMainDO doMainDO = doMainMapper.selectById(confirmVO.getId());
        if(StringUtils.isBlank(confirmVO.getFlag())){
            doMainDO.setStus("Y");
        }else {
            doMainDO.setStus("Z");
        }
        if(StringUtils.isBlank(confirmVO.getPreText10())){
            doMainDO.setPreText10("");
        }else{
            doMainDO.setPreText10(confirmVO.getPreText10());
        }
        doMainDO.setPreText9(Optional.ofNullable(confirmVO.getPreText9()).orElse(""));

        doMainMapper.updateById(doMainDO);
        // 交货单来自电商平台时 生成验收单数据
        if ("D".equals(doMainDO.getFromsys()) || StringUtils.isNotBlank(confirmVO.getFlag())) {
            GrMainSaveReqVO grSaveReqVO = new GrMainSaveReqVO();
            grSaveReqVO.setPono(doMainDO.getPono());
            grSaveReqVO.setReceiveno(doMainDO.getReceiveno());
            grSaveReqVO.setRecvDate(doMainDO.getEstdlvyDate());
            grSaveReqVO.setStus("A");
            grSaveReqVO.setInspDate(DateUtil.getDate());
            if (StringUtils.isNotBlank(confirmVO.getFlag())) {
                grSaveReqVO.setAppid("xiaochengxu");
            }
            Long parentId = grMainService.createGrMain(grSaveReqVO);
            List<DoDetailDO> doDetailDOS = doDetailMapper.selectListByParentId(confirmVO.getId());
            if (doDetailDOS != null && doDetailDOS.size() > 0) {
                for (DoDetailDO doDetailDO : doDetailDOS) {
                    GrDetailDO grDetailDO = new GrDetailDO();
                    grDetailDO.setParentId(parentId);
                    grDetailDO.setMatrlno(doDetailDO.getMatrlno());
                    grDetailDO.setPono(doDetailDO.getPono());
                    grDetailDO.setPoitemno(doDetailDO.getPoitemno());
                    grDetailDO.setRecvqty(doDetailDO.getEstrecvqty());
                    grDetailDO.setInspqty(doDetailDO.getEstrecvqty());
                    grMainService.createGrDetail(grDetailDO);
                }
            }
        }
    }

    @Override
    public void cancelConfirmDoMain(Long id) {
        DoMainDO doMainDO = doMainMapper.selectById(id);
        doMainDO.setStus("N");
        doMainMapper.updateById(doMainDO);
    }

    @Override
    public PageResult<PoDoMainRespVO> getPoDoMainPage(DoMainPageReqVO pageReqVO) {
        IPage<PoDoMainRespVO> pageResult = doMainMapper.selectPageList(MyBatisUtils.buildPage(pageReqVO),
                pageReqVO.getPono(), pageReqVO.getReceiveno(), pageReqVO.getStus());
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }

    @Override
    public List<DoMainRespDTO> getDoMainListByVendor(PoMainReqDTO reqDTO) {
        List<DoMainRespDTO> dtoLists = new ArrayList<>();
        TenantContextHolder.setTenantId(Long.valueOf(reqDTO.getTenantId()));
        List<PoMainDO> mainDOS = poMainMapper.selectListByVendor(reqDTO.getVendorno());
        for (PoMainDO mainDO : mainDOS) {
            List<DoMainDO> doList = doMainMapper.selectListByVendor(mainDO.getPono(), "D", reqDTO.getStus());
            List<DoMainRespDTO> dtoList = BeanUtils.toBean(doList, DoMainRespDTO.class);
            for (DoMainRespDTO dto : dtoList) {
                List<DoDetailDTO> doDetailDOList = doDetailMapper.selectListByReceiveno(dto.getReceiveno());
                dto.setDoDetailList(doDetailDOList);
            }
            dtoLists.addAll(dtoList);
        }
        return dtoLists;
    }

    @Override
    @Transactional
    public String createDoInfo(DoMainSaveReqDTO reqDT) {
        TenantContextHolder.setTenantId(Long.valueOf(reqDT.getTenantId()));
        // 生成交货单号 校验唯一
        String receiveno = noRedisDAO.generate(PmsNoRedisDAO.DO_MAIN_NO_PREFIX);
        if (doMainMapper.selectByReceiveno(receiveno) != null) {
            throw exception(DO_NO_EXISTS);
        }

        DoMainDO mainDO = BeanUtils.toBean(reqDT, DoMainDO.class);
        mainDO.setReceiveno(receiveno);
        mainDO.setCreateEmpno(reqDT.getCreateEmpno());
        mainDO.setUpdateEmpno(reqDT.getCreateEmpno());
        mainDO.setCreator(reqDT.getCreateEmpno());
        mainDO.setUpdater(reqDT.getCreateEmpno());
        mainDO.setStus("N");
        mainDO.setFromsys("D");
        doMainMapper.insert(mainDO);

        Long parentId = mainDO.getId();
        List<DoDetailDTO> doDetailList = reqDT.getDoDetailList();
        if (doDetailList != null && doDetailList.size() > 0) {
            for (DoDetailDTO doDetailDTO : doDetailList) {
                DoDetailDO detailDO = BeanUtils.toBean(doDetailDTO, DoDetailDO.class);
                detailDO.setParentId(parentId);
                detailDO.setProject(mainDO.getProject());
                detailDO.setCompid(mainDO.getCompid());
                detailDO.setReceiveno(mainDO.getReceiveno());
                String serialNo = doDetailMapper.getMaxDoItemNoByParentId(parentId);
                String receiveItemNo = "0000";
                if (serialNo != null) {
                    receiveItemNo = String.format("%04d", Integer.valueOf(serialNo) + 1);
                }
                detailDO.setReceiveitemno(receiveItemNo);
                doDetailMapper.insert(detailDO);
            }
        }
        return mainDO.getReceiveno();
    }

    // ==================== 子表（交货单明细） ====================

    @Override
    public PageResult<DoDetailRespVO> getDoDetailPage(DoDetailPageReqVO pageReqVO) {
        IPage<DoDetailRespVO> pageResult = doDetailMapper.selectDetailPage(
                MyBatisUtils.buildPage(pageReqVO),
                pageReqVO.getParentId(),
                pageReqVO.getPono(),
                pageReqVO.getReceiveno());
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }

    @Override
    public Long createDoDetail(DoDetailDO doDetail) {
        Long parentId = doDetail.getParentId();
        validateDoMainExists(parentId);
        DoMainDO doMain = doMainMapper.selectById(parentId);
        String serialNo = doDetailMapper.getMaxDoItemNoByParentId(parentId);
        String receiveItemNo = "0000";
        if (serialNo != null) {
            receiveItemNo = String.format("%04d", Integer.valueOf(serialNo) + 1);
        }
        doDetail.setProject(doMain.getProject());
        doDetail.setCompid(doMain.getCompid());
        doDetail.setReceiveno(doMain.getReceiveno());
        doDetail.setReceiveitemno(receiveItemNo);
        doDetail.setCreateEmpno(getLoginUserNickname());
        doDetail.setCreateDate(DateUtil.getDate());
        doDetail.setCreator(getLoginUserId().toString());
        doDetail.setUpdateEmpno(getLoginUserNickname());
        doDetail.setUpdateDate(DateUtil.getDate());
        doDetail.setUpdater(getLoginUserId().toString());
        doDetailMapper.insert(doDetail);
        return doDetail.getId();
    }

    @Override
    public void updateDoDetail(DoDetailDO doDetail) {
        // 校验存在
        validateDoDetailExists(doDetail.getId());
        doDetail.setUpdateEmpno(getLoginUserNickname());
        doDetail.setUpdateDate(DateUtil.getDate());
        doDetail.setUpdateTime(LocalDateTime.now());
        doDetail.setUpdater(getLoginUserId().toString());
        // 更新
        doDetailMapper.updateById(doDetail);
    }

    @Override
    public void deleteDoDetail(Long id) {
        // 校验存在
        validateDoDetailExists(id);
        // 删除
        doDetailMapper.deleteById(id);
    }

    @Override
    public DoDetailRespVO getDoDetail(Long id) {
        return doDetailMapper.selectVOById(id);
    }

    private void validateDoDetailExists(Long id) {
        if (doDetailMapper.selectById(id) == null) {
            throw exception(DO_DETAIL_NOT_EXISTS);
        }
    }
    @Override
    public Map<Long, Long> countDetailByPoDoMainId(List<Long> doMainIds) {
        Map<Long, Long> result = new HashMap<>();
        if (CollectionUtil.isNotEmpty(doMainIds)) {
            List<DoDetailDO> detailList =
                    doDetailMapper.selectList(new LambdaQueryWrapperX<DoDetailDO>().in(DoDetailDO::getParentId, doMainIds));
            if (CollectionUtil.isNotEmpty(detailList)) {
                result =
                        detailList.stream().collect(Collectors.groupingBy(DoDetailDO::getParentId, Collectors.counting()));
            }
        }
        return result;
    }
    private void deleteDoDetailByParentId(Long parentId) {
        doDetailMapper.deleteByParentId(parentId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInsertDoDetails(DoDetailBatchInsertReqVO reqVO) {
        String pono = reqVO.getPono();
        String parentId = reqVO.getParentId();
        DoMainDO doMainDO = doMainMapper.selectById(parentId);
        if (doMainDO == null) {
            throw exception(DO_MAIN_NOT_EXISTS);
        }
        List<DoDetailBatchInsertListReqVO> items = reqVO.getItems();
        PoMainDO poMainDO = poMainMapper.selectOne(PoMainDO::getPono, pono);
        if (poMainDO == null) {
            throw exception(PO_MAIN_NOT_EXISTS);
        }
        String serialNo = doDetailMapper.getMaxReceiveItemNo(doMainDO.getReceiveno());
        String receiveItemNo = "0000";
        if (serialNo != null) {
            receiveItemNo = String.format("%04d", Integer.valueOf(serialNo) + 1);
        }
        for (DoDetailBatchInsertListReqVO item : items) {
            PoDetailDO poDetailDO = poDetailMapper.selectOne(PoDetailDO::getPono, pono, PoDetailDO::getPoitemno, item.getPoitemno());
            if (poDetailDO == null) {
                throw exception(PO_DETAIL_NOT_EXISTS_ITEM_NO, item.getPoitemno());
            }
            BigDecimal inspqty = item.getInspqty() != null ? item.getInspqty(): new BigDecimal(0);
            BigDecimal restqty = poDetailDO.getQty().subtract(inspqty);
            if (item.getEstrecvqty() != null && item.getEstrecvqty().compareTo(restqty) > 0) {
                throw exception(DO_DETAIL_ESTRECVQTY_MORE_THAN, item.getPoitemno());
            }
            DoDetailDO doDetailDO = new DoDetailDO();
            doDetailDO.setProject(doMainDO.getProject());
            doDetailDO.setCompid(doMainDO.getCompid());
            doDetailDO.setParentId(doMainDO.getId());
            doDetailDO.setReceiveno(doMainDO.getReceiveno());
            doDetailDO.setReceiveitemno(receiveItemNo);
            doDetailDO.setPono(pono);
            doDetailDO.setPoitemno(item.getPoitemno());
            doDetailDO.setMatrlno(poDetailDO.getMatrlno());
            doDetailDO.setOrderqty(poDetailDO.getQty());
            doDetailDO.setInspqty(new BigDecimal(BigInteger.ZERO));
            doDetailDO.setEstrecvqty(item.getEstrecvqty());
            doDetailDO.setIfweigh(item.getIfweigh());
            doDetailDO.setMemo(poDetailDO.getMemo());
            doDetailDO.setCreateEmpno(getLoginUserNickname());
            doDetailDO.setCreateDate(DateUtil.getDate());
            doDetailDO.setCreator(getLoginUserId().toString());
            doDetailDO.setUpdateEmpno(getLoginUserNickname());
            doDetailDO.setUpdateDate(DateUtil.getDate());
            doDetailDO.setUpdater(getLoginUserId().toString());
            doDetailMapper.insert(doDetailDO);
            receiveItemNo = String.format("%04d", Integer.valueOf(receiveItemNo) + 1);
        }
    }

}

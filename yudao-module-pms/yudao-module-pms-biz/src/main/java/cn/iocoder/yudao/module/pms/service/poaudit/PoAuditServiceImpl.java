package cn.iocoder.yudao.module.pms.service.poaudit;


import cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils;
import cn.iocoder.yudao.module.bpm.api.task.BpmProcessInstanceApi;
import cn.iocoder.yudao.module.bpm.api.task.dto.BpmProcessInstanceCreateReqDTO;
import cn.iocoder.yudao.module.bpm.enums.task.BpmTaskStatusEnum;
import cn.iocoder.yudao.module.pms.api.poaudit.dto.PmsTaskSaveReqDTO;
import cn.iocoder.yudao.module.pms.dal.dataobject.grmain.GrMainDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.pomain.PoDetailDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.pomain.PoMainDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.pomainv.PoDetailVDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.pomainv.PoMainVDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.wmstrade.WmsTradeDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.wmstrademain.WmsTradeDetail0DO;
import cn.iocoder.yudao.module.pms.dal.dataobject.wmstrademain.WmsTradeMainDO;
import cn.iocoder.yudao.module.pms.dal.mysql.grmain.GrMainMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.pomain.PoDetailMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.pomain.PoMainMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.pomainv.PoDetailVMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.pomainv.PoMainVMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.wmstrade.WmsTradeMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.wmstrademain.WmsTradeDetail0Mapper;
import cn.iocoder.yudao.module.pms.dal.mysql.wmstrademain.WmsTradeMainMapper;
import cn.iocoder.yudao.module.pms.service.pomain.PoMainService;
import cn.iocoder.yudao.module.pms.service.wmstrademain.WmsTradeMainService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import cn.iocoder.yudao.module.pms.controller.admin.poaudit.vo.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.poaudit.PoAuditDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.pms.dal.mysql.poaudit.PoAuditMapper;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserNickname;
import static cn.iocoder.yudao.module.pms.enums.ErrorCodeConstants.*;

/**
 * 合同审批 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PoAuditServiceImpl implements PoAuditService {

    @Resource
    private PoAuditMapper poAuditMapper;
    @Resource
    private PoMainMapper poMainMapper;
    @Resource
    private PoMainVMapper poMainVMapper;
    @Resource
    private GrMainMapper grMainMapper;
    @Resource
    private WmsTradeMainMapper wmsTradeMainMapper;
    @Resource
    private WmsTradeMapper wmsTradeMapper;
    @Resource
    private WmsTradeDetail0Mapper wmsTradeDetail0Mapper;
    @Resource
    private PoMainService poMainService;
    @Resource
    private WmsTradeMainService wmsTradeMainService;
    @Resource
    private PoDetailMapper poDetailMapper;
    @Resource
    private PoDetailVMapper poDetailVMapper;
    /**
     * OA 请假对应的流程定义 KEY
     */
    public static final String PMS_PO_AUDIT = "pms_po_audit";
    public static final String PMS_GR_AUDIT = "pms_gr_audit";
    public static final String WMS_TRADE_MAIN_B_AUDIT = "wms_trade_main_b_audit";
    public static final String PMS_PO_AUDIT_IO = "pms_po_audit_io";

    @Resource
    @Lazy
    private BpmProcessInstanceApi processInstanceApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createPoAudit(PoAuditSaveReqVO createReqVO) {
        String businessType = createReqVO.getBusinessType();
        Long id = null;
        switch (businessType) {
            case PMS_PO_AUDIT:
                id = createPmsPoAuditProcess(createReqVO);
                break;
            case PMS_GR_AUDIT:
                id = createPmsGrAuditProcess(createReqVO);
                break;
            case WMS_TRADE_MAIN_B_AUDIT:
                id = createWmsTradeMainBAuditProcess(createReqVO);
                break;
            case PMS_PO_AUDIT_IO:
                id = createPmsPoAuditIOProcess(createReqVO);
                break;
        }
        return id;
    }

    @Transactional(rollbackFor = Exception.class)
    public Long createPmsPoAuditProcess(PoAuditSaveReqVO createReqVO) {
        Long userId = WebFrameworkUtils.getLoginUserId();
        PoAuditDO poAuditDO = BeanUtils.toBean(createReqVO, PoAuditDO.class)
                .setUserId(userId)
                .setUserName(getLoginUserNickname())
                .setParentId(createReqVO.getParentId())
                .setBusinessNo(createReqVO.getBusinessNo())
                .setAppId(createReqVO.getAppId())
                .setStatus(BpmTaskStatusEnum.RUNNING.getStatus().toString());
        poAuditMapper.insert(poAuditDO);

        // 发起 BPM 流程
        Map<String, Object> processInstanceVariables = new HashMap<>();
        String processInstanceId = processInstanceApi.createProcessInstance(userId,
                new BpmProcessInstanceCreateReqDTO().setProcessDefinitionKey(createReqVO.getBusinessType())
                        .setVariables(processInstanceVariables)
                        .setBusinessKey(String.valueOf(poAuditDO.getParentId()))
                        .setStartUserSelectAssignees(createReqVO.getStartUserSelectAssignees())).getCheckedData();

        // 将工作流的编号，回写合同审核表 合同主表
        if("pomainv".equals(poAuditDO.getAppId())){
            calculateAmt(createReqVO.getParentId());
            poMainVMapper.updateById(new PoMainVDO().setId(createReqVO.getParentId()).setSignplace(processInstanceId).setFlowstus("G").setPostus("G"));
        }else {
            calculateVAmt(createReqVO.getParentId());
            poMainMapper.updateById(new PoMainDO().setId(createReqVO.getParentId()).setSignplace(processInstanceId).setFlowstus("G").setPostus("G"));
        }
        poAuditMapper.updateById(new PoAuditDO().setId(poAuditDO.getId()).setProcessInstanceId(processInstanceId).setAuditType("G"));
        return poAuditDO.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public Long createPmsPoAuditIOProcess(PoAuditSaveReqVO createReqVO) {
        Long userId = WebFrameworkUtils.getLoginUserId();
        PoAuditDO poAuditDO = BeanUtils.toBean(createReqVO, PoAuditDO.class)
                .setUserId(userId)
                .setUserName(getLoginUserNickname())
                .setParentId(createReqVO.getParentId())
                .setBusinessNo(createReqVO.getBusinessNo())
                .setAppId(createReqVO.getAppId())
                .setStatus(BpmTaskStatusEnum.RUNNING.getStatus().toString());
        poAuditMapper.insert(poAuditDO);

        // 发起 BPM 流程
        Map<String, Object> processInstanceVariables = new HashMap<>();
        String processInstanceId = processInstanceApi.createProcessInstance(userId,
                new BpmProcessInstanceCreateReqDTO().setProcessDefinitionKey(createReqVO.getBusinessType())
                        .setVariables(processInstanceVariables)
                        .setBusinessKey(String.valueOf(poAuditDO.getParentId()))
                        .setStartUserSelectAssignees(createReqVO.getStartUserSelectAssignees())).getCheckedData();

        // 将工作流的编号，回写合同审核表 合同主表
        if("pomainv".equals(poAuditDO.getAppId())){
            calculateVAmt(createReqVO.getParentId());
            poMainVMapper.updateById(new PoMainVDO().setId(createReqVO.getParentId()).setSignplace(processInstanceId).setFlowstus("G").setPostus("G"));
        }else {
            calculateAmt(createReqVO.getParentId());
            poMainMapper.updateById(new PoMainDO().setId(createReqVO.getParentId()).setSignplace(processInstanceId).setFlowstus("G").setPostus("G"));
        }
        poAuditMapper.updateById(new PoAuditDO().setId(poAuditDO.getId()).setProcessInstanceId(processInstanceId).setAuditType("G"));
        return poAuditDO.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public Long createPmsGrAuditProcess(PoAuditSaveReqVO createReqVO) {
        Long userId = WebFrameworkUtils.getLoginUserId();
        PoAuditDO poAuditDO = BeanUtils.toBean(createReqVO, PoAuditDO.class)
                .setUserId(userId).setParentId(createReqVO.getParentId()).setStatus(BpmTaskStatusEnum.RUNNING.getStatus().toString());
        poAuditMapper.insert(poAuditDO);

        // 发起 BPM 流程
        Map<String, Object> processInstanceVariables = new HashMap<>();
        String processInstanceId = processInstanceApi.createProcessInstance(userId,
                new BpmProcessInstanceCreateReqDTO().setProcessDefinitionKey(createReqVO.getBusinessType())
                        .setVariables(processInstanceVariables)
                        .setBusinessKey(String.valueOf(poAuditDO.getParentId()))
                        .setStartUserSelectAssignees(createReqVO.getStartUserSelectAssignees())).getCheckedData();

        // 将工作流的编号，合同审核表 合同主表
        poAuditMapper.updateById(new PoAuditDO().setId(poAuditDO.getId()).setProcessInstanceId(processInstanceId).setAuditType("D"));
        grMainMapper.updateById(new GrMainDO().setId(createReqVO.getParentId()).setProcessId(processInstanceId).setStus("D"));
        return poAuditDO.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public Long createWmsTradeMainBAuditProcess(PoAuditSaveReqVO createReqVO) {
        Long userId = WebFrameworkUtils.getLoginUserId();
        PoAuditDO poAuditDO = BeanUtils.toBean(createReqVO, PoAuditDO.class)
                .setUserId(userId)
                .setUserName(getLoginUserNickname())
                .setParentId(createReqVO.getParentId())
                .setStatus(BpmTaskStatusEnum.RUNNING.getStatus().toString());
        poAuditMapper.insert(poAuditDO);

        // 发起 BPM 流程
        Map<String, Object> processInstanceVariables = new HashMap<>();
        String processInstanceId = processInstanceApi.createProcessInstance(userId,
                new BpmProcessInstanceCreateReqDTO().setProcessDefinitionKey(createReqVO.getBusinessType())
                        .setVariables(processInstanceVariables)
                        .setBusinessKey(String.valueOf(poAuditDO.getParentId()))
                        .setStartUserSelectAssignees(createReqVO.getStartUserSelectAssignees())).getCheckedData();

        // 将工作流的编号，合同审核表 合同主表
        poAuditMapper.updateById(new PoAuditDO().setId(poAuditDO.getId()).setProcessInstanceId(processInstanceId).setAuditType("D"));
        wmsTradeMainMapper.updateById(new WmsTradeMainDO().setId(createReqVO.getParentId()).setProcessId(processInstanceId).setStus("D"));
        List<WmsTradeDetail0DO> wmsTradeDetail0DOList = wmsTradeDetail0Mapper.selectList(WmsTradeDetail0DO::getParentId, poAuditDO.getParentId());
        if (!wmsTradeDetail0DOList.isEmpty() && wmsTradeDetail0DOList.size() > 0) {
            for (WmsTradeDetail0DO wmsTradeDetail0DO : wmsTradeDetail0DOList) {
                wmsTradeDetail0DO.setStus("D");
                wmsTradeDetail0DO.setUpdater(getLoginUserId().toString());
                wmsTradeDetail0DO.setUpdateEmpNo(getLoginUserNickname());
                wmsTradeDetail0Mapper.updateById(wmsTradeDetail0DO);
            }
        }

        List<WmsTradeDO> wmsTradeDOList = wmsTradeMapper.selectList(WmsTradeDO::getParentId, poAuditDO.getParentId());
        if (!wmsTradeDOList.isEmpty() && wmsTradeDOList.size() > 0) {
            for (WmsTradeDO wmsTradeDO : wmsTradeDOList) {
                wmsTradeDO.setStus("D");
                wmsTradeDO.setUpdater(getLoginUserId().toString());
                wmsTradeDO.setUpdateEmpNo(getLoginUserNickname());
                wmsTradeMapper.updateById(wmsTradeDO);
            }
        }
        return poAuditDO.getId();
    }

    @Transactional
    @Override
    public void updatePmsAuditStatus(PmsTaskSaveReqDTO pmsTaskSaveVO) {
        //回写业务表状态
        PoAuditDO poAuditDO = poAuditMapper.selectAuditByProcessInstanceId(pmsTaskSaveVO.getProcessInstanceId());
        poAuditMapper.updateById(new PoAuditDO().setId(poAuditDO.getId()).setAuditType(pmsTaskSaveVO.getAuditType()).setStatus(String.valueOf(pmsTaskSaveVO.getStatus())));

        if (PMS_PO_AUDIT.equals(pmsTaskSaveVO.getBusinessType())) {
            if("pomainv".equals(poAuditDO.getAppId())){
                poMainVMapper.updateById(new PoMainVDO().setId(poAuditDO.getParentId()).setFlowstus(pmsTaskSaveVO.getAuditType()).setPostus(pmsTaskSaveVO.getAuditType()));
            }else {
                poMainMapper.updateById(new PoMainDO().setId(poAuditDO.getParentId()).setFlowstus(pmsTaskSaveVO.getAuditType()).setPostus(pmsTaskSaveVO.getAuditType()));
            }
        }if (PMS_PO_AUDIT_IO.equals(pmsTaskSaveVO.getBusinessType())) {
            if("pomainv".equals(poAuditDO.getAppId())){
                poMainVMapper.updateById(new PoMainVDO().setId(poAuditDO.getParentId()).setFlowstus(pmsTaskSaveVO.getAuditType()).setPostus(pmsTaskSaveVO.getAuditType()));
            }else {
                poMainMapper.updateById(new PoMainDO().setId(poAuditDO.getParentId()).setFlowstus(pmsTaskSaveVO.getAuditType()).setPostus(pmsTaskSaveVO.getAuditType()));
            }
        } else if (PMS_GR_AUDIT.equals(pmsTaskSaveVO.getBusinessType())) {
            GrMainDO grMainDO = grMainMapper.selectById(pmsTaskSaveVO.getId());
            if("Y".equals(grMainDO.getIsDetailCheck())){//是否详细验质 是 直接流转到验收中状态
                pmsTaskSaveVO.setAuditType("Z");
            }
            grMainMapper.updateById(new GrMainDO().setId(grMainDO.getId()).setStus(pmsTaskSaveVO.getAuditType()));
        } else if (WMS_TRADE_MAIN_B_AUDIT.equals(pmsTaskSaveVO.getBusinessType())) {
            WmsTradeMainDO wmsTradeMainDO = wmsTradeMainMapper.selectById(poAuditDO.getParentId());
            wmsTradeMainDO.setStus(pmsTaskSaveVO.getAuditType());
            wmsTradeMainDO.setUpdater(getLoginUserId().toString());
            wmsTradeMainDO.setUpdateEmpNo(getLoginUserNickname());
            wmsTradeMainMapper.updateById(wmsTradeMainDO);
            List<WmsTradeDO> wmsTradeDOList = wmsTradeMapper.selectList(WmsTradeDO::getParentId, poAuditDO.getParentId());
            if (!wmsTradeDOList.isEmpty() && wmsTradeDOList.size() > 0) {
                for (WmsTradeDO wmsTradeDO : wmsTradeDOList) {
                    wmsTradeDO.setStus(pmsTaskSaveVO.getAuditType());
                    wmsTradeDO.setUpdater(getLoginUserId().toString());
                    wmsTradeDO.setUpdateEmpNo(getLoginUserNickname());
                    wmsTradeMapper.updateById(wmsTradeDO);
                }
            }
            List<WmsTradeDetail0DO> wmsTradeDetail0DOList = wmsTradeDetail0Mapper.selectList(WmsTradeDetail0DO::getParentId, poAuditDO.getParentId());
            if (!wmsTradeDetail0DOList.isEmpty() && wmsTradeDetail0DOList.size() > 0) {
                for (WmsTradeDetail0DO wmsTradeDetail0DO : wmsTradeDetail0DOList) {
                    wmsTradeDetail0DO.setStus(pmsTaskSaveVO.getAuditType());
                    wmsTradeDetail0DO.setUpdater(getLoginUserId().toString());
                    wmsTradeDetail0DO.setUpdateEmpNo(getLoginUserNickname());
                    wmsTradeDetail0Mapper.updateById(wmsTradeDetail0DO);
                }
                if ("K".equals(pmsTaskSaveVO.getAuditType())) {
                    String appid = "";
                    if ("B".equals(wmsTradeMainDO.getIssueType())) {
                        appid = "wmstrademainb";
                        wmsTradeMainService.handleAudit(poAuditDO.getParentId(), appid);
                    } else if ("D".equals(wmsTradeMainDO.getIssueType())) {
                        appid = "wmstrademaind";
                        wmsTradeMainService.handleAudit(poAuditDO.getParentId(), appid);
                    } else if ("K".equals(wmsTradeMainDO.getIssueType())) {
                        appid = "wmstrademaink";
                        wmsTradeMainService.handleAudit(poAuditDO.getParentId(), appid);
                    }
                }
            }
        }

    }

    private void validatePoAuditExists(Long id) {
        if (poAuditMapper.selectById(id) == null) {
            throw exception(PO_AUDIT_NOT_EXISTS);
        }
    }

    @Override
    public PoAuditDO getPoAudit(Long id) {
        return poAuditMapper.selectById(id);
    }

    @Override
    public PageResult<PoAuditDO> getPoAuditPage(PoAuditPageReqVO pageReqVO) {
        return poAuditMapper.selectPage(pageReqVO);
    }

    public void calculateAmt(Long id) {
        BigDecimal totalAmt = new BigDecimal(0);
        BigDecimal totalQty = new BigDecimal(0);
        PoMainDO poMainDO = poMainMapper.selectById(id);
        List<PoDetailDO> poDetailDOs = poDetailMapper.selectListByParentId(id);
        if (poDetailDOs != null && poDetailDOs.size() > 0) {
            for (PoDetailDO detailDO : poDetailDOs) {
                BigDecimal amt = detailDO.getAmt();
                if (amt == null) {
                    amt = new BigDecimal(0);
                }
                BigDecimal qty = detailDO.getQty();
                if (qty == null) {
                    qty = new BigDecimal(0);
                }
                totalAmt = totalAmt.add(amt);
                totalQty = totalQty.add(qty);
            }
        }
        poMainDO.setTotalAmt(totalAmt);
        poMainDO.setTotalQty(totalQty);
        poMainMapper.updateById(poMainDO);
    }

    public void calculateVAmt(Long id) {
        BigDecimal totalAmt = new BigDecimal(0);
        BigDecimal totalQty = new BigDecimal(0);
        PoMainVDO poMainVDO = poMainVMapper.selectById(id);
        List<PoDetailVDO> poDetailVDOs = poDetailVMapper.selectListByParentId(id);
        if (poDetailVDOs != null && poDetailVDOs.size() > 0) {
            for (PoDetailVDO detailVDO : poDetailVDOs) {
                BigDecimal amt = detailVDO.getAmt();
                if (amt == null) {
                    amt = new BigDecimal(0);
                }
                BigDecimal qty = detailVDO.getQty();
                if (qty == null) {
                    qty = new BigDecimal(0);
                }
                totalAmt = totalAmt.add(amt);
                totalQty = totalQty.add(qty);
            }
        }
        poMainVDO.setTotalAmt(totalAmt);
        poMainVDO.setTotalQty(totalQty);
        poMainVMapper.updateById(poMainVDO);
    }


}
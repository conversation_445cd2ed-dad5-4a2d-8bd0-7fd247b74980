package cn.iocoder.yudao.module.pms.controller.admin.wmstrademain;

import cn.iocoder.yudao.framework.common.util.date.DateUtil;
import cn.iocoder.yudao.module.pms.dal.dataobject.wmstrademain.WmsTradeDetail0DO;
import io.swagger.v3.oas.annotations.Parameters;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.math.BigDecimal;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.pms.controller.admin.wmstrademain.vo.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.wmstrademain.WmsTradeMainDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.wmstrade.WmsTradeDO;
import cn.iocoder.yudao.module.pms.service.wmstrademain.WmsTradeMainService;
import org.springframework.web.multipart.MultipartFile;

@Tag(name = "管理后台 - 物料交易申请主档")
@RestController
@RequestMapping("/pms/wms-trade-main")
@Validated
public class WmsTradeMainController {

    @Resource
    private WmsTradeMainService wmsTradeMainService;

    @PostMapping("/create")
    @Operation(summary = "创建物料交易申请主档")
    @PreAuthorize("@ss.hasPermission('pms:wms-trade-main:create')")
    public CommonResult<Long> createWmsTradeMain(@Valid @RequestBody WmsTradeMainSaveReqVO createReqVO) {
        return success(wmsTradeMainService.createWmsTradeMain(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新物料交易申请主档")
    @PreAuthorize("@ss.hasPermission('pms:wms-trade-main:update')")
    public CommonResult<Boolean> updateWmsTradeMain(@Valid @RequestBody WmsTradeMainSaveReqVO updateReqVO) {
        wmsTradeMainService.updateWmsTradeMain(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除物料交易申请主档")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:wms-trade-main:delete')")
    public CommonResult<Boolean> deleteWmsTradeMain(@RequestParam("id") Long id) {
        wmsTradeMainService.deleteWmsTradeMain(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得物料交易申请主档")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:wms-trade-main:query')")
    public CommonResult<WmsTradeMainRespVO> getWmsTradeMain(@RequestParam("id") Long id) {
        WmsTradeMainDO wmsTradeMain = wmsTradeMainService.getWmsTradeMain(id);
        return success(BeanUtils.toBean(wmsTradeMain, WmsTradeMainRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得物料交易申请主档分页")
    @PreAuthorize("@ss.hasPermission('pms:wms-trade-main:query')")
    public CommonResult<PageResult<WmsTradeMainRespVO>> getWmsTradeMainPage(@Valid WmsTradeMainPageReqVO pageReqVO) {
        PageResult<WmsTradeMainDO> pageResult = wmsTradeMainService.getWmsTradeMainPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, WmsTradeMainRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出物料交易申请主档 Excel")
    @PreAuthorize("@ss.hasPermission('pms:wms-trade-main:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportWmsTradeMainExcel(@Valid WmsTradeMainPageReqVO pageReqVO,
                                        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<WmsTradeMainDO> list = wmsTradeMainService.getWmsTradeMainPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "物料交易申请主档.xls", "数据", WmsTradeMainRespVO.class,
                BeanUtils.toBean(list, WmsTradeMainRespVO.class));
    }

    // ==================== 子表（物料交易明细） ====================

    @GetMapping("/wms-trade/page")
    @Operation(summary = "获得物料交易明细分页")
    @Parameter(name = "parentId", description = "父id")
    @PreAuthorize("@ss.hasPermission('pms:wms-trade-main:query')")
    public CommonResult<PageResult<WmsTradeRespVO>> getWmsTradePage(PageParam pageReqVO, @RequestParam("parentId") Long parentId,
                                                                    @RequestParam(value = "purposeId", required = false) String purposeId) {
        return success(wmsTradeMainService.getWmsTradePage(pageReqVO, parentId, purposeId));
    }

    @PostMapping("/wms-trade/create")
    @Operation(summary = "创建物料交易明细")
    @PreAuthorize("@ss.hasPermission('pms:wms-trade-main:create')")
    public CommonResult<Long> createWmsTrade(@Valid @RequestBody WmsTradeDO wmsTrade) {
        return success(wmsTradeMainService.createWmsTrade(wmsTrade));
    }

    @PostMapping("/wms-trade/update")
    @Operation(summary = "更新物料交易明细")
    @PreAuthorize("@ss.hasPermission('pms:wms-trade-main:update')")
    public CommonResult<Boolean> updateWmsTrade(@Valid @RequestBody WmsTradeDO wmsTrade) {
        wmsTradeMainService.updateWmsTrade(wmsTrade);
        return success(true);
    }

    @DeleteMapping("/wms-trade/delete")
    @Parameter(name = "id", description = "编号", required = true)
    @Operation(summary = "删除物料交易明细")
    @PreAuthorize("@ss.hasPermission('pms:wms-trade-main:delete')")
    public CommonResult<Boolean> deleteWmsTrade(@RequestParam("id") Long id) {
        wmsTradeMainService.deleteWmsTrade(id);
        return success(true);
    }

    @GetMapping("/wms-trade/get")
    @Operation(summary = "获得物料交易明细")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:wms-trade-main:query')")
    public CommonResult<WmsTradeRespVO> getWmsTrade(@RequestParam("id") Long id) {
        return success(wmsTradeMainService.getWmsTrade(id));
    }

    @GetMapping("/confirm")
    @Operation(summary = "验收确认")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:wms-trade-main:update')")
    public CommonResult<Boolean> confirmTrade(@RequestParam("id") Long id) {
        wmsTradeMainService.confirmTrade(id);
        return success(true);
    }

    @GetMapping("/cancelConfirm")
    @Operation(summary = "取消验收确认")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:wms-trade-main:update')")
    public CommonResult<Boolean> cancelConfirmTrade(@RequestParam("id") Long id) {
        wmsTradeMainService.cancelConfirmTrade(id);
        return success(true);
    }

    @GetMapping("/audit")
    @Operation(summary = "发起审核")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:wms-trade-main:update')")
    public CommonResult<Boolean> handleAudit(@RequestParam("id") Long id, @RequestParam("appid") String appid) {
        wmsTradeMainService.handleAudit(id, appid);
        return success(true);
    }

    @GetMapping("/wms-trade/audit")
    @Operation(summary = "发起交易明细审核")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:wms-trade-main:update')")
    public CommonResult<Boolean> handleAuditDetail(@RequestParam("id") Long id) {
        wmsTradeMainService.handleAuditDetail(id);
        return success(true);
    }

    @GetMapping("/wms-trade/confirmDetail")
    @Operation(summary = "交易确认")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:wms-trade-main:update')")
    public CommonResult<Boolean> confirmTradeDetail(@RequestParam("id") Long id) {
        wmsTradeMainService.confirmTradeDetail(id);
        return success(true);
    }

    @GetMapping("/wms-trade/cancelDetail")
    @Operation(summary = "取消交易确认")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:wms-trade-main:update')")
    public CommonResult<Boolean> cancelTradeDetail(@RequestParam("id") Long id) {
        wmsTradeMainService.cancelTradeDetail(id);
        return success(true);
    }

    @PostMapping("/wms-trade-detail0/create")
    @Operation(summary = "创建物料交易中间")
    @PreAuthorize("@ss.hasPermission('pms:wms-trade-main:create')")
    public CommonResult<Long> createWmsTradeDetail0(@Valid @RequestBody WmsTradeDetail0DO createReqVO) {
        return success(wmsTradeMainService.createWmsTradeDetail0(createReqVO));
    }

    @PutMapping("/wms-trade-detail0/update")
    @Operation(summary = "更新物料交易中间")
    @PreAuthorize("@ss.hasPermission('pms:wms-trade-main:update')")
    public CommonResult<Boolean> updateWmsTradeDetail0(@Valid @RequestBody WmsTradeDetail0DO updateReqVO) {
        wmsTradeMainService.updateWmsTradeDetail0(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/wms-trade-detail0/delete")
    @Operation(summary = "删除物料交易中间")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:wms-trade-main:delete')")
    public CommonResult<Boolean> deleteWmsTradeDetail0(@RequestParam("id") Long id) {
        wmsTradeMainService.deleteWmsTradeDetail0(id);
        return success(true);
    }

    @GetMapping("/wms-trade-detail0/get")
    @Operation(summary = "获得物料交易中间")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:wms-trade-main:query')")
    public CommonResult<WmsTradeDetail0RespVO> getWmsTradeDetail0(@RequestParam("id") Long id) {
        WmsTradeDetail0RespVO wmsTradeDetail0 = wmsTradeMainService.getWmsTradeDetail0(id);
        return success(BeanUtils.toBean(wmsTradeDetail0, WmsTradeDetail0RespVO.class));
    }

    @GetMapping("/wms-trade-detail0/page")
    @Operation(summary = "获得物料交易中间分页")
    @PreAuthorize("@ss.hasPermission('pms:wms-trade-main:query')")
    public CommonResult<PageResult<WmsTradeDetail0RespVO>> getWmsTradeDetail0Page(PageParam pageReqVO,
                                                                                  @RequestParam("parentId") Long parentId) {
        PageResult<WmsTradeDetail0RespVO> pageResult = wmsTradeMainService.getWmsTradeDetail0Page(pageReqVO, parentId);
        return success(BeanUtils.toBean(pageResult, WmsTradeDetail0RespVO.class));
    }

    @PostMapping("/wms-trade-detail0/batch")
    @Operation(summary = "申请清单批量新增")
    @PreAuthorize("@ss.hasPermission('pms:wms-trade-main:create')")
    public CommonResult<Boolean> batchInsert(@RequestBody WmsTradeDetail0SaveReqVO reqVO) throws Exception {
        wmsTradeMainService.batchInsertDetails(reqVO);
        return success(true);
    }

    @GetMapping("/wms-trade/get-import-template")
    @Operation(summary = "获得期初开账清单导入模板")
    @PreAuthorize("@ss.hasPermission('pms:wms-trade:export')")
    public void importTemplate(HttpServletResponse response) throws IOException {
        // 手动创建导出 demo
        List<WmsTradeOImportExcelVO> list = Arrays.asList(
                WmsTradeOImportExcelVO.builder()
                        .serialNo("1")
                        .issueDate(DateUtil.getDate())
                        .vchrDate(DateUtil.getDate())
                        .planTranDate(DateUtil.getDate())
                        .matrlNo("08091002")
                        .stgNo("1B")
                        .lotNo("5555555")
                        .transNum(new BigDecimal(5.01))
                        .transAmt(new BigDecimal(50.05))
                        .remark("备注")
                        .build()
        );
        // 输出
        ExcelUtils.write(response, "期初开账清单导入模板.xls", "期初开账清单", WmsTradeOImportExcelVO.class, list);
    }

    @PostMapping("/wms-trade/import")
    @Operation(summary = "期初开账清单导入")
    @Parameters({
            @Parameter(name = "file", description = "Excel 文件", required = true),
            @Parameter(name = "updateSupport", description = "是否支持更新，默认为 false", example = "true")
    })
    @PreAuthorize("@ss.hasPermission('pms:wms-trade:export')")
    public CommonResult<WmsTradeOImportRespVO> importExcel(@RequestParam("file") MultipartFile file,
                                                           @RequestParam(value = "updateSupport", required = false, defaultValue = "false") Boolean updateSupport,
                                                           @RequestParam(value = "parentId", required = false, defaultValue = "1L") Long parentId) throws Exception {
        List<WmsTradeOImportExcelVO> list = ExcelUtils.read(file, WmsTradeOImportExcelVO.class);
        return success(wmsTradeMainService.importTradeList(list, updateSupport,parentId));
    }

    @PostMapping("/wms-trade/batchSplit")
    @Operation(summary = "发料清单拆分")
    @PreAuthorize("@ss.hasPermission('pms:wms-trade-main:create')")
    public CommonResult<String> batchSplitCase(@RequestBody WmsTradeSaveReqVO reqVO) throws Exception {
        return success(wmsTradeMainService.batchSplitCase(reqVO));
    }

    @PostMapping("/wms-trade/batch")
    @Operation(summary = "交易清单批量新增")
    @PreAuthorize("@ss.hasPermission('pms:wms-trade-main:create')")
    public CommonResult<Boolean> batchInsertWmsTrade(@RequestBody WmsTradeBatchSaveReqVO reqVO) throws Exception {
        wmsTradeMainService.batchInsertWmsTrade(reqVO);
        return success(true);
    }

    @PostMapping("/wms-trade/batch2")
    @Operation(summary = "交易清单批量新增")
    @PreAuthorize("@ss.hasPermission('pms:wms-trade-main:create')")
    public CommonResult<Boolean> batchInsertWmsTrade2(@RequestBody WmsTrade2BatchSaveReqVO reqVO) throws Exception {
        wmsTradeMainService.batchInsertWmsTrade2(reqVO);
        return success(true);
    }

    @GetMapping("/wms-trade/produce")
    @Operation(summary = "产生盘盈亏单")
    @PreAuthorize("@ss.hasPermission('pms:wms-trade-main:create')")
    public CommonResult<String> produceCase(@RequestParam("id") Long id) throws Exception {
        return success(wmsTradeMainService.produceCase(id));
    }

}
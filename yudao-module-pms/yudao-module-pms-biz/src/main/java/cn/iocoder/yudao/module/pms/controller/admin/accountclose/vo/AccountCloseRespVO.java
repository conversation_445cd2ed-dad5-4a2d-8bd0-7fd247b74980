package cn.iocoder.yudao.module.pms.controller.admin.accountclose.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 关账日期 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AccountCloseRespVO {

    @Schema(description = "账务编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "10239")
    @ExcelProperty("账务编号")
    private Long id;

    @Schema(description = "项目号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("项目号")
    private String project;

    @Schema(description = "公司别", requiredMode = Schema.RequiredMode.REQUIRED, example = "6445")
    @ExcelProperty("公司别")
    private String compid;

    @Schema(description = "品别", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("品别")
    private String inventorytype;

    @Schema(description = "年月", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("年月")
    private String yearmo;

    @Schema(description = "关帐起日")
    @ExcelProperty("关帐起日")
    private String firstdate;

    @Schema(description = "关帐止日")
    @ExcelProperty("关帐止日")
    private String enddate;

    @Schema(description = "关帐是否确定")
    @ExcelProperty("关帐是否确定")
    private String stus;

    @Schema(description = "关帐人")
    @ExcelProperty("关帐人")
    private String closeempno;

    @Schema(description = "关帐日期")
    @ExcelProperty("关帐日期")
    private String closedate;

    @Schema(description = "关帐时间")
    @ExcelProperty("关帐时间")
    private String closetime;

    @Schema(description = "新增时间")
    @ExcelProperty("新增时间")
    private LocalDateTime createTime;

}
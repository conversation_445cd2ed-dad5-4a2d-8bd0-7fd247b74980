package cn.iocoder.yudao.module.pms.service.mpp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.number.NumberUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.redis.lock.RedisDistributedLock;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.basic.api.BasicApi;
import cn.iocoder.yudao.module.basic.api.dto.MaterialDto;
import cn.iocoder.yudao.module.basic.api.dto.MaterialRespDto;
import cn.iocoder.yudao.module.bpm.api.task.BpmProcessInstanceApi;
import cn.iocoder.yudao.module.bpm.api.task.dto.BpmProcessInstanceCreateReqDTO;
import cn.iocoder.yudao.module.pms.api.mpp.audit.dto.MppDTO;
import cn.iocoder.yudao.module.pms.controller.admin.mpp.vo.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.mpp.MppDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.mpp.MppDetailDO;
import cn.iocoder.yudao.module.pms.dal.mysql.mpp.MppDetailMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.mpp.MppMapper;
import cn.iocoder.yudao.module.pms.service.wmstrade.WmsTradeService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Supplier;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.*;
import static cn.iocoder.yudao.module.pms.enums.ApiConstants.MPP_PROCESS_KEY;
import static cn.iocoder.yudao.module.pms.enums.ErrorCodeConstants.*;

/**
 * 采购计划主档 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MppServiceImpl implements MppService {

    @Resource
    private MppMapper mppMapper;
    @Resource
    private MppDetailMapper mppDetailMapper;
    @Resource
    private RedisDistributedLock redisDistributedLock;

    private WmsTradeService wmsTradeService;
    @Resource
    private BasicApi basicApi;

    public static final String REQNO ="MPP_REQNO";

    @Override
    public Long createMpp(MppSaveReqVO createReqVO) {
        // 插入
        MppDO mpp = BeanUtils.toBean(createReqVO, MppDO.class);
        mpp.setProject(String.valueOf(getLoginUserTopDeptId()));
        mpp.setCompid(String.valueOf(TenantContextHolder.getTenantId()));
        mpp.setReqempl(String.valueOf(getLoginUserId()));
        mpp.setReqdept(String.valueOf(getLoginUserDeptId()));
        mpp.setCreateEmpno(getLoginUserNickname());
        if(createReqVO.getExecutionPeriod()!=null&&createReqVO.getExecutionPeriod().length>1) {
            mpp.setExecutionPeriodStart(createReqVO.getExecutionPeriod()[0]);
            mpp.setExecutionPeriodEnd(createReqVO.getExecutionPeriod()[1]);
        }
//        mpp.setCreateDate(DateUtil.getDate());
        mpp.setCreateDate(LocalDateTimeUtil.formatNormal(LocalDate.now()));
        String seq = redisDistributedLock.lockWithNum(REQNO);
        mpp.setReqno("RW"+seq);

        mppMapper.insert(mpp);
        createReqVO.setReqno(mpp.getReqno());
        createReqVO.setId(mpp.getId());
        // 返回
        return mpp.getId();
    }

    @Override
    public void updateMpp(MppSaveReqVO updateReqVO) {
        // 校验存在
        MppDO mppDO = validateMppExists(updateReqVO.getId());

        if(!mppDO.getStus().matches("[0IJ]")){
            throw exception(TEADE_COMMON_ERROR,"请购计划目前不允许操作修改！请刷新");
        }
        // 更新
        MppDO updateObj = BeanUtils.toBean(updateReqVO, MppDO.class);
//        updateObj.setUpdateDate(DateUtil.getDate());
        updateObj.setUpdateDate(LocalDateTimeUtil.formatNormal(LocalDate.now()));
        updateObj.setUpdateEmpno(getLoginUserNickname());

        if(updateReqVO.getExecutionPeriod()!=null&&updateReqVO.getExecutionPeriod().length>1) {
            updateObj.setExecutionPeriodStart(updateReqVO.getExecutionPeriod()[0]);
            updateObj.setExecutionPeriodEnd(updateReqVO.getExecutionPeriod()[1]);
        }
//        updateObj.setDeleted(false);
        mppMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMpp(Long id) {
        // 校验存在
        MppDO mppDO = validateMppExists(id);
        if(!mppDO.getStus().matches("[0IJ]")){
            throw exception(TEADE_COMMON_ERROR,"请购计划目前不允许操作删除！请刷新");
        }
        // 删除
        mppMapper.deleteById(id);

        // 删除子表
        deleteMppDetailByMainId(id);
    }

    private MppDO validateMppExists(Long id) {
        MppDO mppDO = mppMapper.selectById(id);
        if ( mppDO== null) {
            throw exception(MPP_NOT_EXISTS);
        }
        return mppDO;
    }

    @Override
    public MppDO getMpp(Long id) {
        MppDO mppDO = mppMapper.selectById(id);
        if(StringUtils.isNotEmpty(mppDO.getExecutionPeriodStart())&&StringUtils.isNotEmpty(mppDO.getExecutionPeriodEnd())){
            mppDO.setExecutionPeriod(new String[]{mppDO.getExecutionPeriodStart(),mppDO.getExecutionPeriodEnd()});
        }
        return mppDO;
    }

    @Override
    public PageResult<MppDO> getMppPage(MppPageReqVO pageReqVO) {
        return mppMapper.selectPage(pageReqVO);
    }

    @Override
    public Map<String,Object> calcCale(String reqno){
        return mppDetailMapper.calcCale(reqno);
    }
    // ==================== 子表（采购计划明细） ====================
    public static final String REQITEMNO ="MPP_REQITEMNO:";
    @Override
    public PageResult<MppDetailDO> getMppDetailPage(PageParam pageReqVO, Long mainId) {
        return mppDetailMapper.selectPage(pageReqVO, mainId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createMppDetail(MppDetailDO mppDetail) {
//        mppDetail.setCreateDate(DateUtil.getDate());
        MppDO mppDO = validateMppExists(mppDetail.getMainId());
        if(!mppDO.getStus().matches("[0IJ]")){
            throw exception(TEADE_COMMON_ERROR,"请购计划目前不允许操作导入！请刷新");
        }
        mppDetail.setMainId(mppDO.getId());
        mppDetail.setReqno(mppDO.getReqno());
        mppDetail.setCompid(String.valueOf(TenantContextHolder.getTenantId()));
        mppDetail.setProject(String.valueOf(getLoginUserTopDeptId()));
        mppDetail.setCreateEmp(getLoginUserNickname());
        mppDetail.setCreateDate(LocalDateTimeUtil.formatNormal(LocalDate.now()));
        if(mppDetail.getPlantallyno()==null) {
            calcUnitprice(mppDetail);
        }
        Supplier<Long> func = ()->{
            try {
                String maxReqitemno = mppDetailMapper.selectMaxReqitemno(mppDetail.getMainId());
                DecimalFormat formatter = new DecimalFormat("0000");
                int iMaxSeq = Integer.parseInt(maxReqitemno);
                mppDetail.setReqitemno(formatter.format(++iMaxSeq));
                mppDetailMapper.insert(mppDetail);
                mppDO.setTotalAmt(calcTotalAmt(mppDO.getId()));
                mppMapper.updateById(mppDO);
                return mppDetail.getId();
            } catch (Exception e) {
                e.printStackTrace();
                return -1L;
            }
        };
        Long lock = redisDistributedLock.lock(REQITEMNO+mppDetail.getMainId(), func);
        if(lock==-1L){
            throw exception(TEADE_COMMON_ERROR,"新增失败");
        }
        return lock;
    }

    @Override
    public void updateMppDetail(MppDetailDO mppDetail) {
        MppDO mppDO = validateMppExists(mppDetail.getMainId());
        if(!mppDO.getStus().matches("[0IJ]")){
            throw exception(TEADE_COMMON_ERROR,"请购计划目前不允许操作导入！请刷新");
        }
        // 校验存在
        validateMppDetailExists(mppDetail.getId());
//        mppDetail.setUpdateDate(DateUtil.getDate());
        mppDetail.setUpdateEmp(getLoginUserNickname());
        mppDetail.setUpdateDate(LocalDateTimeUtil.formatNormal(LocalDate.now()));
        if(mppDetail.getPlantallyno()==null) {
            calcUnitprice(mppDetail);
        }
        // 更新
        mppDetailMapper.updateById(mppDetail);
//        MppDO mppDO = new MppDO();
//        mppDO.setId(mppDetail.getMainId());
        mppDO.setTotalAmt(calcTotalAmt(mppDetail.getMainId()));
        mppMapper.updateById(mppDO);
    }

    private void calcUnitprice(MppDetailDO mppDetail) {
        BigDecimal unitPrice=null;
        try {
            unitPrice = wmsTradeService.getUnitPrice(mppDetail.getProject(), mppDetail.getCompid(), mppDetail.getMatrlno(), "");
        } catch (Exception e) {
        }
        if (unitPrice!=null&&unitPrice.compareTo(BigDecimal.ZERO)>0){
            mppDetail.setUnitprice(unitPrice);
        }
        mppDetail.setAmt(mppDetail.getQty().multiply(mppDetail.getUnitprice()).setScale(2, RoundingMode.HALF_UP));
    }

    private BigDecimal calcTotalAmt(Long mainId){
        PageParam pageReqVO = new MppPageReqVO();
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        PageResult<MppDetailDO> mppDetailPage = this.getMppDetailPage(pageReqVO, mainId);
        AtomicReference<BigDecimal> totalAmt = new AtomicReference<>(BigDecimal.ZERO);
        if (mppDetailPage.getTotal()>0) {
            mppDetailPage.getList().forEach(d->totalAmt.set(totalAmt.get().add(d.getAmt())));
        }
        return totalAmt.get();
    }

    @Override
    public void deleteMppDetail(Long id) {
        // 校验存在
        MppDetailDO detailDO = validateMppDetailExists(id);
        MppDO mppDO = validateMppExists(detailDO.getMainId());
        if(!mppDO.getStus().matches("[0IJ]")){
            throw exception(TEADE_COMMON_ERROR,"请购计划目前不允许操作删除！请刷新");
        }
        // 删除
        mppDetailMapper.deleteById(id);
    }

    @Override
    public MppDetailDO getMppDetail(Long id) {
        return mppDetailMapper.selectById(id);
    }


    private MppDetailDO validateMppDetailExists(Long id) {
        MppDetailDO detailDO = mppDetailMapper.selectById(id);
        if (detailDO == null) {
            throw exception(MPP_DETAIL_NOT_EXISTS);
        }
        return detailDO;
    }
    private void validateMppDetailExistsByMppId(Long id) {
        if (mppDetailMapper.selectCount(MppDetailDO::getMainId,id)==0) {
            throw exception(MPP_DETAIL_NOT_EXISTS);
        }
    }

    private void deleteMppDetailByMainId(Long mainId) {
        mppDetailMapper.deleteByMainId(mainId);
    }

    @Resource
    private BpmProcessInstanceApi processInstanceApi;
    @Override
    public Long createFlow(Long userId, MppSaveReqVO createReqVO) {
        Long mppId = createReqVO.getId();
        MppDO mppDO = validateMppExists(mppId);
        if(!mppDO.getStus().matches("[0IJ]")&&!"H".equals(mppDO.getStus())){
            throw exception(TEADE_COMMON_ERROR,"请购计划目前不允许操作！请刷新");
        }
        validateMppDetailExistsByMppId(mppId);
        MppDO mppDO1=new MppDO();
        mppDO1.setId(mppId);
        mppDO1.setUpdateDate(LocalDateTimeUtil.formatNormal(LocalDate.now()));
        mppDO1.setUpdateEmpno(getLoginUserNickname());
        if("03".equals(mppDO.getPurtype())&&!"3".equals(mppDO.getTendertype()) ||"H".equals(mppDO.getStus())){
            mppDO1.setStus("M");//原料采购 询价走流程   有些不需要审批
            mppDO1.setFlowstus("2");
            mppMapper.updateById(mppDO1);
            return mppId;
        }
        // 发起 BPM 流程
//        Map<String, Object> processInstanceVariables = new HashMap<>();
//        processInstanceVariables.put("day", day);
        Map<String, List<Long>> startUserSelectAssignees = new HashMap<>();
//        startUserSelectAssignees.put("Activity_0tx2pkp", Arrays.asList(1L));
        createReqVO.setStartUserSelectAssignees(startUserSelectAssignees);
        BpmProcessInstanceCreateReqDTO bpmProcessInstanceCreateReqDTO=new BpmProcessInstanceCreateReqDTO();
        bpmProcessInstanceCreateReqDTO.setProcessDefinitionKey(MPP_PROCESS_KEY);
        bpmProcessInstanceCreateReqDTO.setBusinessKey(String.valueOf(mppId));
        bpmProcessInstanceCreateReqDTO.setStartUserSelectAssignees(createReqVO.getStartUserSelectAssignees());
        String processInstanceId = processInstanceApi.createProcessInstance(userId, bpmProcessInstanceCreateReqDTO).getCheckedData();
//        String processInstanceId = processInstanceApi.createProcessInstance(userId,
//                        new BpmProcessInstanceCreateReqDTO().setProcessDefinitionKey(MPP_PROCESS_KEY)
////                        .setVariables(processInstanceVariables)
//                                .setBusinessKey(String.valueOf(mppId))
//                                .setStartUserSelectAssignees(createReqVO.getStartUserSelectAssignees())
//                )
//                .getCheckedData();

        // 将工作流的编号，更新到 OA 请假单中
//        MppDO mppDO1=new MppDO();
//        mppDO1.setId(mppId);
//        mppDO1.setUpdateDate(LocalDateTimeUtil.formatNormal(LocalDate.now()));
//        mppDO1.setUpdateEmpno(getLoginUserNickname());
        mppDO1.setProcessInstanceId(processInstanceId);
        mppDO1.setStus("G");
        mppDO1.setFlowstus("-");
        mppMapper.updateById(mppDO1);
//        mppMapper.updateById(new MppDO().setId(mppId)
//                .setProcessInstanceId(processInstanceId)
//                .setUpdateDate(LocalDateTimeUtil.formatNormal(LocalDate.now()))
//                .setUpdateEmpno(getLoginUserNickname())
//                .setStus("G")
//                .setFlowstus("-"));
//        leaveMapper.updateById(new BpmOALeaveDO().setId(leave.getId()).setProcessInstanceId(processInstanceId));
        return mppId;
    }
    @Override
    public Long cancelFlow(MppSaveReqVO createReqVO){
        Long mppId = createReqVO.getId();
        MppDO mppDO = validateMppExists(mppId);
        if(mppDO.getStus().matches("[0IJ]")){
            throw exception(TEADE_COMMON_ERROR,"请购计划目前不允许操作！请刷新");
        }
        MppDetailDO reqPoSum = mppDetailMapper.getReqPoSum(createReqVO.getReqno(), false);
        if(reqPoSum!=null&& NumberUtils.nullToZero(reqPoSum.getQty()).compareTo(BigDecimal.ZERO)!=0){
            throw exception(TEADE_COMMON_ERROR,"请购计划已经生成合同，无法取消");
        }
        MppDO mppDO1=new MppDO();
        mppDO1.setId(mppId);
        mppDO1.setUpdateDate(LocalDateTimeUtil.formatNormal(LocalDate.now()));
        mppDO1.setUpdateEmpno(getLoginUserNickname());
        mppDO1.setStus("0");
        mppDO1.setFlowstus("-");
        mppMapper.updateById(mppDO1);
        return mppId;
    }
    @Override
    public void updateMppMsg(MppSaveReqVO updateReqVO) {
        // 校验存在
        MppDO mppDO = validateMppExists(updateReqVO.getId());
        boolean isAllow = false;
        if(StringUtils.isNotEmpty(updateReqVO.getFileExtend())&&!mppDO.getFileExtend().equals(updateReqVO.getFileExtend())) {
            mppDO.setFileExtend(updateReqVO.getFileExtend());
            isAllow=true;
        }
        if(isAllow){
            mppMapper.updateById(mppDO);
        }
    }
    @Override
    public void updateMppStatus(MppDTO mppDTO) {
        MppDO mppDO=new MppDO();
        mppDO.setUpdateDate(LocalDateTimeUtil.formatNormal(LocalDate.now()));
        mppDO.setUpdateEmpno(getLoginUserNickname());
        mppDO.setFlowstus(mppDTO.getAuditType());
        mppDO.setStus(mppDTO.getStatus());
        mppDO.setProcessInstanceId(mppDTO.getId());
        mppMapper.updateMppStatus(mppDO);
//        mppMapper.updateMppStatus(new MppDO()
//                .setUpdateDate(LocalDateTimeUtil.formatNormal(LocalDate.now()))
//                .setUpdateEmpno(getLoginUserNickname())
//                .setFlowstus(mppDTO.getAuditType())
//                .setStus(mppDTO.getStatus())
//                .setProcessInstanceId(mppDTO.getId()));
    }

    @Override
    public MppDetailImportRespVO importUserList(List<MppDetailImportVO> list, Long mainId, Boolean updateSupport) {
        // 1 参数校验
        if (CollUtil.isEmpty(list)) {
            throw exception(MPP_IMPORT_LIST_IS_EMPTY);
        }
        MppDO mppDO = mppMapper.selectById(mainId);
        if(mppDO==null){
            throw exception(TEADE_COMMON_ERROR,"请购计划不存在！");
        }
        if(!mppDO.getStus().matches("[0IJ]")){
            throw exception(TEADE_COMMON_ERROR,"请购计划目前不允许操作导入！请刷新");
        }
        List<MppDetailDO> mppDetailDOS = mppDetailMapper.selectList(MppDetailDO::getMainId, mainId);
        // 2. 遍历，逐个创建 or 更新
        MppDetailImportRespVO respVO = MppDetailImportRespVO.builder()
                .createDetails(new ArrayList<>()).updateDetails(new ArrayList<>())
                .failureDetails(new LinkedHashMap<>()).build();
        Set<String> matrlnos= new HashSet<>();
        list.forEach(detail -> {
            String seq = String.valueOf(detail.getSeq());
            if(detail.getMatrlno()==null||detail.getMatrlno().isEmpty()){
                respVO.getFailureDetails().put(seq,"料号不能为空");
                return;
            }
            if(detail.getUseDate()==null||detail.getUseDate().isEmpty()||!detail.getUseDate().matches("\\d{4}-\\d{2}-\\d{2}")){
                respVO.getFailureDetails().put(seq,"需用日期不能为空或格式不为yyyy-MM-dd");
                return;
            }
            if (detail.getQty()==null||BigDecimal.ZERO.compareTo(detail.getQty())==0) {
                respVO.getFailureDetails().put(seq,"数量不存在或不能为0");
                return;
            }
            if (detail.getUnitprice()==null||BigDecimal.ZERO.compareTo(detail.getUnitprice())==0) {
                respVO.getFailureDetails().put(seq,"单价不存在或不能为0");
                return;
            }
            MppDetailDO mppDetail = BeanUtils.toBean(detail, MppDetailDO.class);
            mppDetail.setMainId(mainId);
            try {
                if (matrlnos.contains(mppDetail.getMatrlno())) {
                    respVO.getFailureDetails().put(seq,"料号重复导入");
                    return ;
                }
                matrlnos.add(mppDetail.getMatrlno());
                Optional<MppDetailDO> first = mppDetailDOS.stream().filter(m -> m.getMatrlno().equals(mppDetail.getMatrlno())).findFirst();
                if (first.isPresent()) {
                    if(updateSupport){
                        mppDetail.setId(first.get().getId());
                        updateMppDetail(mppDetail);
                        respVO.getUpdateDetails().add(seq);
                        return ;
                    }
                    respVO.getFailureDetails().put(seq,"料号已存在");
                    return ;
                }

                MaterialDto materialDto = new MaterialDto();
                materialDto.setMatrlno(mppDetail.getMatrlno());
                PageResult<MaterialRespDto> materialDtoPageResult = basicApi.apiMaterialList(materialDto).getCheckedData();
                if (materialDtoPageResult.getTotal()==0) {
                    respVO.getFailureDetails().put(seq,"没有找到料号资料");
                    return;
                }
                MaterialRespDto materialDto2 = materialDtoPageResult.getList().get(0);
                mppDetail.setMatrlno(materialDto2.getMatrlno());
                mppDetail.setCnMdesc(materialDto2.getCnmdesc());
//                mppDetail.setEnMdesc(materialDto.getenMdesc;
                mppDetail.setChNspec(materialDto2.getNmspec());
                mppDetail.setQlty(materialDto2.getQuality());
                mppDetail.setDrawing(materialDto2.getPicno());
                mppDetail.setUnit(materialDto2.getUnitinv());
                createMppDetail(mppDetail);
                respVO.getCreateDetails().add(seq);
            } catch (Exception e){
                e.printStackTrace();
                respVO.getFailureDetails().put(seq,e.getMessage());
            }

        });
        return respVO;
    }

    @Override
    public List<MppDetailDO> selectMppDetailByMainId(HashMap map) {
        return mppMapper.selectMppDetailByMainId(map);
    }

    @Override
    public MppDO selectMppByProcessInstanceId(HashMap map) {
        return mppMapper.selectMppByProcessInstanceId(map);
    }

    @Override
    public PageResult<MppDetailDO> getMppDetailPage2(MppDetailPageReqVO pageReqVO) {
        return mppDetailMapper.selectPage2(pageReqVO);
    }


}

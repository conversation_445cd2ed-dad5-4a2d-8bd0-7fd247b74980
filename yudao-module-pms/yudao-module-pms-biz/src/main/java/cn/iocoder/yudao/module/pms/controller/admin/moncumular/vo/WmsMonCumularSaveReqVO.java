package cn.iocoder.yudao.module.pms.controller.admin.moncumular.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 料号月累计收发新增/修改 Request VO")
@Data
public class WmsMonCumularSaveReqVO {
    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "10060")
    private Long id;

    @Schema(description = "项目号")
    private String project;

    @Schema(description = "公司别", example = "10524")
    private String compId;

    @Schema(description = "料号")
    private String matrlNo;

    @Schema(description = "月累计收发")
    private String yearMo;

    @Schema(description = "品级（备件属性）")
    private String matrlGrade;

    @Schema(description = "品别", example = "1")
    private String inventoryType;

    @Schema(description = "计价类别")
    private String storageNo;

    @Schema(description = "批号")
    private String lotNo;

    @Schema(description = "批次")
    private String batch;

    @Schema(description = "会计科目")
    private String acctCode;

    @Schema(description = "月末库存量")
    private BigDecimal endQty;

    @Schema(description = "月末库存湿重")
    private BigDecimal endWetQty;

    @Schema(description = "月末库存金额")
    private BigDecimal endAmt;

    @Schema(description = "月初库存量")
    private BigDecimal moBeginQty;

    @Schema(description = "月初库存金额")
    private BigDecimal moBeginAmt;

    @Schema(description = "月累积入库量")
    private BigDecimal moAccuRecvQty;

    @Schema(description = "月累积入库金额")
    private BigDecimal moAccuRecvAmt;

    @Schema(description = "月累积出库量")
    private BigDecimal moAccuIssuQty;

    @Schema(description = "月累积出库金额")
    private BigDecimal moAccuIssuAmt;

    @Schema(description = "月调整入库量")
    private BigDecimal moAccuAdjRecvQty;

    @Schema(description = "月调整入库金额")
    private BigDecimal moAccuAdjRecvAmt;

    @Schema(description = "月调整出库量")
    private BigDecimal moAccuAdjIssuQty;

    @Schema(description = "月调整出库金额")
    private BigDecimal moAccuAdjIssuAmt;

    @Schema(description = "年初库存量")
    private BigDecimal yrBeginQty;

    @Schema(description = "年初库存金额")
    private BigDecimal yrBeginAmt;

    @Schema(description = "年累积入库量")
    private BigDecimal yrAccuRecvQty;

    @Schema(description = "年累积入库金额")
    private BigDecimal yrAccuRecvAmt;

    @Schema(description = "年累积出库量")
    private BigDecimal yrAccuIssuQty;

    @Schema(description = "年累积出库金额")
    private BigDecimal yrAccuIssuAmt;

    @Schema(description = "年调整入库量")
    private BigDecimal yrAccuAdjRecvQty;

    @Schema(description = "年调整入库金额")
    private BigDecimal yrAccuAdjRecvAmt;

    @Schema(description = "年调整出库量")
    private BigDecimal yrAccuAdjIssuQty;

    @Schema(description = "年调整出库金额")
    private BigDecimal yrAccuAdjIssuAmt;

    @Schema(description = "月加权平均价", example = "6794")
    private BigDecimal unitPrice;

    @Schema(description = "序号")
    private String sequenceNo;

    @Schema(description = "状态")
    private String stus;

    @Schema(description = "备用字段")
    private String fields1;

    @Schema(description = "备用字段")
    private String fields2;

    @Schema(description = "备用字段")
    private String fields3;

    @Schema(description = "备用字段")
    private String fields4;

    @Schema(description = "备用字段")
    private String fields5;

    @Schema(description = "月初库存湿重量")
    private BigDecimal moBeginWetQty;

    @Schema(description = "月累积入库湿重量")
    private BigDecimal moAccuRecvWetQty;

    @Schema(description = "月累积出库湿重量")
    private BigDecimal moAccuIssuWetQty;

    @Schema(description = "月调整入库湿重量")
    private BigDecimal moAccuAdjRecvWetQty;

    @Schema(description = "月调整出库湿重量")
    private BigDecimal moAccuAdjIssuWetQty;

    @Schema(description = "年初库存湿重量")
    private BigDecimal yrBeginWetQty;

    @Schema(description = "年累积入库湿重量")
    private BigDecimal yrAccuRecvWetQty;

    @Schema(description = "年累积出库湿重量")
    private BigDecimal yrAccuIssuWetQty;

    @Schema(description = "年调整入库湿重量")
    private BigDecimal yrAccuAdjRecvWetQty;

    @Schema(description = "年调整出库湿重量")
    private BigDecimal yrAccuAdjIssuWetQty;

    @Schema(description = "月初库存湿重金额")
    private BigDecimal moBeginWetAmt;

    @Schema(description = "月累积入库湿重金额")
    private BigDecimal moAccuRecvWetAmt;

    @Schema(description = "月累积出库湿重金额")
    private BigDecimal moAccuIssuWetAmt;

    @Schema(description = "月调整入库湿重金额")
    private BigDecimal moAccuAdjRecvWetAmt;

    @Schema(description = "月调整出库湿重金额")
    private BigDecimal moAccuAdjIssuWetAmt;

    @Schema(description = "年初库存湿重金额")
    private BigDecimal yrBeginWetAmt;

    @Schema(description = "年累积入库湿重金额")
    private BigDecimal yrAccuRecvWetAmt;

    @Schema(description = "年累积出库湿重金额")
    private BigDecimal yrAccuIssuWetAmt;

    @Schema(description = "年调整入库湿重金额")
    private BigDecimal yrAccuAdjRecvWetAmt;

    @Schema(description = "年调整出库湿重金额")
    private BigDecimal yrAccuAdjIssuWetAmt;

}
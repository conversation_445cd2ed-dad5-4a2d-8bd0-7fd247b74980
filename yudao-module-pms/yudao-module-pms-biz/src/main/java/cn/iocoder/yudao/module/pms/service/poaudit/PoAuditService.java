package cn.iocoder.yudao.module.pms.service.poaudit;


import javax.validation.*;
import cn.iocoder.yudao.module.pms.api.poaudit.dto.PmsTaskSaveReqDTO;
import cn.iocoder.yudao.module.pms.controller.admin.poaudit.vo.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.poaudit.PoAuditDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 合同审批 Service 接口
 *
 * <AUTHOR>
 */
public interface PoAuditService {

    /**
     * 创建合同审批
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPoAudit(@Valid PoAuditSaveReqVO createReqVO);

    /**
     * 更新请假申请的状态
     *
     * @param id     编号
     * @param status 结果
     */
    void updatePmsAuditStatus(PmsTaskSaveReqDTO pmsTaskSaveVO);

    /**
     * 获得合同审批
     *
     * @param id 编号
     * @return 合同审批
     */
    PoAuditDO getPoAudit(Long id);

    /**
     * 获得合同审批分页
     *
     * @param pageReqVO 分页查询
     * @return 合同审批分页
     */
    PageResult<PoAuditDO> getPoAuditPage(PoAuditPageReqVO pageReqVO);

}
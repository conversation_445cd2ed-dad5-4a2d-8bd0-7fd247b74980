package cn.iocoder.yudao.module.pms.controller.admin.monthlyindex;

import cn.iocoder.yudao.module.pms.controller.admin.mpp.vo.MppPageReqVO;
import cn.iocoder.yudao.module.pms.controller.admin.mrp.vo.MrpDetailImportRespVO;
import cn.iocoder.yudao.module.pms.controller.admin.mrp.vo.MrpDetailImportVO;
import cn.iocoder.yudao.module.pms.dal.dataobject.mpp.MppDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.mpp.MppDetailDO;
import cn.iocoder.yudao.module.system.api.dept.dto.DeptRespDTO;
import com.alibaba.fastjson.JSON;
import com.sun.org.apache.xpath.internal.operations.Bool;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.io.FileNotFoundException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.io.IOException;
import java.util.concurrent.atomic.AtomicLong;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;
import static cn.iocoder.yudao.module.pms.enums.ErrorCodeConstants.TEADE_COMMON_ERROR;

import cn.iocoder.yudao.module.pms.controller.admin.monthlyindex.vo.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.monthlyindex.MonthlyIndexDO;
import cn.iocoder.yudao.module.pms.service.monthlyindex.MonthlyIndexService;
import org.springframework.web.multipart.MultipartFile;

@Tag(name = "管理后台 - 日月指数")
@RestController
@RequestMapping("/pms/monthly-index")
@Validated
public class MonthlyIndexController {

    @Resource
    private MonthlyIndexService monthlyIndexService;

    @PostMapping("/create")
    @Operation(summary = "创建日月指数")
    @PreAuthorize("@ss.hasPermission('pms:monthly-index:create')")
    public CommonResult<Long> createMonthlyIndex(@Valid @RequestBody MonthlyIndexSaveReqVO createReqVO) {
        return success(monthlyIndexService.createMonthlyIndex(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新日月指数")
    @PreAuthorize("@ss.hasPermission('pms:monthly-index:update')")
    public CommonResult<Boolean> updateMonthlyIndex(@Valid @RequestBody MonthlyIndexSaveReqVO updateReqVO) {
        monthlyIndexService.updateMonthlyIndex(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除日月指数")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:monthly-index:delete')")
    public CommonResult<Boolean> deleteMonthlyIndex(@RequestParam("id") Long id) {
        monthlyIndexService.deleteMonthlyIndex(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得日月指数")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:monthly-index:query')")
    public CommonResult<MonthlyIndexRespVO> getMonthlyIndex(@RequestParam("id") Long id,@RequestParam(value = "cmt",required = false) String cmt) {
        MonthlyIndexDO monthlyIndex = monthlyIndexService.getMonthlyIndex(id,cmt);
        return success(BeanUtils.toBean(monthlyIndex, MonthlyIndexRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得日月指数分页")
    @PreAuthorize("@ss.hasPermission('pms:monthly-index:query')")
    public CommonResult<MonthLyIndexRespSVO> getMonthlyIndexPage(@Valid MonthlyIndexPageReqVO pageReqVO) {
        PageResult<MonthlyIndexDO> pageResult = monthlyIndexService.getMonthlyIndexPage(pageReqVO);
        PageResult<MonthlyIndexRespVO> page = BeanUtils.toBean(pageResult, MonthlyIndexRespVO.class);
        MonthLyIndexRespSVO respSVO = new MonthLyIndexRespSVO();
        if("Y".equalsIgnoreCase(pageReqVO.getIsAvg())) {
            respSVO.setAvgIndex(BeanUtils.toBean(monthlyIndexService.calcAvg(pageReqVO), MonthlyIndexRespVO.class));
        }
        respSVO.setPage(page);
        return success(respSVO);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出日月指数 Excel")
    @PreAuthorize("@ss.hasPermission('pms:monthly-index:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportMonthlyIndexExcel(@Valid MonthlyIndexPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MonthlyIndexDO> list = monthlyIndexService.getMonthlyIndexPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "日月指数.xls", "数据", MonthlyIndexRespVO.class,
                        BeanUtils.toBean(list, MonthlyIndexRespVO.class));
    }

    @GetMapping ("/get-import-template")
    @Operation(summary = "获取模板")
    @PreAuthorize("@ss.hasPermission('pms:monthly-index:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void getTemplateMrpDetail(HttpServletResponse response,@RequestParam("titles") String titleStr) throws Exception {
//        List<MonthlyIndexImportVO> list = Arrays.asList(
//                MonthlyIndexImportVO.builder().matrlno("220201010002").issueqty(BigDecimal.TEN).unitprice(BigDecimal.TEN).build(),
//                MonthlyIndexImportVO.builder().matrlno("220201010001").issueqty(BigDecimal.TEN).unitprice(BigDecimal.TEN).build()
//        );
//        // 输出
//        ExcelUtils.write(response, "需求计划明细导入模板.xls", "明细列表", MrpDetailImportVO.class, list);
        if (StringUtils.isEmpty(titleStr)) {
            throw  exception(TEADE_COMMON_ERROR,"标题丢失！！");
        }
        Map<String,Object> map = new HashMap<>();
        List<MonthlyIndexImportVO> list = new ArrayList<>();
        MonthlyIndexImportVO importVO = new MonthlyIndexImportVO();
        importVO.setSeq(1L);
        importVO.setRecordDate(LocalDate.now());
        String[] titles = titleStr.split(",");
        for (int i = 0; i < 20; i++) { //注意跟表一致
            if(i<titles.length) {
                map.put("index" + (i + 1), titles[i]);
                MonthlyIndexImportVO.class.getMethod("setIndexValue"+(i + 1),BigDecimal.class).invoke(importVO,BigDecimal.TEN);
            } else {
                map.put("index" + (i + 1), "");
                MonthlyIndexImportVO.class.getMethod("setIndexValue"+(i + 1),BigDecimal.class).invoke(importVO,(BigDecimal)null);
            }
        }
        list.add(importVO);
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        org.springframework.core.io.Resource resource = resolver.getResource("/templates/monthIndex.xlsx");
        if (!resource.exists()) {
            throw new FileNotFoundException("文件未找到: " + resource.getURI());
        }
        System.out.println(map);
        System.out.println(JSON.toJSONString(list));
        // 导出 Excel
        ExcelUtils.write(response, "日指标数据.xlsx", "日指标数据", resource.getInputStream(), map, list);
    }
    @PostMapping ("/import")
    @Operation(summary = "更新需求计划明细")
    @PreAuthorize("@ss.hasPermission('pms:monthly-index:export')")
    @ApiAccessLog(operateType = IMPORT)
    public CommonResult<MonthlyIndexImportRespVO> importMrpDetail(@RequestParam("file") MultipartFile file,
                                                                  @RequestParam("titles") String titleStr,
                                                               @RequestParam(value = "updateSupport", required = false, defaultValue = "false") Boolean updateSupport) throws IOException {

        List<MonthlyIndexImportVO> list=ExcelUtils.read(file, MonthlyIndexImportVO.class);
        return success(monthlyIndexService.importMonthlyIndexList(list, titleStr, updateSupport));
    }
    @PostMapping ("/genMonthIndex")
    @Operation(summary = "生成月指标")
    @PreAuthorize("@ss.hasPermission('pms:monthly-index:export')")
    @ApiAccessLog(operateType = IMPORT)
    public CommonResult<Boolean> genMonthIndex(@RequestBody MonthlyIndexPageReqVO reqVO) throws IOException {
        if(reqVO.getRecordDate()==null||reqVO.getRecordDate().length!=2){
            throw exception(TEADE_COMMON_ERROR,"日期范围不能为空或值不够");
        }
        LocalDate firstDayOfMonth = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastDayOfMonth = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        if(!firstDayOfMonth.isEqual(reqVO.getRecordDate()[0]) || !lastDayOfMonth.isEqual(reqVO.getRecordDate()[1])){
            throw exception(TEADE_COMMON_ERROR,"日期范围不是一个整月");
        }

        monthlyIndexService.genMonthIndex(reqVO);
        return success(true);
    }
}
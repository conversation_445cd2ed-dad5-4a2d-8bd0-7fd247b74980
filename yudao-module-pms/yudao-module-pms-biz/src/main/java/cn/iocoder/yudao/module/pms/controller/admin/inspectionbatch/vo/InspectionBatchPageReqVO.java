package cn.iocoder.yudao.module.pms.controller.admin.inspectionbatch.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 验收批次档分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InspectionBatchPageReqVO extends PageParam {

    @Schema(description = "项目")
    private String project;

    @Schema(description = "公司别", example = "7747")
    private String compid;

    @Schema(description = "检验批号")
    private String chkno;

    @Schema(description = "手工批号")
    private String manualchkno;

    @Schema(description = "检验类别", example = "2")
    private String chktype;

    @Schema(description = "类别", example = "2")
    private String type;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "合同号")
    private String contractno;
    @Schema(description = "供应商代码")
    private String supplierno;

    @Schema(description = "供应商", example = "张三")
    private String supplyname;

    @Schema(description = "检验人")
    private String tester;

    @Schema(description = "主检人")
    private String mjtester;

    @Schema(description = "发站")
    private String sendstation;

    @Schema(description = "发站说明")
    private String sendstationdesc;

    @Schema(description = "原料料号")
    private String matrlno;

    @Schema(description = "是否已品质验收")
    private String isqtyacpt;

    @Schema(description = "是否已重量验收")
    private String ismasacpt;

    @Schema(description = "品质检验结果")
    private String qtyover;

    @Schema(description = "化验室")
    private String testroom;

    @Schema(description = "检验日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] testdate;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
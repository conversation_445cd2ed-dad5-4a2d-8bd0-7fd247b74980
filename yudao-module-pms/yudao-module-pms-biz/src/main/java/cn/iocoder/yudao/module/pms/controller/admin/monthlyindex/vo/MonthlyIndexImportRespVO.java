package cn.iocoder.yudao.module.pms.controller.admin.monthlyindex.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Schema(description = "管理后台 - 需求计划明细导入 Response VO")
@Data
@Builder
public class MonthlyIndexImportRespVO {

    @Schema(description = "创建成功的日指标数据数组", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> createDetails;

    @Schema(description = "更新成功的日指标数据数组", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> updateDetails;

    @Schema(description = "导入失败的日指标数据集合，key 为需求计划明细，value 为失败原因", requiredMode = Schema.RequiredMode.REQUIRED)
    private Map<String, String> failureDetails;

}

package cn.iocoder.yudao.module.pms.dal.mysql.mpp;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.pms.controller.admin.mpp.vo.ProcurementCollectionItemPageReqVO;
import cn.iocoder.yudao.module.pms.dal.dataobject.mpp.ProcurementCollectionItemDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 集采物料明细子表 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProcurementCollectionItemMapper extends BaseMapperX<ProcurementCollectionItemDO> {

    default PageResult<ProcurementCollectionItemDO> selectPage(ProcurementCollectionItemPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProcurementCollectionItemDO>()
                .eqIfPresent(ProcurementCollectionItemDO::getCollectionId, reqVO.getCollectionId())
                .likeIfPresent(ProcurementCollectionItemDO::getMaterialNo, reqVO.getMaterialNo())
                .likeIfPresent(ProcurementCollectionItemDO::getMaterialNameCn, reqVO.getMaterialNameCn())
                .eqIfPresent(ProcurementCollectionItemDO::getCategoryCode, reqVO.getCategoryCode())
                .eqIfPresent(ProcurementCollectionItemDO::getStatusCode, reqVO.getStatusCode())
                .orderByAsc(ProcurementCollectionItemDO::getSortOrder)
                .orderByDesc(ProcurementCollectionItemDO::getId));
    }

    default List<ProcurementCollectionItemDO> selectListByCollectionId(Long collectionId) {
        return selectList(new LambdaQueryWrapperX<ProcurementCollectionItemDO>()
                .eq(ProcurementCollectionItemDO::getCollectionId, collectionId)
                .orderByAsc(ProcurementCollectionItemDO::getSortOrder)
                .orderByDesc(ProcurementCollectionItemDO::getId));
    }

    default List<ProcurementCollectionItemDO> selectListByCollectionIdAndVersion(Long collectionId, String version) {
        return selectList(new LambdaQueryWrapperX<ProcurementCollectionItemDO>()
                .eq(ProcurementCollectionItemDO::getCollectionId, collectionId)
                .eq(ProcurementCollectionItemDO::getItemVersion, version)
                .orderByAsc(ProcurementCollectionItemDO::getSortOrder)
                .orderByDesc(ProcurementCollectionItemDO::getId));
    }

    default void deleteByCollectionId(Long collectionId) {
        delete(new LambdaQueryWrapperX<ProcurementCollectionItemDO>()
                .eq(ProcurementCollectionItemDO::getCollectionId, collectionId));
    }
}

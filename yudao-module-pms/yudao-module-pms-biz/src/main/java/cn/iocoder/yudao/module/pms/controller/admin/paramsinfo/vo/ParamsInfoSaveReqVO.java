package cn.iocoder.yudao.module.pms.controller.admin.paramsinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 配置信息新增/修改 Request VO")
@Data
public class ParamsInfoSaveReqVO {
    private Long id;
    private String paramId;

    @Schema(description = "第B栏内容")
    private String valuea;

    @Schema(description = "第A栏内容")
    private String valueb;

    @Schema(description = "第C栏内容")
    private String valuec;

    @Schema(description = "第D栏内容")
    private String valued;

    @Schema(description = "第E栏内容")
    private String valuee;

    @Schema(description = "第F栏内容")
    private String valuef;

    @Schema(description = "第G栏内容")
    private String valueg;

    @Schema(description = "第H栏内容")
    private String valueh;

    @Schema(description = "第I栏内容")
    private String valuei;

    @Schema(description = "第J栏内容")
    private String valuej;

    @Schema(description = "第K栏内容")
    private String valuek;

    @Schema(description = "第L栏内容")
    private String valuel;

}
package cn.iocoder.yudao.module.pms;

import cn.iocoder.yudao.module.bpm.api.task.BpmProcessInstanceApi;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * 项目的启动类

 * <AUTHOR>
 */
@SpringBootApplication
@EnableFeignClients(clients = {BpmProcessInstanceApi.class})
public class PmsServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(PmsServerApplication.class, args);
    }

}

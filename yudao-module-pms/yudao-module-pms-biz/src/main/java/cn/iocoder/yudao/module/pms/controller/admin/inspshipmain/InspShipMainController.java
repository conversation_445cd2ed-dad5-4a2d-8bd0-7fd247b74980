package cn.iocoder.yudao.module.pms.controller.admin.inspshipmain;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.pms.controller.admin.inspshipmain.vo.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.inspshipmain.InspShipMainDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.inspectionbatch.InspectionBatchPhysicalDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.inspshipmain.InspectionBatchPhysiCal2DO;
import cn.iocoder.yudao.module.pms.service.inspshipmain.InspShipMainService;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 船运验收入储主档")
@RestController
@RequestMapping("/pms/insp-ship-main")
@Validated
public class InspShipMainController {

    @Resource
    private InspShipMainService inspShipMainService;

    @PostMapping("/create")
    @Operation(summary = "创建船运验收入储主档")
    @PreAuthorize("@ss.hasPermission('pms:insp-ship-main:create')")
    public CommonResult<Long> createInspShipMain(@Valid @RequestBody InspShipMainSaveReqVO createReqVO) {
        return success(inspShipMainService.createInspShipMain(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新船运验收入储主档")
    @PreAuthorize("@ss.hasPermission('pms:insp-ship-main:update')")
    public CommonResult<Boolean> updateInspShipMain(@Valid @RequestBody InspShipMainSaveReqVO updateReqVO) {
        inspShipMainService.updateInspShipMain(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除船运验收入储主档")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:insp-ship-main:delete')")
    public CommonResult<Boolean> deleteInspShipMain(@RequestParam("id") Long id) {
        inspShipMainService.deleteInspShipMain(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得船运验收入储主档")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:insp-ship-main:query')")
    public CommonResult<InspShipMainRespVO> getInspShipMain(@RequestParam("id") Long id) {
        InspShipMainDO inspShipMain = inspShipMainService.getInspShipMain(id);
        return success(BeanUtils.toBean(inspShipMain, InspShipMainRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得船运验收入储主档分页")
    @PreAuthorize("@ss.hasPermission('pms:insp-ship-main:query')")
    public CommonResult<PageResult<InspShipMainRespVO>> getInspShipMainPage(@Valid InspShipMainPageReqVO pageReqVO) {
        PageResult<InspShipMainDO> pageResult = inspShipMainService.getInspShipMainPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InspShipMainRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出船运验收入储主档 Excel")
    @PreAuthorize("@ss.hasPermission('pms:insp-ship-main:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInspShipMainExcel(@Valid InspShipMainPageReqVO pageReqVO,
                                        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InspShipMainDO> list = inspShipMainService.getInspShipMainPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "船运验收入储主档.xls", "数据", InspShipMainRespVO.class,
                BeanUtils.toBean(list, InspShipMainRespVO.class));
    }

    // ==================== 子表（验收批次化物性档） ====================

    @GetMapping("/inspection-batch-physical/page")
    @Operation(summary = "获得验收批次化物性档分页")
    @Parameter(name = "mainId", description = "主档id")
    @PreAuthorize("@ss.hasPermission('pms:insp-ship-main:query')")
    public CommonResult<PageResult<InspectionBatchPhysicalDO>> getInspectionBatchPhysicalPage(PageParam pageReqVO, @RequestParam("mainId") Long mainId, @RequestParam(value = "chkType", required = false) String type) {
        return success(inspShipMainService.getInspectionBatchPhysicalPage(pageReqVO, mainId, type));
    }

    @PostMapping("/inspection-batch-physical/create")
    @Operation(summary = "创建验收批次化物性档")
    @PreAuthorize("@ss.hasPermission('pms:insp-ship-main:create')")
    public CommonResult<Long> createInspectionBatchPhysical(@Valid @RequestBody InspectionBatchPhysicalDO inspectionBatchPhysical) {
        return success(inspShipMainService.createInspectionBatchPhysical(inspectionBatchPhysical));
    }

    @PostMapping("/inspection-batch-physical/update")
    @Operation(summary = "更新验收批次化物性档")
    @PreAuthorize("@ss.hasPermission('pms:insp-ship-main:update')")
    public CommonResult<Boolean> updateInspectionBatchPhysical(@Valid @RequestBody InspectionBatchPhysicalDO inspectionBatchPhysical) {
        inspShipMainService.updateInspectionBatchPhysical(inspectionBatchPhysical);
        return success(true);
    }

    @DeleteMapping("/inspection-batch-physical/delete")
    @Parameter(name = "id", description = "编号", required = true)
    @Operation(summary = "删除验收批次化物性档")
    @PreAuthorize("@ss.hasPermission('pms:insp-ship-main:delete')")
    public CommonResult<Boolean> deleteInspectionBatchPhysical(@RequestParam("id") Long id) {
        inspShipMainService.deleteInspectionBatchPhysical(id);
        return success(true);
    }

    @GetMapping("/inspection-batch-physical/get")
    @Operation(summary = "获得验收批次化物性档")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:insp-ship-main:query')")
    public CommonResult<InspectionBatchPhysicalDO> getInspectionBatchPhysical(@RequestParam("id") Long id) {
        return success(inspShipMainService.getInspectionBatchPhysical(id));
    }

    @GetMapping("/inspection-batch-physical/getCOAH2OByPono")
    @Operation(summary = "获得COA中H2O")
    @Parameter(name = "pono", description = "pono", required = true)
    public CommonResult<InspectionBatchPhysicalDO> getCOAH2OByPono(@RequestParam("pono") String pono) {
        return success(inspShipMainService.getCOAH2OByPono(pono));
    }

    // ==================== 子表（CIQ指标） ====================

    @GetMapping("/inspection-batch-physi-cal2/page")
    @Operation(summary = "获得CIQ指标分页")
    @Parameter(name = "parentId", description = "父id")
    @PreAuthorize("@ss.hasPermission('pms:insp-ship-main:query')")
    public CommonResult<PageResult<InspectionBatchPhysiCal2DO>> getInspectionBatchPhysiCal2Page(PageParam pageReqVO,
                                                                                                @RequestParam("parentId") Long parentId) {
        return success(inspShipMainService.getInspectionBatchPhysiCal2Page(pageReqVO, parentId));
    }

    @PostMapping("/inspection-batch-physi-cal2/create")
    @Operation(summary = "创建CIQ指标")
    @PreAuthorize("@ss.hasPermission('pms:insp-ship-main:create')")
    public CommonResult<Long> createInspectionBatchPhysiCal2(@Valid @RequestBody InspectionBatchPhysiCal2DO inspectionBatchPhysiCal2) {
        return success(inspShipMainService.createInspectionBatchPhysiCal2(inspectionBatchPhysiCal2));
    }

    @PostMapping("/inspection-batch-physi-cal2/update")
    @Operation(summary = "更新CIQ指标")
    @PreAuthorize("@ss.hasPermission('pms:insp-ship-main:update')")
    public CommonResult<Boolean> updateInspectionBatchPhysiCal2(@Valid @RequestBody InspectionBatchPhysiCal2DO inspectionBatchPhysiCal2) {
        inspShipMainService.updateInspectionBatchPhysiCal2(inspectionBatchPhysiCal2);
        return success(true);
    }

    @DeleteMapping("/inspection-batch-physi-cal2/delete")
    @Parameter(name = "id", description = "编号", required = true)
    @Operation(summary = "删除CIQ指标")
    @PreAuthorize("@ss.hasPermission('pms:insp-ship-main:delete')")
    public CommonResult<Boolean> deleteInspectionBatchPhysiCal2(@RequestParam("id") Long id) {
        inspShipMainService.deleteInspectionBatchPhysiCal2(id);
        return success(true);
    }

    @GetMapping("/inspection-batch-physi-cal2/get")
    @Operation(summary = "获得CIQ指标")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:insp-ship-main:query')")
    public CommonResult<InspectionBatchPhysiCal2DO> getInspectionBatchPhysiCal2(@RequestParam("id") Long id) {
        return success(inspShipMainService.getInspectionBatchPhysiCal2(id));
    }

    @GetMapping("/confirm")
    @Operation(summary = "确认")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:insp-ship-main:update')")
    public CommonResult<Boolean> confirmInspShipMain(@RequestParam("id") Long id, @RequestParam("flag") String flag) {
        inspShipMainService.confirmDoMain(id, flag);
        return success(true);
    }

    @GetMapping("/cancelConfirm")
    @Operation(summary = "取消")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:insp-ship-main:update')")
    public CommonResult<Boolean> cancelConfirmInspShipMain(@RequestParam("id") Long id, @RequestParam("flag") String flag) {
        inspShipMainService.cancelConfirmDoMain(id, flag);
        return success(true);
    }

    @GetMapping("/monCalPage")
    @Operation(summary = "获得船运验收入储主档分页")
    @PreAuthorize("@ss.hasPermission('pms:insp-ship-main:query')")
    public CommonResult<PageResult<StoreMonthlyIndexImportVO>> getInspShipMonCalPage(@Valid InspShipMainPageReqVO pageReqVO) {
        PageResult<StoreMonthlyIndexImportVO> pageResult = inspShipMainService.getInspShipMonCalPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StoreMonthlyIndexImportVO.class));
    }

    @GetMapping("/export-store-mon-cal-excel")
    @Operation(summary = "导出月入库数据CIQ/COA指标 Excel")
    @PreAuthorize("@ss.hasPermission('pms:insp-ship-main:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInspShipMonCalExcel(@Valid InspShipMainPageReqVO pageReqVO,
                                          HttpServletResponse response) throws Exception {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        String[] titles = inspShipMainService.getchkItemArr(pageReqVO.getYearMon());
        List<StoreMonthlyIndexImportVO> list = inspShipMainService.getInspShipMonCalPage(pageReqVO).getList();
        Map<String, Object> map = new HashMap<>();
        List<StoreMonthlyIndexImportVO> list1 = new ArrayList<>();
        Long l = 1L;
        for (StoreMonthlyIndexImportVO inspShipMainDO : list) {
            List<BigDecimal> indexValues = Arrays.asList(
                    null, // 占位，使索引从1开始
                    inspShipMainDO.getIndexValue1() == null ? null : inspShipMainDO.getIndexValue1().setScale(2, BigDecimal.ROUND_HALF_UP),
                    inspShipMainDO.getIndexValue2() == null ? null : inspShipMainDO.getIndexValue2().setScale(2, BigDecimal.ROUND_HALF_UP),
                    inspShipMainDO.getIndexValue3() == null ? null : inspShipMainDO.getIndexValue3().setScale(2, BigDecimal.ROUND_HALF_UP),
                    inspShipMainDO.getIndexValue4() == null ? null : inspShipMainDO.getIndexValue4().setScale(2, BigDecimal.ROUND_HALF_UP),
                    inspShipMainDO.getIndexValue5() == null ? null : inspShipMainDO.getIndexValue5().setScale(2, BigDecimal.ROUND_HALF_UP),
                    inspShipMainDO.getIndexValue6() == null ? null : inspShipMainDO.getIndexValue6().setScale(2, BigDecimal.ROUND_HALF_UP),
                    inspShipMainDO.getIndexValue7() == null ? null : inspShipMainDO.getIndexValue7().setScale(2, BigDecimal.ROUND_HALF_UP),
                    inspShipMainDO.getIndexValue8() == null ? null : inspShipMainDO.getIndexValue8().setScale(2, BigDecimal.ROUND_HALF_UP),
                    inspShipMainDO.getIndexValue9() == null ? null : inspShipMainDO.getIndexValue9().setScale(2, BigDecimal.ROUND_HALF_UP),
                    inspShipMainDO.getIndexValue10() == null ? null : inspShipMainDO.getIndexValue10().setScale(2, BigDecimal.ROUND_HALF_UP),
                    inspShipMainDO.getIndexValue11() == null ? null : inspShipMainDO.getIndexValue11().setScale(2, BigDecimal.ROUND_HALF_UP),
                    inspShipMainDO.getIndexValue12() == null ? null : inspShipMainDO.getIndexValue12().setScale(2, BigDecimal.ROUND_HALF_UP),
                    inspShipMainDO.getIndexValue13() == null ? null : inspShipMainDO.getIndexValue13().setScale(2, BigDecimal.ROUND_HALF_UP),
                    inspShipMainDO.getIndexValue14() == null ? null : inspShipMainDO.getIndexValue14().setScale(2, BigDecimal.ROUND_HALF_UP),
                    inspShipMainDO.getIndexValue15() == null ? null : inspShipMainDO.getIndexValue15().setScale(2, BigDecimal.ROUND_HALF_UP),
                    inspShipMainDO.getIndexValue16() == null ? null : inspShipMainDO.getIndexValue16().setScale(2, BigDecimal.ROUND_HALF_UP),
                    inspShipMainDO.getIndexValue17() == null ? null : inspShipMainDO.getIndexValue17().setScale(2, BigDecimal.ROUND_HALF_UP),
                    inspShipMainDO.getIndexValue18() == null ? null : inspShipMainDO.getIndexValue18().setScale(2, BigDecimal.ROUND_HALF_UP),
                    inspShipMainDO.getIndexValue19() == null ? null : inspShipMainDO.getIndexValue19().setScale(2, BigDecimal.ROUND_HALF_UP),
                    inspShipMainDO.getIndexValue20() == null ? null : inspShipMainDO.getIndexValue20().setScale(2, BigDecimal.ROUND_HALF_UP)
            );

            StoreMonthlyIndexImportVO importVO = new StoreMonthlyIndexImportVO();
            importVO.setSeq(l);
            importVO.setContractNo(inspShipMainDO.getContractNo());
            importVO.setMatrlName(inspShipMainDO.getMatrlName());
            String chkType = inspShipMainDO.getChkType();
            String chkTypeStr = null;
            switch (chkType) {
                case "CIQ":
                    chkTypeStr = "商检CIQ";
                    break;
                case "COA":
                    chkTypeStr = "商检COA";
                    break;
                case "S":
                    chkTypeStr = "入厂指标";
                    break;
            }
            importVO.setChkType(chkTypeStr);
            //importVO.setTransNum(inspShipMainDO.getTransNum());
            for (int i = 0; i < 20; i++) { //注意跟表一致
                if (i < titles.length) {
                    map.put("index" + (i + 1), titles[i]);
                    StoreMonthlyIndexImportVO.class.getMethod("setIndexValue" + (i + 1), BigDecimal.class).invoke(importVO, indexValues.get(i + 1));
                } else {
                    map.put("index" + (i + 1), "");
                    StoreMonthlyIndexImportVO.class.getMethod("setIndexValue" + (i + 1), BigDecimal.class).invoke(importVO, (BigDecimal) null);
                }
            }
            list1.add(importVO);
            l++;
        }

        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        org.springframework.core.io.Resource resource = resolver.getResource("/templates/storeMonCalIndex.xlsx");
        if (!resource.exists()) {
            throw new FileNotFoundException("文件未找到: " + resource.getURI());
        }
        System.out.println(map);
        System.out.println(JSON.toJSONString(list));
        // 导出 Excel
        ExcelUtils.write(response, "月入库数据指标.xlsx", "月入库数据指标", resource.getInputStream(), map, list1);
    }

    @GetMapping("/dictColumn")
    @Operation(summary = "获取字段字典")
    @PreAuthorize("@ss.hasPermission('pms:insp-ship-main:query')")
    public CommonResult<List<Map<String, Object>>> getDictColumn(@Valid InspShipMainPageReqVO pageReqVO) {
        String[] arr = inspShipMainService.getchkItemArr(pageReqVO.getYearMon());
        List<Map<String, Object>> indexList = new ArrayList<>();
        for (int i = 0; i < arr.length; i++) {
            Map<String, Object> map = new HashMap<>();
            map.put("label", arr[i]);
            map.put("value", i + 1);
            indexList.add(map);
        }
        return success(indexList);
    }

    @GetMapping("/cleanUp")
    @Operation(summary = "确认")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:insp-ship-main:update')")
    public CommonResult<Boolean> cleanUpInspShipMain(@RequestParam("id") Long id) {
        inspShipMainService.cleanUpInspShipMain(id);
        return success(true);
    }

    @GetMapping("/cancelCleanUp")
    @Operation(summary = "取消")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:insp-ship-main:update')")
    public CommonResult<Boolean> cancelCleanUpConfirmInspShipMain(@RequestParam("id") Long id) {
        inspShipMainService.cancelCleanUpConfirmInspShipMain(id);
        return success(true);
    }

    @GetMapping("/getByPo")
    @Operation(summary = "获得船运验收入储主档")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:insp-ship-main:query')")
    public CommonResult<List<InspShipMainRespVO>> getInspShipMainByPo(@RequestParam("pono") String pono) {
        List<InspShipMainDO> res = inspShipMainService.getInspShipMainByPo(pono);
        return success(BeanUtils.toBean(res, InspShipMainRespVO.class));
    }

    @GetMapping("/shipments")
    @Operation(summary = "获得货物发运情况")
    @Parameter(name = "pono", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:insp-ship-main:query')")
    public CommonResult<List<InspShipmentsRespVO>> getShipmentsByPo(@RequestParam("pono") String pono) {
        List<InspShipmentsRespVO> res = inspShipMainService.getShipmentsByPo(pono);
        return success(BeanUtils.toBean(res, InspShipmentsRespVO.class));
    }

}
package cn.iocoder.yudao.module.pms.dal.dataobject.inq;

import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 采购案项次档管理 DO
 *
 * <AUTHOR>
 */
@TableName("pms_inq_detail")
@KeySequence("pms_inq_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InqDetailDO extends BaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * PARENTID
     */
    private Long parentid;
    /**
     * 项目号
     */
    private String project;
    /**
     * 公司别
     */
    private String compId;
    /**
     * 计划行号
     */
    private String mrLineId;
    /**
     * 计划期间
     */
    private String mrPeriod;
    /**
     * 计划类型
     *
     * 枚举 {@link TODO MR_TYPE 对应的类}
     */
    private String mrType;
    /**
     * 物料代码
     */
    private String itemId;
    /**
     * 物料名称
     */
    private String itemName;
    /**
     * 物料计量单位
     *
     * 枚举 {@link TODO stock_unit 对应的类}
     */
    private String itemUom;
    /**
     * 物料类型
     *
     * 枚举 {@link TODO inven_tory_type 对应的类}
     */
    private String itemType;
    /**
     * 物料型号规格
     */
    private String itemModel;
    /**
     * 物料图号
     */
    private String itemChart;
    /**
     * 物料材质
     */
    private String itemTexture;
    /**
     * 单重
     */
    private BigDecimal unitWeight;
    /**
     * 物料短描述
     */
    private String itemDesc;
    /**
     * 标段号
     */
    private String bidId;
    /**
     * 建议标段号
     */
    private String autoBidId;
    /**
     * 计划数量
     */
    private BigDecimal mrApplyQty;
    /**
     * 计划核准数量
     */
    private BigDecimal bidCheckQty;
    /**
     * 预算价
     */
    private BigDecimal budgetPrice;
    /**
     * 核价不含税单价
     */
    private BigDecimal bidNotaxPrice;
    /**
     * 核价不含税金额
     */
    private BigDecimal bidNotaxAmt;
    /**
     * 要求交货期
     */
    private String reqDeliveryDate;
    /**
     * 技术参数
     */
    private String tecParameter;
    /**
     * 品牌
     */
    private String brand;
    /**
     * 备注
     */
    private String remark;
    /**
     * 采购案号
     */
    private String inqId;
    /**
     * 采购案行号
     */
    private String inqLineId;
    /**
     * 项目号
     */
    private String projectNo;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 部门代码
     */
    private String deptId;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 预留字段1 
     */
    private String preText1;
    /**
     * 预留字段2 
     */
    private String preText2;
    /**
     * 预留字段3 
     */
    private String preText3;
    /**
     * 预留字段4 
     */
    private String preText4;
    /**
     * 预留字段5 
     */
    private String preText5;
    /**
     * 预留字段6 
     */
    private String preText6;
    /**
     * 预留字段7 
     */
    private String preText7;
    /**
     * 预留字段8 
     */
    private String preText8;
    /**
     * 预留字段9 
     */
    private String preText9;
    /**
     * 预留字段10 
     */
    private String preText10;
    /**
     * 预留字段11 
     */
    private BigDecimal preNum11;
    /**
     * 预留字段12 
     */
    private BigDecimal preNum12;
    /**
     * 预留字段13 
     */
    private BigDecimal preNum13;
    /**
     * 预留字段14 
     */
    private BigDecimal preNum14;
    /**
     * 预留字段15 
     */
    private BigDecimal preNum15;
    /**
     * 创建者姓名
     */
    private String createEmpNo;
    /**
     * 更新者者姓名
     */
    private String updateEmpNo;

}
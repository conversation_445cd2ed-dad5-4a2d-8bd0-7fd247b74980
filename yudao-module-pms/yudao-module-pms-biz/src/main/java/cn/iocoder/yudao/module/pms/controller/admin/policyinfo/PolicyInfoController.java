package cn.iocoder.yudao.module.pms.controller.admin.policyinfo;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;
import cn.iocoder.yudao.module.pms.controller.admin.policyinfo.vo.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.policyinfo.PolicyInfoDO;
import cn.iocoder.yudao.module.pms.service.policyinfo.PolicyInfoService;

@Tag(name = "管理后台 - 保单基本信息")
@RestController
@RequestMapping("/pms/policy-info")
@Validated
public class PolicyInfoController {

    @Resource
    private PolicyInfoService policyInfoService;

    @PostMapping("/create")
    @Operation(summary = "创建保单基本信息")
    @PreAuthorize("@ss.hasPermission('pms:policy-info:create')")
    public CommonResult<Long> createPolicyInfo(@Valid @RequestBody PolicyInfoSaveReqVO createReqVO) {
        return success(policyInfoService.createPolicyInfo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新保单基本信息")
    @PreAuthorize("@ss.hasPermission('pms:policy-info:update')")
    public CommonResult<Boolean> updatePolicyInfo(@Valid @RequestBody PolicyInfoSaveReqVO updateReqVO) {
        policyInfoService.updatePolicyInfo(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除保单基本信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:policy-info:delete')")
    public CommonResult<Boolean> deletePolicyInfo(@RequestParam("id") Long id) {
        policyInfoService.deletePolicyInfo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得保单基本信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:policy-info:query')")
    public CommonResult<PolicyInfoRespVO> getPolicyInfo(@RequestParam("id") Long id) {
        PolicyInfoDO policyInfo = policyInfoService.getPolicyInfo(id);
        return success(BeanUtils.toBean(policyInfo, PolicyInfoRespVO.class));
    }

    @GetMapping("/getPolicy")
    @Operation(summary = "获得保单基本信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:policy-info:query')")
    public CommonResult<PolicyInfoRespVO> getPolicyInfo(@RequestParam("pono") String pono) {
        PolicyInfoDO policyInfo = policyInfoService.getPolicyInfoByPo(pono);
        return success(BeanUtils.toBean(policyInfo, PolicyInfoRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得保单基本信息分页")
    @PreAuthorize("@ss.hasPermission('pms:policy-info:query')")
    public CommonResult<PageResult<PolicyInfoRespVO>> getPolicyInfoPage(@Valid PolicyInfoPageReqVO pageReqVO) {
        PageResult<PolicyInfoDO> pageResult = policyInfoService.getPolicyInfoPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PolicyInfoRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出保单基本信息 Excel")
    @PreAuthorize("@ss.hasPermission('pms:policy-info:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportPolicyInfoExcel(@Valid PolicyInfoPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<PolicyInfoDO> list = policyInfoService.getPolicyInfoPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "保单基本信息.xls", "数据", PolicyInfoRespVO.class,
                        BeanUtils.toBean(list, PolicyInfoRespVO.class));
    }

}
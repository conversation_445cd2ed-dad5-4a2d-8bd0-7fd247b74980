package cn.iocoder.yudao.module.pms.dal.dataobject.wmstrademain;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 物料交易申请主档 DO
 *
 * <AUTHOR>
 */
@TableName("wms_trade_main")
@KeySequence("wms_trade_main_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WmsTradeMainDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 项目号
     */
    private String project;
    /**
     * 公司别
     */
    private String compId;
    /**
     * 申请单号
     */
    private String issueTallyNo;
    /**
     * 交易种类
     */
    private String issueType;
    /**
     * 用途别
     */
    private String purposeId;
    /**
     * 品别
     */
    private String inventoryType;
    /**
     * 计划交易日
     */
    private String planTranDate;
    /**
     * 申请人职工编号
     */
    private String issueTranEmpNo;
    /**
     * 申请部门
     */
    private String issueTranDeptNo;
    /**
     * 申请日期
     */
    private String issueDate;
    /**
     * 申请人联系电话
     */
    private String issuePhone;
    /**
     * 状况码
     */
    private String stus;
    /**
     * 结案人
     */
    private String finalEmpno;
    /**
     * 结案日期
     */
    private LocalDate finalDate;
    /**
     * 结案时间
     */
    private LocalTime finalTime;
    /**
     * 验收单
     */
    private String tranTallyNo;
    /**
     * 计划单号
     */
    private String planTallyNo;
    /**
     * 原申请单号
     */
    private String oldIssueTallyNo;
    /**
     * 领料单号
     */
    private String showTallyNo;
    /**
     * 关联交易公司别
     */
    private String affilCompId;
    /**
     * 关联交易单号
     */
    private String affilIssueTallyNo;
    /**
     * 关联交易领料单号
     */
    private String affilShowTallyNo;
    /**
     * 盘盈单号
     */
    private String profitTallyNo;
    /**
     * 盘亏单号
     */
    private String lossTallyNo;
    /**
     * 红冲单号
     */
    private String apprvId;
    /**
     * 系统别
     */
    private String systemId;
    /**
     * 程式代码
     */
    private String appId;
    /**
     * 供应商代码
     */
    private String supplierNo;
    /**
     * 合同编号
     */
    private String contractNo;
    /**
     * 成本中心
     */
    private String costCenter;
    /**
     * 工程项目编号
     */
    private String engineerNo;
    /**
     * 工程预算编号
     */
    private String hitBudgetNo;
    /**
     * 工程合同编号
     */
    private String hitContractNo;
    /**
     * 设备编号
     */
    private String equipmentsNo;
    /**
     * 固定资产编号
     */
    private String fixedAssetsNo;
    /**
     * 工单编号
     */
    private String workOrder;
    /**
     * 参数1Id
     */
    private String refIdA;
    /**
     * 备用栏位A
     */
    private String refNoA;
    /**
     * 参数2Id
     */
    private String refIdB;
    /**
     * 备用栏位B
     */
    private String refNoB;
    /**
     * 参数3Id
     */
    private String refIdC;
    /**
     * 备用栏位C
     */
    private String refNoC;
    /**
     * 参数4Id
     */
    private String refIdD;
    /**
     * 备用栏位D
     */
    private String refNoD;
    /**
     * 是否配送
     */
    private String isDeliver;
    /**
     * 配送地点
     */
    private String deliverPlace;
    /**
     * 发料仓库
     */
    private String storeHouse;
    /**
     * 传票号码
     */
    private String vchrNo;
    /**
     * 传票日期
     */
    private String vchrDate;
    /**
     * 备注
     */
    private String remark;
    /**
     * 资料建立人员
     */
    private String createEmpNo;
    /**
     * 资料建立日期
     */
    private String createDate;
    /**
     * 资料异动人员
     */
    private String updateEmpNo;
    /**
     * 资料异动日期
     */
    private String updateDate;
    /**
     * 流程编号
     */
    private String processId;

}
package cn.iocoder.yudao.module.pms.controller.admin.mineralprices;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.math.BigDecimal;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.pms.controller.admin.mineralprices.vo.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.mineralprices.MineralPricesDO;
import cn.iocoder.yudao.module.pms.service.mineralprices.MineralPricesService;

@Tag(name = "管理后台 - 进口矿价格")
@RestController
@RequestMapping("/pms/mineral-prices")
@Validated
public class MineralPricesController {

    @Resource
    private MineralPricesService mineralPricesService;

    @PostMapping("/create")
    @Operation(summary = "创建进口矿价格")
    @PreAuthorize("@ss.hasPermission('pms:mineral-prices:create')")
    public CommonResult<Long> createMineralPrices(@Valid @RequestBody MineralPricesSaveReqVO createReqVO) {
        return success(mineralPricesService.createMineralPrices(createReqVO));
    }
    @PostMapping("/calc/dmtu")
    @Operation(summary = "计算DMTU")
    @PreAuthorize("@ss.hasPermission('pms:mineral-prices:create')")
    public CommonResult<BigDecimal> calcDMTU(@Valid @RequestBody MineralPricesSaveReqVO createReqVO) {
        return success(mineralPricesService.calcDMTU(createReqVO));
    }
    @PostMapping("/calc/amt")
    @Operation(summary = "计算DMTU")
    @PreAuthorize("@ss.hasPermission('pms:mineral-prices:create')")
    public CommonResult<BigDecimal> calcAmt(@Valid @RequestBody MineralPricesSaveReqVO createReqVO) {
        return success(mineralPricesService.calcAmt(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新进口矿价格")
    @PreAuthorize("@ss.hasPermission('pms:mineral-prices:update')")
    public CommonResult<Boolean> updateMineralPrices(@Valid @RequestBody MineralPricesSaveReqVO updateReqVO) {
        mineralPricesService.updateMineralPrices(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除进口矿价格")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:mineral-prices:delete')")
    public CommonResult<Boolean> deleteMineralPrices(@RequestParam("id") Long id) {
        mineralPricesService.deleteMineralPrices(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得进口矿价格")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:mineral-prices:query')")
    public CommonResult<MineralPricesRespVO> getMineralPrices(@RequestParam("id") Long id) {
        MineralPricesDO mineralPrices = mineralPricesService.getMineralPrices(id);
        return success(BeanUtils.toBean(mineralPrices, MineralPricesRespVO.class));
    }
    @GetMapping("/getFormula")
    @PreAuthorize("@ss.hasPermission('pms:mineral-prices:query')")
    public CommonResult<MineralPricesRespVO> getFormula(@RequestParam("variety") String variety) {
        MineralPricesDO mineralPrices = mineralPricesService.getFormula(variety);
        return success(BeanUtils.toBean(mineralPrices, MineralPricesRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得进口矿价格分页")
    @PreAuthorize("@ss.hasPermission('pms:mineral-prices:query')")
    public CommonResult<PageResult<MineralPricesRespVO>> getMineralPricesPage(@Valid MineralPricesPageReqVO pageReqVO) {
        PageResult<MineralPricesDO> pageResult = mineralPricesService.getMineralPricesPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MineralPricesRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出进口矿价格 Excel")
    @PreAuthorize("@ss.hasPermission('pms:mineral-prices:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportMineralPricesExcel(@Valid MineralPricesPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MineralPricesDO> list = mineralPricesService.getMineralPricesPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "进口矿价格.xls", "数据", MineralPricesRespVO.class,
                        BeanUtils.toBean(list, MineralPricesRespVO.class));
    }

}
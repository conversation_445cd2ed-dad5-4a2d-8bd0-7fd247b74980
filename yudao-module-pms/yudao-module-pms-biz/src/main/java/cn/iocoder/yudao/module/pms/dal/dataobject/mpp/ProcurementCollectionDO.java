package cn.iocoder.yudao.module.pms.dal.dataobject.mpp;

import lombok.*;
import java.time.LocalDateTime;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 集采信息主表 DO
 *
 * <AUTHOR>
 */
@TableName("PMS_MPP_procurement_collection")
@KeySequence("PMS_MPP_procurement_collection_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcurementCollectionDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 集采编号
     */
    private String collectionNo;

    /**
     * 公司别
     */
    private String companyCode;

    /**
     * 版本号
     */
    private String versionNo;

    /**
     * 建立人职工编号
     */
    private String creatorEmpNo;

    /**
     * 建立日期
     */
    private LocalDateTime createDate;

    /**
     * 修改人职工编号
     */
    private String updaterEmpNo;

    /**
     * 修改日期
     */
    private LocalDateTime updateDate;

    /**
     * 结案时间
     */
    private LocalDateTime closeTime;

    /**
     * 生效日期
     */
    private LocalDateTime effectiveDate;

    /**
     * 生效状态：0-未生效，1-已生效，2-已失效
     */
    private Integer effectiveStatus;

    /**
     * 备注
     */
    private String remark;
}

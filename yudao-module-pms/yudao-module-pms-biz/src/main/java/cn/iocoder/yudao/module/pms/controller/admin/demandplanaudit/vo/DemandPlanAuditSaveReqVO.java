package cn.iocoder.yudao.module.pms.controller.admin.demandplanaudit.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 需求计划申报工作流关联新增/修改 Request VO")
@Data
public class DemandPlanAuditSaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10698")
    private Long id;

    @Schema(description = "需求计划申报表 ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2388")
    @NotNull(message = "需求计划申报表 ID不能为空")
    private Long parentId;

    @Schema(description = "计划行号", example = "15909")
    private String mrLineId;

    @Schema(description = "作业部计划员ID", example = "7665")
    private String applyUserId;

    @Schema(description = "作业部计划员姓名", example = "赵六")
    private String applyUserName;

    @Schema(description = "流程实例的编号", example = "2055")
    private String processInstanceId;

}
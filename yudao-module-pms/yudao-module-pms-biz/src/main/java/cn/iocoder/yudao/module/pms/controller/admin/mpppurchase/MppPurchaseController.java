package cn.iocoder.yudao.module.pms.controller.admin.mpppurchase;

import cn.iocoder.yudao.module.pms.controller.admin.wmstrademain.vo.WmsTradeBatchSaveReqVO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.pms.controller.admin.mpppurchase.vo.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.mpppurchase.MppPurchaseDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.mpppurchase.MppPurchasedDetailDO;
import cn.iocoder.yudao.module.pms.service.mpppurchase.MppPurchaseService;

@Tag(name = "管理后台 - 采购案")
@RestController
@RequestMapping("/pms/mpp-purchase")
@Validated
public class MppPurchaseController {

    @Resource
    private MppPurchaseService mppPurchaseService;

    @PostMapping("/create")
    @Operation(summary = "创建采购案")
    @PreAuthorize("@ss.hasPermission('pms:mpp-purchase:create')")
    public CommonResult<Long> createMppPurchase(@Valid @RequestBody MppPurchaseSaveReqVO createReqVO) {
        return success(mppPurchaseService.createMppPurchase(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新采购案")
    @PreAuthorize("@ss.hasPermission('pms:mpp-purchase:update')")
    public CommonResult<Boolean> updateMppPurchase(@Valid @RequestBody MppPurchaseSaveReqVO updateReqVO) {
        mppPurchaseService.updateMppPurchase(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除采购案")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:mpp-purchase:delete')")
    public CommonResult<Boolean> deleteMppPurchase(@RequestParam("id") Long id) {
        mppPurchaseService.deleteMppPurchase(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得采购案")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:mpp-purchase:query')")
    public CommonResult<MppPurchaseRespVO> getMppPurchase(@RequestParam("id") Long id) {
        MppPurchaseDO mppPurchase = mppPurchaseService.getMppPurchase(id);
        return success(BeanUtils.toBean(mppPurchase, MppPurchaseRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得采购案分页")
    @PreAuthorize("@ss.hasPermission('pms:mpp-purchase:query')")
    public CommonResult<PageResult<MppPurchaseRespVO>> getMppPurchasePage(@Valid MppPurchasePageReqVO pageReqVO) {
        PageResult<MppPurchaseDO> pageResult = mppPurchaseService.getMppPurchasePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MppPurchaseRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出采购案 Excel")
    @PreAuthorize("@ss.hasPermission('pms:mpp-purchase:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportMppPurchaseExcel(@Valid MppPurchasePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MppPurchaseDO> list = mppPurchaseService.getMppPurchasePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "采购案.xls", "数据", MppPurchaseRespVO.class,
                        BeanUtils.toBean(list, MppPurchaseRespVO.class));
    }

    // ==================== 子表（采购案明细） ====================

    @GetMapping("/mpp-purchased-detail/page")
    @Operation(summary = "获得采购案明细分页")
    @Parameter(name = "purchaseId", description = "采购案主表id")
    @PreAuthorize("@ss.hasPermission('pms:mpp-purchase:query')")
    public CommonResult<PageResult<MppPurchasedDetailDO>> getMppPurchasedDetailPage(PageParam pageReqVO,
                                                                                        @RequestParam("purchaseId") Long purchaseId) {
        return success(mppPurchaseService.getMppPurchasedDetailPage(pageReqVO, purchaseId));
    }

    @GetMapping("/mpp-purchased-detail/page2")
    @Operation(summary = "获得采购案明细分页")
    @Parameter(name = "purchaseId", description = "采购案主表id")
    @PreAuthorize("@ss.hasPermission('pms:mpp-purchase:query')")
    public CommonResult<PageResult<MppPurchasedDetailDO>> getMppPurchasedDetailPage2(PageParam pageReqVO,
                                                                                    @RequestParam("purno") String purno) {
        return success(mppPurchaseService.getMppPurchasedDetailPage2(pageReqVO, purno));
    }

    @PostMapping("/mpp-purchased-detail/create")
    @Operation(summary = "创建采购案明细")
    @PreAuthorize("@ss.hasPermission('pms:mpp-purchase:create')")
    public CommonResult<Long> createMppPurchasedDetail(@Valid @RequestBody MppPurchasedDetailDO mppPurchasedDetail) {
        return success(mppPurchaseService.createMppPurchasedDetail(mppPurchasedDetail));
    }

    @PutMapping("/mpp-purchased-detail/update")
    @Operation(summary = "更新采购案明细")
    @PreAuthorize("@ss.hasPermission('pms:mpp-purchase:update')")
    public CommonResult<Boolean> updateMppPurchasedDetail(@Valid @RequestBody MppPurchasedDetailDO mppPurchasedDetail) {
        mppPurchaseService.updateMppPurchasedDetail(mppPurchasedDetail);
        return success(true);
    }

    @DeleteMapping("/mpp-purchased-detail/delete")
    @Parameter(name = "id", description = "编号", required = true)
    @Operation(summary = "删除采购案明细")
    @PreAuthorize("@ss.hasPermission('pms:mpp-purchase:delete')")
    public CommonResult<Boolean> deleteMppPurchasedDetail(@RequestParam("id") Long id) {
        mppPurchaseService.deleteMppPurchasedDetail(id);
        return success(true);
    }

	@GetMapping("/mpp-purchased-detail/get")
	@Operation(summary = "获得采购案明细")
	@Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:mpp-purchase:query')")
	public CommonResult<MppPurchasedDetailDO> getMppPurchasedDetail(@RequestParam("id") Long id) {
	    return success(mppPurchaseService.getMppPurchasedDetail(id));
	}



}
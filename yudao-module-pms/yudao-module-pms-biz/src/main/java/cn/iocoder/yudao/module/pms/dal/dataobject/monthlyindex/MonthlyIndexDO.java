package cn.iocoder.yudao.module.pms.dal.dataobject.monthlyindex;

import lombok.*;

import java.time.LocalDate;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 日月指数 DO
 *
 * <AUTHOR>
 */
@TableName("daily_monthly_index")
@KeySequence("daily_monthly_index_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MonthlyIndexDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 记录日期
     */
    private LocalDate recordDate;
    /**
     * 指数类型(1:日指数,2:月指数)
     */
    private Integer indexType;
    /**
     * 指数类型名称
     */
    private String typeName;
    /**
     * 日期开始
     */
    private LocalDate startDate;
    /**
     * 日期结束
     */
    private LocalDate endDate;
    /**
     * 指数值1
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal indexValue1;
    /**
     * 指数值2
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal indexValue2;
    /**
     * 指数值3
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal indexValue3;
    /**
     * 指数值4
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal indexValue4;
    /**
     * 指数值5
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal indexValue5;
    /**
     * 指数值6
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal indexValue6;
    /**
     * 指数值7
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal indexValue7;
    /**
     * 指数值8
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal indexValue8;
    /**
     * 指数值9
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal indexValue9;
    /**
     * 指数值10
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal indexValue10;
    /**
     * 指数值11
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal indexValue11;
    /**
     * 指数值12
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal indexValue12;
    /**
     * 指数值13
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal indexValue13;
    /**
     * 指数值14
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal indexValue14;
    /**
     * 指数值15
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal indexValue15;
    /**
     * 指数值16
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal indexValue16;
    /**
     * 指数值17
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal indexValue17;
    /**
     * 指数值18
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal indexValue18;
    /**
     * 指数值19
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal indexValue19;
    /**
     * 指数值20
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal indexValue20;
    /**
     * 月指数参考值
     */
    private String backup1;
    /**
     * 备用字段2
     */
    private String backup2;
    /**
     * 备用字段3
     */
    private String backup3;
    /**
     * 备用字段4
     */
    private String backup4;
    /**
     * 备用字段5
     */
    private String backup5;
    /**
     * 备用字段6
     */
    private String backup6;
    /**
     * 备用字段7
     */
    private String backup7;
    /**
     * 备用字段8
     */
    private String backup8;
    /**
     * 备用字段9
     */
    private String backup9;
    /**
     * 备用字段10
     */
    private String backup10;

}
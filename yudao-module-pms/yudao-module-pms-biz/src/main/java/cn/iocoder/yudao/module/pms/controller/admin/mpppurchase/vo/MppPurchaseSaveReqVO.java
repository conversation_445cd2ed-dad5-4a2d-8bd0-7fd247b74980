package cn.iocoder.yudao.module.pms.controller.admin.mpppurchase.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import cn.iocoder.yudao.module.pms.dal.dataobject.mpppurchase.MppPurchasedDetailDO;

@Schema(description = "管理后台 - 采购案新增/修改 Request VO")
@Data
public class MppPurchaseSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "10272")
    private Long id;

    @Schema(description = "项目")
    private String project;

    @Schema(description = "公司代码", example = "515")
    private String compid;

    @Schema(description = "采购案号")
    private String purno;

    @Schema(description = "采购案别")
    private String purcode;

    @Schema(description = "承办人单位")
    private String respdept;

    @Schema(description = "承办人职工")
    private String respEmpno;

    @Schema(description = "承办人Email")
    private String respemail;

    @Schema(description = "承办人手机号码")
    private String respmobile;

    @Schema(description = "承办人电话")
    private String resptel;

    @Schema(description = "承办人传真")
    private String respfax;

    @Schema(description = "接办日期")
    private String acceptDate;

    @Schema(description = "取消日期")
    private String cancelDate;

    @Schema(description = "查询授权群组")
    private String qrygrp;

    @Schema(description = "归档日期")
    private String closeDate;

    @Schema(description = "判定者")
    private String decemplno;

    @Schema(description = "是否议比价")
    private String isquot;

    @Schema(description = "采购案状态")
    private String stus;

    @Schema(description = "采购案备注", example = "你说的对")
    private String purmemo;

    @Schema(description = "招标方式", example = "2")
    private String tendertype;

    @Schema(description = "请购类别", example = "2")
    private String purtype;

    @Schema(description = "长约编号")
    private String contractno;

    @Schema(description = "委托单号")
    private String precatno;

    @Schema(description = "投标范围")
    private String tendarea;

    @Schema(description = "招标方式", example = "1")
    private String tendtype;

    @Schema(description = "业务类别", example = "1")
    private String fromtype;

    @Schema(description = "业务类别细分类", example = "2")
    private String fromltype;

    @Schema(description = "拟订开标日期")
    private String intendDate;

    @Schema(description = "工程预算编号")
    private String budgetno;

    @Schema(description = "是否招标")
    private String istend;

    @Schema(description = "评委会成员构成", example = "2")
    private String empnotype;

    @Schema(description = "是否含税")
    private String istax;

    @Schema(description = "税率")
    private BigDecimal taxrate;

    @Schema(description = "评标方法", example = "1")
    private String pbtype;

    @Schema(description = "付款方式", example = "2")
    private String paytype;

    @Schema(description = "付款形式", example = "1")
    private String lbtype;

    @Schema(description = "定标方式", example = "2")
    private String winbidtype;

    @Schema(description = "是否专家定标")
    private String expertdecision;

    @Schema(description = "限价方式", example = "2")
    private String maxpricetype;

    @Schema(description = "付款形式")
    private String payfrom;

    @Schema(description = "委托类别(品别大类)", example = "2")
    private String inventorytype;

    @Schema(description = "委托类别细分类", example = "2")
    private String inventoryltype;

    @Schema(description = "打包号")
    private String baleno;

    @Schema(description = "新增人员")
    private String createEmpno;

    @Schema(description = "新增日期")
    private String createDate;

    @Schema(description = "修改人员")
    private String updateEmpno;

    @Schema(description = "修改日期")
    private String updateDate;

}
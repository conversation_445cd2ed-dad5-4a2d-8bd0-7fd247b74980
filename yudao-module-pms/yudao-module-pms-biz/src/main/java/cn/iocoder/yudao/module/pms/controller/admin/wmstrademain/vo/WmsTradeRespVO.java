package cn.iocoder.yudao.module.pms.controller.admin.wmstrademain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

@Schema(description = "管理后台 - 物料交易申请主档 Response VO")
@Data
@ExcelIgnoreUnannotated
public class WmsTradeRespVO {

    @Schema(description = "编号")
    private Long id;
    
    @Schema(description = "父id")
    private Long parentId;

     @Schema(description = "项目号")
    private String project;
     
    @Schema(description = "公司别")
    private String compId;

    @Schema(description = "申请单号")
    private String issueTallyNo;

    @Schema(description = "申请单位")
    private String issueDeptNo;

    @Schema(description = "申请日期")
    private String issueDate;

     @Schema(description = "申请时间")
    private LocalTime issueTime;

     @Schema(description = "申请人")
    private String issueEmpNo;

    @Schema(description = "账务代码")
    private String purposeId;
    /**
     * 系统别
     */
    @Schema(description = "")
    private String systemId;
    /**
     * 是否贸易
     */
    @Schema(description = "")
    private String isMy;
    /**
     * 交易种类
     */
    @Schema(description = "")
    private String issueType;
    /**
     * 入库单据类别
     */
    @Schema(description = "")
    private String inspType;
    /**
     * 验收单
     */
    @Schema(description = "")
    private String tranTallyNo;
    /**
     * 移库出库单号
     */
    @Schema(description = "")
    private String tranOutTallyNo;
    /**
     * 检验批号
     */
    @Schema(description = "")
    private String chkNo;
    /**
     * 流水序号
     */
    @Schema(description = "")
    private String seqNo;
    /**
     * 磅单号
     */
    @Schema(description = "")
    private String wgtListNo;
    /**
     * 计划单号
     */
    @Schema(description = "")
    private String planTallyNo;
    /**
     * 品别
     */
    @Schema(description = "")
    private String inventoryType;
    /**
     * 计划交易日
     */
    @Schema(description = "")
    private String planTranDate;
    /**
     * 申请人职工编号
     */
    @Schema(description = "")
    private String issueTranEmpNo;
    /**
     * 申请部门
     */
    @Schema(description = "")
    private String issueTranDeptNo;
    /**
     * 申请人联系电话
     */
    @Schema(description = "")
    private String issuePhone;
    /**
     * 原申请单号
     */
    @Schema(description = "")
    private String oldIssueTallyNo;
    /**
     * 程式代码
     */
    @Schema(description = "")
    private String appId;
    /**
     * 是否配送
     */
    @Schema(description = "")
    private String isDeliver;
    /**
     * 配送地点
     */
    @Schema(description = "")
    private String deliverPlace;
    /**
     * 发料仓库
     */
    @Schema(description = "")
    private String storeHouse;
    /**
     * 传票号码
     */
    @Schema(description = "")
    private String vchrNo;
    /**
     * 手工检验批号
     */
    @Schema(description = "")
    private String manualChkNo;
    /**
     * 供应商代码
     */
    @Schema(description = "")
    private String supplierNo;
    /**
     * 合同编号
     */
    @Schema(description = "")
    private String contractNo;
    /**
     * 成本中心
     */
    @Schema(description = "")
    private String costCenter;
    /**
     * 产线号码
     */
    @Schema(description = "")
    private String prodLineNo;
    /**
     * 预算编号
     */
    @Schema(description = "")
    private String budgetNo;
    /**
     * 还料厂商
     */
    @Schema(description = "")
    private String backSupplierNo;
    /**
     * 销售合同号
     */
    @Schema(description = "")
    private String saleContractNo;
    /**
     * 红冲单号
     */
    @Schema(description = "")
    private String apprvId;
    /**
     * 原料料号
     */
    @Schema(description = "")
    private String matrlNo;
    /**
     * 合同版次
     */
    @Schema(description = "")
    private String pover;
    /**
     * 化验水
     */
    @Schema(description = "")
    private BigDecimal chkWater;
    /**
     * 批号
     */
    @Schema(description = "")
    private String lotNo;
    /**
     * 储位代码
     */
    @Schema(description = "")
    private String stgNo;
    /**
     * 发料单位
     */
    @Schema(description = "")
    private String sendDeptNo;
    /**
     * 发料储位代码
     */
    @Schema(description = "")
    private String sendStgNo;
    /**
     * 发料日期
     */
    @Schema(description = "")
    private String sendDate;
    /**
     * 发料时间
     */
    @Schema(description = "")
    private String sendTime;
    /**
     * 收料单位
     */
    @Schema(description = "")
    private String acptDeptNo;
    /**
     * 收料储位代码
     */
    @Schema(description = "")
    private String acptStgNo;
    /**
     * 收料日期
     */
    @Schema(description = "")
    private String acptDate;
    /**
     * 收料时间
     */
    @Schema(description = "")
    private String acptTime;
    /**
     * 过磅时刻
     */
    @Schema(description = "")
    private String loadDateTime;
    /**
     * 结案人
     */
    @Schema(description = "")
    private String finalEmpNo;
    /**
     * 结案日期
     */
    @Schema(description = "")
    private LocalDate finalDate;
    /**
     * 结案时间
     */
    @Schema(description = "")
    private LocalTime finalTime;
    /**
     * 传票日期
     */
    @Schema(description = "")
    private LocalDate vchrDate;
    /**
     * 格式代码
     */
    @Schema(description = "")
    private String formId;
    /**
     * 年月
     */
    @Schema(description = "")
    private String yearMo;
    /**
     * 检斤数量
     */
    @Schema(description = "")
    private BigDecimal scaleNum;
    /**
     * 交易数量
     */
    @Schema(description = "")
    private BigDecimal transNum;
    /**
     * 交易金额
     */
    @Schema(description = "")
    private BigDecimal transAmt;
    /**
     * 耗用数量
     */
    @Schema(description = "")
    private BigDecimal usedNum;
    /**
     * 分摊数量
     */
    @Schema(description = "")
    private BigDecimal sharedNum;
    /**
     * 外售金额
     */
    @Schema(description = "")
    private BigDecimal salesAmt;
    /**
     * 运杂费金额
     */
    @Schema(description = "")
    private BigDecimal blendAmt;
    /**
     * 品质验收状态
     */
    @Schema(description = "")
    private String isQtyAcpt;
    /**
     * 运输方式
     */
    @Schema(description = "")
    private String carrierType;
    /**
     * 验收方式
     */
    @Schema(description = "")
    private String acptType;
    /**
     * 检验样
     */
    @Schema(description = "")
    private String qtyAcptType;
    /**
     * 发站
     */
    @Schema(description = "")
    private String sendStation;
    /**
     * 交易状态
     */
    @Schema(description = "")
    private String stus;
    /**
     * 炉堆号/钢卷号
     */
    @Schema(description = "")
    private String heatNo;
    /**
     * 会计科目
     */
    @Schema(description = "")
    private String acctCode;
    /**
     * 会计科目
     */
    @Schema(description = "")
    private String acctCodeDr;
    /**
     * 成本科目
     */
    @Schema(description = "")
    private String costCode;
    /**
     * 是否抛成本系统
     */
    @Schema(description = "")
    private String isThrowAc;
    /**
     * 接收公司
     */
    @Schema(description = "")
    private String affilCompId;
    /**
     * 关联交易申请单号
     */
    @Schema(description = "")
    private String affilIssueTallyNo;

    /**
     * 车号
     */
    @Schema(description = "")
    private String carNo;
    /**
     * 车数
     */
    @Schema(description = "")
    private String carNum;
    /**
     * 损耗类别
     */
    @Schema(description = "")
    private String lossType;
    /**
     * 变入料号
     */
    @Schema(description = "")
    private String aftAdjustMatrlNo;
    /**
     * 称量室
     */
    @Schema(description = "")
    private String scaleRoom;
    /**
     * 备注
     */
    @Schema(description = "")
    private String remark;

    /**
     * 内部结算销售单号
     */
    @Schema(description = "")
    private String ssNo;
    /**
     * 新增人员
     */
    @Schema(description = "")
    private String createEmpNo;
    /**
     * 新增日期
     */
    @Schema(description = "")
    private String createDate;
    /**
     * 修改人员
     */
    @Schema(description = "")
    private String updateEmpNo;
    /**
     * 修改日期
     */
    @Schema(description = "")
    private String updateDate;
    /**
     * 修改人B
     */
    @Schema(description = "")
    private String updateEmpNoB;
    /**
     * 修改日期B
     */
    @Schema(description = "")
    private LocalDate updateDateB;
    /**
     * 库存量
     */
    @Schema(description = "")
    private BigDecimal inventoryQty;
    /**
     * 库存金额
     */
    @Schema(description = "")
    private BigDecimal inventoryAmt;
    /**
     * 盘点量
     */
    @Schema(description = "")
    private BigDecimal checkQty;
    /**
     * 盘点金额
     */
    @Schema(description = "")
    private BigDecimal checkAmt;
    /**
     * 库存单位
     */
    @Schema(description = "")
    private String unitInv;
    /**
     * 残值比
     */
    @Schema(description = "")
    private String remnantPer;
    /**
     * 尚末发料量
     */
    @Schema(description = "")
    private BigDecimal accuPreTranQty;
    /**
     * 流程编号
     */
    @Schema(description = "")
    private String processId;

    @Schema(description = "料号中文名称")
    private String matrlname;

    @Schema(description = "规格型号")
    private String spec;

    @Schema(description = "资料建立时间")
    private String createTime;

    @Schema(description = "资料更新时间")
    private String updateTime;

    @Schema(description = "创建人名称")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

}
package cn.iocoder.yudao.module.pms.controller.admin.configstock.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 库存配置管理新增/修改 Request VO")
@Data
public class StockConfigSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "项目号")
    private String project;

    @Schema(description = "公司别")
    private String compid;

    @Schema(description = "物料类别")
    private String inventoryType;

    @Schema(description = "料号")
    private String matrlno;

    @Schema(description = "是否批次")
    private String isBatch;

    @Schema(description = "是否合同")
    private String isPo;

    @Schema(description = "创建时间")
    private LocalDateTime createDate;

    @Schema(description = "创建人")
    private String createEmpno;

    @Schema(description = "创建者")
    private String creator;

}
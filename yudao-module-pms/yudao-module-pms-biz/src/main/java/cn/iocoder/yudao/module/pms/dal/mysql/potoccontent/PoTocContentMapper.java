package cn.iocoder.yudao.module.pms.dal.mysql.potoccontent;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.pms.dal.dataobject.potoccontent.PoTocContentDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.pms.controller.admin.potoccontent.vo.*;

/**
 * 合同条款内容-按行保存 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PoTocContentMapper extends BaseMapperX<PoTocContentDO> {

    default PageResult<PoTocContentDO> selectPage(PoTocContentPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PoTocContentDO>()
                .eqIfPresent(PoTocContentDO::getParentId, reqVO.getParentId())
                .eqIfPresent(PoTocContentDO::getSrlno, reqVO.getSrlno())
                .eqIfPresent(PoTocContentDO::getContent, reqVO.getContent())
                .betweenIfPresent(PoTocContentDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(PoTocContentDO::getPreText1, reqVO.getPreText1())
                .eqIfPresent(PoTocContentDO::getPreText2, reqVO.getPreText2())
                .eqIfPresent(PoTocContentDO::getPreText3, reqVO.getPreText3())
                .eqIfPresent(PoTocContentDO::getPreText4, reqVO.getPreText4())
                .eqIfPresent(PoTocContentDO::getPreText5, reqVO.getPreText5())
                .eqIfPresent(PoTocContentDO::getPreText6, reqVO.getPreText6())
                .eqIfPresent(PoTocContentDO::getPreText7, reqVO.getPreText7())
                .eqIfPresent(PoTocContentDO::getPreText8, reqVO.getPreText8())
                .eqIfPresent(PoTocContentDO::getPreText9, reqVO.getPreText9())
                .eqIfPresent(PoTocContentDO::getPreText10, reqVO.getPreText10())
                .eqIfPresent(PoTocContentDO::getPreNum11, reqVO.getPreNum11())
                .eqIfPresent(PoTocContentDO::getPreNum12, reqVO.getPreNum12())
                .eqIfPresent(PoTocContentDO::getPreNum13, reqVO.getPreNum13())
                .eqIfPresent(PoTocContentDO::getPreNum14, reqVO.getPreNum14())
                .eqIfPresent(PoTocContentDO::getPreNum15, reqVO.getPreNum15())
                .eqIfPresent(PoTocContentDO::getType, reqVO.getType())
                .orderByDesc(PoTocContentDO::getId));
    }

}
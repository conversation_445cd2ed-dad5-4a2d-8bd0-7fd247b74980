package cn.iocoder.yudao.module.pms.controller.admin.wrstradedetail2;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;
import cn.iocoder.yudao.module.pms.controller.admin.wrstradedetail2.vo.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.wrstradedetail2.WrsTradeDetail2DO;
import cn.iocoder.yudao.module.pms.service.wrstradedetail2.WrsTradeDetail2Service;

@Tag(name = "管理后台 - 原料待结算明细")
@RestController
@RequestMapping("/pms/wrs-trade-detail2")
@Validated
public class WrsTradeDetail2Controller {

    @Resource
    private WrsTradeDetail2Service tradeDetail2Service;

    @PostMapping("/create")
    @Operation(summary = "创建原料待结算明细")
    @PreAuthorize("@ss.hasPermission('pms:wrs-trade-detail2:create')")
    public CommonResult<Long> createTradeDetail2(@Valid @RequestBody WrsTradeDetail2SaveReqVO createReqVO) {
        return success(tradeDetail2Service.createTradeDetail2(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新原料待结算明细")
    @PreAuthorize("@ss.hasPermission('pms:wrs-trade-detail2:update')")
    public CommonResult<Boolean> updateTradeDetail2(@Valid @RequestBody WrsTradeDetail2SaveReqVO updateReqVO) {
        tradeDetail2Service.updateTradeDetail2(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除原料待结算明细")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:wrs-trade-detail2:delete')")
    public CommonResult<Boolean> deleteTradeDetail2(@RequestParam("id") Long id) {
        tradeDetail2Service.deleteTradeDetail2(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得原料待结算明细")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:wrs-trade-detail2:query')")
    public CommonResult<WrsTradeDetail2RespVO> getTradeDetail2(@RequestParam("id") Long id) {
        WrsTradeDetail2DO tradeDetail2 = tradeDetail2Service.getTradeDetail2(id);
        return success(BeanUtils.toBean(tradeDetail2, WrsTradeDetail2RespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得原料待结算明细分页")
    @PreAuthorize("@ss.hasPermission('pms:wrs-trade-detail2:query')")
    public CommonResult<PageResult<WrsTradeDetail2RespVO>> getTradeDetail2Page(@Valid WrsTradeDetail2PageReqVO pageReqVO) {
        PageResult<WrsTradeDetail2DO> pageResult = tradeDetail2Service.getTradeDetail2Page(pageReqVO);
        return success(BeanUtils.toBean(pageResult, WrsTradeDetail2RespVO.class));
    }

    @GetMapping("/settlePage")
    @Operation(summary = "获得原料待结算明细分页")
    @PreAuthorize("@ss.hasPermission('pms:wrs-trade-detail2:query')")
    public CommonResult<PageResult<WrsTradeDetail2RespVO>> getTradeDetail2SettlePage(@Valid WrsTradeDetail2PageReqVO pageReqVO) {
        PageResult<WrsTradeDetail2DO> pageResult = tradeDetail2Service.getTradeDetail2SettlePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, WrsTradeDetail2RespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出原料待结算明细 Excel")
    @PreAuthorize("@ss.hasPermission('pms:wrs-trade-detail2:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTradeDetail2Excel(@Valid WrsTradeDetail2PageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<WrsTradeDetail2DO> list = tradeDetail2Service.getTradeDetail2Page(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "原料待结算明细.xls", "数据", WrsTradeDetail2RespVO.class,
                        BeanUtils.toBean(list, WrsTradeDetail2RespVO.class));
    }

}
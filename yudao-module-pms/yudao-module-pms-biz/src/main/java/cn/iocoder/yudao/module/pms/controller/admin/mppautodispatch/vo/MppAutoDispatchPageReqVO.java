package cn.iocoder.yudao.module.pms.controller.admin.mppautodispatch.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 采购计划自动调度规则分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MppAutoDispatchPageReqVO extends PageParam {

    @Schema(description = "物料编号")
    private String matrlno;

    @Schema(description = "调度承办人")
    private String dispatch;

    @Schema(description = "承办人")
    private String respemp;

}
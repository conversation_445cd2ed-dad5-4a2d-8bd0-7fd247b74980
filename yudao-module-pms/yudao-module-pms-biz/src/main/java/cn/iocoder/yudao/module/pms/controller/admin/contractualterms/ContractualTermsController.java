package cn.iocoder.yudao.module.pms.controller.admin.contractualterms;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.pms.controller.admin.contractualterms.vo.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.contractualterms.ContractualTermsDO;
import cn.iocoder.yudao.module.pms.service.contractualterms.ContractualTermsService;

@Tag(name = "管理后台 - 合同条款设定")
@RestController
@RequestMapping("/pms/contractual-terms")
@Validated
public class ContractualTermsController {

    @Resource
    private ContractualTermsService contractualTermsService;

    @PostMapping("/create")
    @Operation(summary = "创建合同条款设定")
    @PreAuthorize("@ss.hasPermission('pms:contractual-terms:create')")
    public CommonResult<Long> createContractualTerms(@Valid @RequestBody ContractualTermsSaveReqVO createReqVO) {
        return success(contractualTermsService.createContractualTerms(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新合同条款设定")
    @PreAuthorize("@ss.hasPermission('pms:contractual-terms:update')")
    public CommonResult<Boolean> updateContractualTerms(@Valid @RequestBody ContractualTermsSaveReqVO updateReqVO) {
        contractualTermsService.updateContractualTerms(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除合同条款设定")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:contractual-terms:delete')")
    public CommonResult<Boolean> deleteContractualTerms(@RequestParam("id") Long id) {
        contractualTermsService.deleteContractualTerms(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得合同条款设定")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:contractual-terms:query')")
    public CommonResult<ContractualTermsRespVO> getContractualTerms(@RequestParam("id") Long id) {
        ContractualTermsDO contractualTerms = contractualTermsService.getContractualTerms(id);
        return success(BeanUtils.toBean(contractualTerms, ContractualTermsRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得合同条款设定分页")
    @PreAuthorize("@ss.hasPermission('pms:contractual-terms:query')")
    public CommonResult<PageResult<ContractualTermsRespVO>> getContractualTermsPage(@Valid ContractualTermsPageReqVO pageReqVO) {
        PageResult<ContractualTermsDO> pageResult = contractualTermsService.getContractualTermsPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ContractualTermsRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出合同条款设定 Excel")
    @PreAuthorize("@ss.hasPermission('pms:contractual-terms:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportContractualTermsExcel(@Valid ContractualTermsPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ContractualTermsDO> list = contractualTermsService.getContractualTermsPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "合同条款设定.xls", "数据", ContractualTermsRespVO.class,
                        BeanUtils.toBean(list, ContractualTermsRespVO.class));
    }

    @GetMapping("/confirm")
    @Operation(summary = "确认合同条款设定")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:contractual-terms:update')")
    public CommonResult<Boolean> confirmContractualTerms(@RequestParam("id") Long id) {
        contractualTermsService.confirmContractualTerms(id);
        return success(Boolean.TRUE);
    }
    @GetMapping("/cancelConfirm")
    @Operation(summary = "取消确认合同条款设定")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:contractual-terms:update')")
    public CommonResult<Boolean> cancelConfirmContractualTerms(@RequestParam("id") Long id) {
        contractualTermsService.cancelConfirmContractualTerms(id);
        return success(Boolean.TRUE);
    }
    @GetMapping("/getAllList")
    @Operation(summary = "获得合同条款设定")
    @PreAuthorize("@ss.hasPermission('pms:contractual-terms:query')")
    public CommonResult<List<ContractualTermsRespVO>> getContractualTermsAll() {
        List<ContractualTermsDO> list = contractualTermsService.getContractualTermsAll();
        return success(BeanUtils.toBean(list, ContractualTermsRespVO.class));
    }

}
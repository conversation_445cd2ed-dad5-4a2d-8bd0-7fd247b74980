package cn.iocoder.yudao.module.pms.service.lcusage;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.date.DateUtil;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.pms.controller.admin.lcusage.vo.LcUsagePageReqVO;
import cn.iocoder.yudao.module.pms.controller.admin.lcusage.vo.LcUsageSaveReqVO;
import cn.iocoder.yudao.module.pms.dal.dataobject.lcusage.LcUsageDO;
import cn.iocoder.yudao.module.pms.dal.mysql.lcusage.LcUsageMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserNickname;
import static cn.iocoder.yudao.module.pms.enums.ErrorCodeConstants.LC_USAGE_NOT_EXISTS;

/**
 * 信用证使用情况 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LcUsageServiceImpl implements LcUsageService {

    @Resource
    private LcUsageMapper lcUsageMapper;

    @Override
    public Long createLcUsage(LcUsageSaveReqVO createReqVO) {
        // 插入
        LcUsageDO lcUsage = BeanUtils.toBean(createReqVO, LcUsageDO.class);
        lcUsage.setCreateEmpNo(getLoginUserNickname());
        lcUsage.setUpdateEmpNo(getLoginUserNickname());
        lcUsage.setCreator(String.valueOf(getLoginUserId()));
        lcUsage.setUpdater(String.valueOf(getLoginUserId()));
        lcUsage.setCreateDate(DateUtil.getDate());
        lcUsage.setUpdateDate(DateUtil.getDate());
        lcUsageMapper.insert(lcUsage);
        // 返回
        return lcUsage.getId();
    }

    @Override
    public void updateLcUsage(LcUsageSaveReqVO updateReqVO) {
        // 校验存在
        validateLcUsageExists(updateReqVO.getId());
        // 更新
        LcUsageDO updateObj = BeanUtils.toBean(updateReqVO, LcUsageDO.class);
        updateObj.setUpdateEmpNo(getLoginUserNickname());
        updateObj.setUpdater(String.valueOf(getLoginUserId()));
        updateObj.setUpdateDate(DateUtil.getDate());
        lcUsageMapper.updateById(updateObj);
    }

    @Override
    public void deleteLcUsage(Long id) {
        // 校验存在
        validateLcUsageExists(id);
        // 删除
        lcUsageMapper.deleteById(id);
    }

    private void validateLcUsageExists(Long id) {
        if (lcUsageMapper.selectById(id) == null) {
            throw exception(LC_USAGE_NOT_EXISTS);
        }
    }

    @Override
    public LcUsageDO getLcUsage(Long id) {
        return lcUsageMapper.selectById(id);
    }

    @Override
    public PageResult<LcUsageDO> getLcUsagePage(LcUsagePageReqVO pageReqVO) {
        return lcUsageMapper.selectPage(pageReqVO);
    }

}
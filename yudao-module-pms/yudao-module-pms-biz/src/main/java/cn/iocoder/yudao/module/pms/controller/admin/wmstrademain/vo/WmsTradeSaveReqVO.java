package cn.iocoder.yudao.module.pms.controller.admin.wmstrademain.vo;

import cn.iocoder.yudao.module.pms.dal.dataobject.wmstrade.WmsTradeDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Collections;
import java.util.List;
import java.util.Set;

@Schema(description = "管理后台 - 交货单明细新增 Request VO")
@Data
public class WmsTradeSaveReqVO {

    @Schema(description = "父id")
    private Long parentId;

    @Schema(description = "交易明细集合")
    private List<WmsTradeDO> items;

}
package cn.iocoder.yudao.module.pms.controller.admin.inspshipmain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false)
@ExcelIgnoreUnannotated
public class StoreMonthlyIndexImportVO {
    @ExcelProperty("序号")
    private Long seq;

    @ExcelProperty("合同号")
    private String contractNo;

    @ExcelProperty("品名")
    private String matrlName;

    @ExcelProperty("入厂量（吨）")
    private String transNum;

    @ExcelProperty("指标类型")
    private String chkType;
    /**
     * 指数值1
     */
    @ExcelProperty(index = 5)
    @NumberFormat("#.##")
    private BigDecimal indexValue1;
    /**
     * 指数值2
     */
    @ExcelProperty(index = 6)
    @NumberFormat("#.##")
    private BigDecimal indexValue2;
    /**
     * 指数值3
     */
    @ExcelProperty(index = 7)
    @NumberFormat("#.##")
    private BigDecimal indexValue3;
    /**
     * 指数值4
     */
    @ExcelProperty(index = 8)
    @NumberFormat("#.##")
    private BigDecimal indexValue4;
    /**
     * 指数值5
     */
    @ExcelProperty(index = 9)
    @NumberFormat("#.##")
    private BigDecimal indexValue5;
    /**
     * 指数值6
     */
    @ExcelProperty(index = 10)
    @NumberFormat("#.##")
    private BigDecimal indexValue6;
    /**
     * 指数值7
     */
    @ExcelProperty(index = 11)
    @NumberFormat("#.##")
    private BigDecimal indexValue7;
    /**
     * 指数值8
     */
    @ExcelProperty(index = 12)
    @NumberFormat("#.##")
    private BigDecimal indexValue8;
    /**
     * 指数值9
     */
    @ExcelProperty(index = 13)
    @NumberFormat("#.##")
    private BigDecimal indexValue9;
    /**
     * 指数值10
     */
    @ExcelProperty(index = 14)
    @NumberFormat("#.##")
    private BigDecimal indexValue10;
    /**
     * 指数值11
     */
    @ExcelProperty(index = 15)
    @NumberFormat("#.##")
    private BigDecimal indexValue11;
    /**
     * 指数值12
     */
    @ExcelProperty(index = 16)
    @NumberFormat("#.##")
    private BigDecimal indexValue12;
    /**
     * 指数值13
     */
    @ExcelProperty(index = 17)
    @NumberFormat("#.##")
    private BigDecimal indexValue13;
    /**
     * 指数值14
     */
    @ExcelProperty(index = 18)
    @NumberFormat("#.##")
    private BigDecimal indexValue14;
    /**
     * 指数值15
     */
    @ExcelProperty(index = 19)
    @NumberFormat("#.##")
    private BigDecimal indexValue15;
    /**
     * 指数值16
     */
    @ExcelProperty(index = 20)
    @NumberFormat("#.##")
    private BigDecimal indexValue16;
    /**
     * 指数值17
     */
    @ExcelProperty(index = 21)
    @NumberFormat("#.##")
    private BigDecimal indexValue17;
    /**
     * 指数值18
     */
    @ExcelProperty(index = 22)
    @NumberFormat("#.##")
    private BigDecimal indexValue18;
    /**
     * 指数值19
     */
    @ExcelProperty(index = 23)
    @NumberFormat("#.##")
    private BigDecimal indexValue19;
    /**
     * 指数值20
     */
    @ExcelProperty(index = 24)
    @NumberFormat("#.##")
    private BigDecimal indexValue20;




}
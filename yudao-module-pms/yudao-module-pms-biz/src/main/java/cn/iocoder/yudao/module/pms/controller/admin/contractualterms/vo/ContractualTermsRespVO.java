package cn.iocoder.yudao.module.pms.controller.admin.contractualterms.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 合同条款设定 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ContractualTermsRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("ID")
    private Long id;

    @Schema(description = "项目号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("项目号")
    private String project;

    @Schema(description = "公司别", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("公司别")
    private String compId;

    @Schema(description = "条款编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("条款编号")
    private String termNo;

    @Schema(description = "条款名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王")
    @ExcelProperty("条款名称")
    private String termName;

    @Schema(description = "条款说明", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("条款说明")
    private String termExplan;

    @Schema(description = "状态")
    @ExcelProperty("状态")
    private String termStatus;

    @Schema(description = "条款内容")
    @ExcelProperty("条款内容")
    private String termContent;

    @Schema(description = "创建者")
    @ExcelProperty("创建者")
    private String creator;

    @Schema(description = "更新者")
    @ExcelProperty("更新者")
    private String updater;

    @Schema(description = "新增时间")
    @ExcelProperty("新增时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    @ExcelProperty("修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建者姓名")
    @ExcelProperty("创建者姓名")
    private String createEmpNo;

    @Schema(description = "更新者者姓名")
    @ExcelProperty("更新者者姓名")
    private String updateEmpNo;

}
package cn.iocoder.yudao.module.pms.dal.mysql.mpp;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.pms.controller.admin.mpp.vo.ProcurementCollectionItemVersionPageReqVO;
import cn.iocoder.yudao.module.pms.dal.dataobject.mpp.ProcurementCollectionItemVersionDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 集采物料明细版本记录表 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProcurementCollectionItemVersionMapper extends BaseMapperX<ProcurementCollectionItemVersionDO> {

    default PageResult<ProcurementCollectionItemVersionDO> selectPage(ProcurementCollectionItemVersionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProcurementCollectionItemVersionDO>()
                .eqIfPresent(ProcurementCollectionItemVersionDO::getCollectionId, reqVO.getCollectionId())
                .eqIfPresent(ProcurementCollectionItemVersionDO::getItemId, reqVO.getItemId())
                .eqIfPresent(ProcurementCollectionItemVersionDO::getVersionNo, reqVO.getVersionNo())
                .likeIfPresent(ProcurementCollectionItemVersionDO::getMaterialNo, reqVO.getMaterialNo())
                .eqIfPresent(ProcurementCollectionItemVersionDO::getChangeType, reqVO.getChangeType())
                .orderByDesc(ProcurementCollectionItemVersionDO::getCreateTime));
    }

    default List<ProcurementCollectionItemVersionDO> selectListByCollectionId(Long collectionId) {
        return selectList(new LambdaQueryWrapperX<ProcurementCollectionItemVersionDO>()
                .eq(ProcurementCollectionItemVersionDO::getCollectionId, collectionId)
                .orderByDesc(ProcurementCollectionItemVersionDO::getCreateTime));
    }

    default List<ProcurementCollectionItemVersionDO> selectListByItemId(Long itemId) {
        return selectList(new LambdaQueryWrapperX<ProcurementCollectionItemVersionDO>()
                .eq(ProcurementCollectionItemVersionDO::getItemId, itemId)
                .orderByDesc(ProcurementCollectionItemVersionDO::getCreateTime));
    }

    default List<String> selectDistinctVersionsByCollectionId(Long collectionId) {
        List<Object> objects = selectObjs(new LambdaQueryWrapperX<ProcurementCollectionItemVersionDO>()
                .select(ProcurementCollectionItemVersionDO::getVersionNo)
                .eq(ProcurementCollectionItemVersionDO::getCollectionId, collectionId)
                .groupBy(ProcurementCollectionItemVersionDO::getVersionNo)
                .orderByDesc(ProcurementCollectionItemVersionDO::getVersionNo));
        return objects.stream().map(obj -> (String) obj).collect(java.util.stream.Collectors.toList());
    }

    default List<ProcurementCollectionItemVersionDO> selectListByCollectionIdAndVersion(Long collectionId, String versionNo) {
        return selectList(new LambdaQueryWrapperX<ProcurementCollectionItemVersionDO>()
                .eq(ProcurementCollectionItemVersionDO::getCollectionId, collectionId)
                .eq(ProcurementCollectionItemVersionDO::getVersionNo, versionNo)
                .orderByAsc(ProcurementCollectionItemVersionDO::getSortOrder)
                .orderByDesc(ProcurementCollectionItemVersionDO::getCreateTime));
    }
}

package cn.iocoder.yudao.module.pms.controller.admin.invoiceinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 发票信息新增/修改 Request VO")
@Data
public class InvoiceInfoSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "项目")
    private String project;

    @Schema(description = "公司代码")
    private String compId;

    @Schema(description = "申请单号")
    private String issueNo;

    @Schema(description = "发票号")
    private String voucherNo;

    @Schema(description = "发票日期")
    private String voucherDate;

    @Schema(description = "发票性质")
    private String voucherType;

    @Schema(description = "厂商编号")
    private String vendorNo;

    @Schema(description = "装运通知单")
    private String inputTransNo;

    @Schema(description = "厂商名称")
    private String vendorName;

    @Schema(description = "状态")
    private String poStus;

    @Schema(description = "部门")
    private String issueDept;

    @Schema(description = "录入人")
    private String issueEmpNo;

    @Schema(description = "录入说明")
    private String memo;

    @Schema(description = "汇率")
    private BigDecimal exchangeRate;

    @Schema(description = "发票金额(原币)")
    private BigDecimal prodVoucherAmt;

    @Schema(description = "发票金额(人民币)")
    private BigDecimal prodVoucherAmtRmb;

    @Schema(description = "附件数目")
    private String uploadFile;

    @Schema(description = "订购合同号")
    private String poNo;

    @Schema(description = "物料编号")
    private String matrlNo;

    @Schema(description = "品名")
    private String matrlName;

    @Schema(description = "订购数量")
    private BigDecimal qty;

    @Schema(description = "单价")
    private BigDecimal unitPrice;

    @Schema(description = "暂估流水号档")
    private String apNo;

    @Schema(description = "订购合同项次号")
    private String poItemNo;

    @Schema(description = "验收单号(结算单号)")
    private String inspNo;

    @Schema(description = "验收单项次")
    private String inspItemNo;

    @Schema(description = "币别")
    private String crcy;

    @Schema(description = "入库单")
    private String miNo;

    @Schema(description = "账务日期")
    private String vchrDate;

    @Schema(description = "新增人员")
    private String createEmpNo;

    @Schema(description = "新增日期")
    private String createDate;

    @Schema(description = "修改人员")
    private String updateEmpNo;

    @Schema(description = "修改日期")
    private String updateDate;

    @Schema(description = "附件")
    private String annex;

}
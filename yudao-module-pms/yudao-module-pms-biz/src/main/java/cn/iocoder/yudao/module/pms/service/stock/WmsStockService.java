package cn.iocoder.yudao.module.pms.service.stock;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.pms.controller.admin.stock.vo.WmsStockPageReqVO;
import cn.iocoder.yudao.module.pms.controller.admin.stock.vo.WmsStockRespVO;
import cn.iocoder.yudao.module.pms.controller.admin.stock.vo.WmsStockSaveReqVO;
import cn.iocoder.yudao.module.pms.dal.dataobject.stock.WmsStockDO;

import javax.validation.Valid;
import java.util.HashMap;

/**
 * 料号实时库存 Service 接口
 *
 * <AUTHOR>
 */
public interface WmsStockService {

    /**
     * 创建料号实时库存
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createWmsStock(@Valid WmsStockSaveReqVO createReqVO);

    /**
     * 更新料号实时库存
     *
     * @param updateReqVO 更新信息
     */
    void updateWmsStock(@Valid WmsStockSaveReqVO updateReqVO);

    /**
     * 删除料号实时库存
     *
     * @param id 编号
     */
    void deleteWmsStock(Long id);

    /**
     * 获得料号实时库存
     *
     * @param id 编号
     * @return 料号实时库存
     */
    WmsStockDO getWmsStock(Long id);

    /**
     * 获得料号实时库存分页
     *
     * @param pageReqVO 分页查询
     * @return 料号实时库存分页
     */
    PageResult<WmsStockRespVO> getWmsStockPage(WmsStockPageReqVO pageReqVO);

    WmsStockDO selectStockByKz(HashMap map);

    /** 通过料号和合同号获取库存数据*/
    WmsStockDO getWmsStockByMatrlnoAndPono(String matrlno, String lotno);

    WmsStockDO getEndQtySumByMatrlno(String matrlno);
}

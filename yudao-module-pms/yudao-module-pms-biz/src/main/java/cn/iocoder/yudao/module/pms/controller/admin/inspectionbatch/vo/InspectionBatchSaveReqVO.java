package cn.iocoder.yudao.module.pms.controller.admin.inspectionbatch.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 验收批次档新增/修改 Request VO")
@Data
public class InspectionBatchSaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "9416")
    private Long id;

    @Schema(description = "项目")
    private String project;

    @Schema(description = "公司别", requiredMode = Schema.RequiredMode.REQUIRED, example = "7747")
    private String compid;

    @Schema(description = "合同号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String contractno;
    @Schema(description = "检验批号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String chkno;

    @Schema(description = "手工批号")
    private String manualchkno;

    @Schema(description = "检验类别", example = "2")
    private String chktype;

    @Schema(description = "类别", example = "2")
    private String type;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "供应商代码")
    private String supplierno;

    @Schema(description = "供应商", example = "张三")
    private String supplyname;

    @Schema(description = "检验人")
    private String tester;

    @Schema(description = "主检人")
    private String mjtester;

    @Schema(description = "发站")
    private String sendstation;

    @Schema(description = "发站说明")
    private String sendstationdesc;

    @Schema(description = "原料料号")
    private String matrlno;

    @Schema(description = "是否已品质验收")
    private String isqtyacpt;

    @Schema(description = "是否已重量验收")
    private String ismasacpt;

    @Schema(description = "品质检验结果")
    private String qtyover;

    @Schema(description = "化验室")
    private String testroom;

    @Schema(description = "检验日期")
    private String testdate;

}
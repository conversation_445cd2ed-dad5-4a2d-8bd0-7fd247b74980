package cn.iocoder.yudao.module.pms.controller.admin.contractmain.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class FwResultReqVO implements Serializable{
    /**
     * 响应码
     */
    private Integer code;

    /**
     * 响应数据
     */
    private ResultBody data;

    /**
     * 响应消息
     */
    private String message;

    @Data
    public static class ResultBody implements Serializable {
        /**
         * 第三方版本号
         */
        private String thirdVersion;

        /**
         * 第三方合同编号
         */
        private String thirdContractNo;

        /**
         * 合同ID
         */
        private String contractId;

        /**
         * 合同编码
         */
        private String contractCode;

        /**
         * 合同生效日期
         */
        private String contractTakeEffectDate;

        /**
         * 文件ID
         */
        private String fileId;
    }
}

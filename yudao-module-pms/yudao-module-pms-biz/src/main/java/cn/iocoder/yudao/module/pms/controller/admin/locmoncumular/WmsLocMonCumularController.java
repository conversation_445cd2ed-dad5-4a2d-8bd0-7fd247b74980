package cn.iocoder.yudao.module.pms.controller.admin.locmoncumular;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.pms.controller.admin.locmoncumular.vo.WmsLocMonCumularPageReqVO;
import cn.iocoder.yudao.module.pms.controller.admin.locmoncumular.vo.WmsLocMonCumularRespVO;
import cn.iocoder.yudao.module.pms.controller.admin.locmoncumular.vo.WmsLocMonCumularSaveReqVO;
import cn.iocoder.yudao.module.pms.controller.admin.wmstrade.vo.WmsTradeSaveReqVO;
import cn.iocoder.yudao.module.pms.dal.dataobject.locmoncumular.WmsLocMonCumularDO;
import cn.iocoder.yudao.module.pms.service.locmoncumular.WmsLocMonCumularService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 储位料号月累计收发")
@RestController
@RequestMapping("/pms/wms-loc-mon-cumular")
@Validated
public class WmsLocMonCumularController {

    @Resource
    private WmsLocMonCumularService wmsLocMonCumularService;

    @PostMapping("/create")
    @Operation(summary = "创建储位料号月累计收发")
    @PreAuthorize("@ss.hasPermission('pms:wms-loc-mon-cumular:create')")
    public CommonResult<Long> createWmsLocMonCumular(@Valid @RequestBody WmsLocMonCumularSaveReqVO createReqVO) {
        return success(wmsLocMonCumularService.createWmsLocMonCumular(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新储位料号月累计收发")
    @PreAuthorize("@ss.hasPermission('pms:wms-loc-mon-cumular:update')")
    public CommonResult<Boolean> updateWmsLocMonCumular(@Valid @RequestBody WmsLocMonCumularSaveReqVO updateReqVO) {
        wmsLocMonCumularService.updateWmsLocMonCumular(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除储位料号月累计收发")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:wms-loc-mon-cumular:delete')")
    public CommonResult<Boolean> deleteWmsLocMonCumular(@RequestParam("id") Long id) {
        wmsLocMonCumularService.deleteWmsLocMonCumular(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得储位料号月累计收发")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:wms-loc-mon-cumular:query')")
    public CommonResult<WmsLocMonCumularRespVO> getWmsLocMonCumular(@RequestParam("id") Long id) {
        WmsLocMonCumularDO wmsLocMonCumular = wmsLocMonCumularService.getWmsLocMonCumular(id);
        return success(BeanUtils.toBean(wmsLocMonCumular, WmsLocMonCumularRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得储位料号月累计收发分页")
    @PreAuthorize("@ss.hasPermission('pms:wms-loc-mon-cumular:query')")
    public CommonResult<PageResult<WmsLocMonCumularRespVO>> getWmsLocMonCumularPage(@Valid WmsLocMonCumularPageReqVO pageReqVO) {
        PageResult<WmsLocMonCumularRespVO> pageResult = wmsLocMonCumularService.getWmsLocMonCumularPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, WmsLocMonCumularRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出储位料号月累计收发 Excel")
    @PreAuthorize("@ss.hasPermission('pms:wms-loc-mon-cumular:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportWmsLocMonCumularExcel(@Valid WmsLocMonCumularPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<WmsLocMonCumularRespVO> list = wmsLocMonCumularService.getWmsLocMonCumularPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "储位料号月累计收发.xls", "数据", WmsLocMonCumularRespVO.class,
                        BeanUtils.toBean(list, WmsLocMonCumularRespVO.class));
    }

    @GetMapping("/carryOver")
    @Operation(summary = "条件搜索库存结转")
    @PreAuthorize("@ss.hasPermission('pms:wms-loc-mon-cumular:create')")
    public CommonResult<Boolean> stockCarryOver(@Valid WmsLocMonCumularPageReqVO pageReqVO) {
        wmsLocMonCumularService.stockCarryOver(pageReqVO);
        return success(true);
    }

}
package cn.iocoder.yudao.module.pms.controller.admin.policyinfo.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 保单基本信息新增/修改 Request VO")
@Data
public class PolicyInfoSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "父id")
    private Long parentId;

    @Schema(description = "申请单号")
    private String poNo;

    @Schema(description = "发票号")
    private String voucherNo;

    @Schema(description = "信用证号")
    private String lcNo;

    @Schema(description = "发票金额")
    private BigDecimal voucherAmount;

    @Schema(description = "投保加成")
    private BigDecimal plus;

    @Schema(description = "货物类型")
    private String goodsCategory;

    @Schema(description = "湿重数量")
    private BigDecimal quantity;

    @Schema(description = "品名")
    private String matrlName;

    @Schema(description = "保险金额")
    private BigDecimal policyAmt;

    @Schema(description = "币别")
    private String crcy;

    @Schema(description = "发港日期")
    private String actlArivDate;

    @Schema(description = "发港日期（英文）")
    private String actlArivDateEn;

    @Schema(description = "船名")
    private String shipName;

    @Schema(description = "船名（英文）")
    private String shipNameEn;

    @Schema(description = "发港名称")
    private String loadPort;

    @Schema(description = "发港名称（英文）")
    private String loadPortEn;

    @Schema(description = "中转港名称")
    private String viaPort;

    @Schema(description = "中转港名称（英文）")
    private String viaPortEn;

    @Schema(description = "卸港名称")
    private String relsPort;

    @Schema(description = "卸港名称（英文）")
    private String relsPortEn;

    @Schema(description = "提单号")
    private String blNo;

    @Schema(description = "赔款偿付地点")
    private String claimPayableAt;

    @Schema(description = "赔款偿付地点（英文）")
    private String claimPayableAtEn;

    @Schema(description = "投保险别")
    private String insuranceConditions;

    @Schema(description = "集装箱种类")
    private String container;

    @Schema(description = "转运工具")
    private String transit;

    @Schema(description = "船籍")
    private String shipRegistry;

    @Schema(description = "船龄")
    private String shipAge;

    @Schema(description = "合同创建人")
    private String poCreateEmp;

    @Schema(description = "投保日期")
    private String policyDate;

    @Schema(description = "电话")
    private String tel;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "费率")
    private BigDecimal rate;

    @Schema(description = "保费")
    private BigDecimal premium;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "经办人")
    private String operator;

    @Schema(description = "核保人")
    private String underwriter;

    @Schema(description = "负责人")
    private String manager;

    @Schema(description = "新增人员")
    private String createEmpNo;

    @Schema(description = "新增日期")
    private String createDate;

    @Schema(description = "修改人员")
    private String updateEmpNo;

    @Schema(description = "修改日期")
    private String updateDate;

}
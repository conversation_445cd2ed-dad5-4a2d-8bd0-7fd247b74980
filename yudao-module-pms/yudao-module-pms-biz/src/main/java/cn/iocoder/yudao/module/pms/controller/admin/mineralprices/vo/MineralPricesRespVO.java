package cn.iocoder.yudao.module.pms.controller.admin.mineralprices.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 进口矿价格 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MineralPricesRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2326")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "品种", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("品种")
    private String variety;

    @Schema(description = "货款计算基础指数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("货款计算基础指数")
    private String paymentBaseIndex;

    @Schema(description = "运费计算基础指数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("运费计算基础指数")
    private String freightBaseIndex;

    @Schema(description = "指数月(YYYY-MM-01格式)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("指数月(YYYY-MM-01格式)")
    @JsonFormat(pattern = "yyyy-MM-01")
    private LocalDate indexMonth;

    @Schema(description = "系统计算DMTU")
    @ExcelProperty("系统计算DMTU")
    private BigDecimal systemCalculatedDmtu;

    @Schema(description = "市场公布DMTU")
    @ExcelProperty("市场公布DMTU")
    private BigDecimal marketPublishedDmtu;

    @Schema(description = "典型值1")
    @ExcelProperty("典型值1")
    private BigDecimal typical1;

    @Schema(description = "典型值2")
    @ExcelProperty("典型值2")
    private BigDecimal typical2;

    @Schema(description = "典型值3")
    @ExcelProperty("典型值3")
    private BigDecimal typical3;

    @Schema(description = "典型值4")
    @ExcelProperty("典型值4")
    private BigDecimal typical4;

    @Schema(description = "典型值5")
    @ExcelProperty("典型值5")
    private BigDecimal typical5;

    @Schema(description = "典型值6")
    @ExcelProperty("典型值6")
    private BigDecimal typical6;

    @Schema(description = "典型值7")
    @ExcelProperty("典型值7")
    private BigDecimal typical7;

    @Schema(description = "典型值8")
    @ExcelProperty("典型值8")
    private BigDecimal typical8;

    @Schema(description = "典型值9")
    @ExcelProperty("典型值9")
    private BigDecimal typical9;

    @Schema(description = "典型值10")
    @ExcelProperty("典型值10")
    private BigDecimal typical10;

    @Schema(description = "折扣方式")
    @ExcelProperty("折扣方式")
    private String discountMethod;

    @Schema(description = "折扣/溢价")
    @ExcelProperty("折扣/溢价")
    private BigDecimal discountPremium;

    @Schema(description = "DMTU计算公式")
    @ExcelProperty("DMTU计算公式")
    private String dmtuFormula;

    @Schema(description = "货值计算公式")
    @ExcelProperty("货值计算公式")
    private String cargoFormula;

    @Schema(description = "运费计算公式")
    @ExcelProperty("运费计算公式")
    private String freightFormula;

    @Schema(description = "折扣计算公式")
    @ExcelProperty("折扣计算公式")
    private String discountFormula;

    @Schema(description = "备用字段1")
    @ExcelProperty("备用字段1")
    private String backup1;

    @Schema(description = "备用字段2")
    @ExcelProperty("备用字段2")
    private String backup2;

    @Schema(description = "备用字段3")
    @ExcelProperty("备用字段3")
    private String backup3;

    @Schema(description = "备用字段4")
    @ExcelProperty("备用字段4")
    private String backup4;

    @Schema(description = "备用字段5")
    @ExcelProperty("备用字段5")
    private String backup5;

    @Schema(description = "备用字段6")
    @ExcelProperty("备用字段6")
    private String backup6;

    @Schema(description = "备用字段7")
    @ExcelProperty("备用字段7")
    private String backup7;

    @Schema(description = "备用字段8")
    @ExcelProperty("备用字段8")
    private String backup8;

    @Schema(description = "备用字段9")
    @ExcelProperty("备用字段9")
    private String backup9;

    @Schema(description = "备用字段10")
    @ExcelProperty("备用字段10")
    private String backup10;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
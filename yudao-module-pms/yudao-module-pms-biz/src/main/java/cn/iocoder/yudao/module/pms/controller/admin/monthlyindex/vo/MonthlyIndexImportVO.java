package cn.iocoder.yudao.module.pms.controller.admin.monthlyindex.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.format.NumberFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false)
@ExcelIgnoreUnannotated
public class MonthlyIndexImportVO {
    @ExcelProperty("序号")
    private Long seq;
    @ExcelProperty("记录日期")
    @DateTimeFormat(FORMAT_YEAR_MONTH_DAY)
    private LocalDate recordDate;
    /**
     * 指数值1
     */
    @ExcelProperty("普指62指数")
    @NumberFormat("#.##")
    private BigDecimal indexValue1;
    /**
     * 指数值2
     */
    @ExcelProperty(index = 3)
    @NumberFormat("#.##")
    private BigDecimal indexValue2;
    /**
     * 指数值3
     */
    @ExcelProperty(index = 4)
    @NumberFormat("#.##")
    private BigDecimal indexValue3;
    /**
     * 指数值4
     */
    @ExcelProperty(index = 5)
    @NumberFormat("#.##")
    private BigDecimal indexValue4;
    /**
     * 指数值5
     */
    @ExcelProperty(index = 6)
    @NumberFormat("#.##")
    private BigDecimal indexValue5;
    /**
     * 指数值6
     */
    @ExcelProperty(index = 7)
    @NumberFormat("#.##")
    private BigDecimal indexValue6;
    /**
     * 指数值7
     */
    @ExcelProperty(index = 8)
    @NumberFormat("#.##")
    private BigDecimal indexValue7;
    /**
     * 指数值8
     */
    @ExcelProperty(index = 9)
    @NumberFormat("#.##")
    private BigDecimal indexValue8;
    /**
     * 指数值9
     */
    @ExcelProperty(index = 10)
    @NumberFormat("#.##")
    private BigDecimal indexValue9;
    /**
     * 指数值10
     */
    @ExcelProperty(index = 11)
    @NumberFormat("#.##")
    private BigDecimal indexValue10;
    /**
     * 指数值11
     */
    @ExcelProperty(index = 12)
    @NumberFormat("#.##")
    private BigDecimal indexValue11;
    /**
     * 指数值12
     */
    @ExcelProperty(index = 13)
    @NumberFormat("#.##")
    private BigDecimal indexValue12;
    /**
     * 指数值13
     */
    @ExcelProperty(index = 14)
    @NumberFormat("#.##")
    private BigDecimal indexValue13;
    /**
     * 指数值14
     */
    @ExcelProperty(index = 15)
    @NumberFormat("#.##")
    private BigDecimal indexValue14;
    /**
     * 指数值15
     */
    @ExcelProperty(index = 16)
    @NumberFormat("#.##")
    private BigDecimal indexValue15;
    /**
     * 指数值16
     */
    @ExcelProperty(index = 17)
    @NumberFormat("#.##")
    private BigDecimal indexValue16;
    /**
     * 指数值17
     */
    @ExcelProperty(index = 18)
    @NumberFormat("#.##")
    private BigDecimal indexValue17;
    /**
     * 指数值18
     */
    @ExcelProperty(index = 19)
    @NumberFormat("#.##")
    private BigDecimal indexValue18;
    /**
     * 指数值19
     */
    @ExcelProperty(index = 20)
    @NumberFormat("#.##")
    private BigDecimal indexValue19;
    /**
     * 指数值20
     */
    @ExcelProperty(index = 21)
    @NumberFormat("#.##")
    private BigDecimal indexValue20;


}
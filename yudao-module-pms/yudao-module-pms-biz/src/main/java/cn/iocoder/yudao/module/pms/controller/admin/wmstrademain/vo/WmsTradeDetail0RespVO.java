package cn.iocoder.yudao.module.pms.controller.admin.wmstrademain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Schema(description = "管理后台 - 物料交易申请主档 Response VO")
@Data
@ExcelIgnoreUnannotated
public class WmsTradeDetail0RespVO {


    /**
     * 库存ID
     */
    @Schema(description = "库存ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;
    /**
     * 父节点编号
     */
    @Schema(description = "父节点编号")
    private Long parentId;
    /**
     * 项目号
     */
    @Schema(description = "项目号")
    private String project;
    /**
     * 公司别
     */
    @Schema(description = "公司别")
    private String compId;
    /**
     * 交易单号
     */
    @Schema(description = "交易单号")
    private String issueTallyNo;
    /**
     * 交易种类
     */
    @Schema(description = "交易种类")
    private String issueType;
    /**
     * 项次序号
     */
    @Schema(description = "项次序号")
    private String seqNo;
    /**
     * 品别
     */
    @Schema(description = "品别")
    private String inventoryType;
    /**
     * 料号
     */
    @Schema(description = "料号")
    private String matrlNo;
    /**
     * 品级
     */
    @Schema(description = "品级")
    private String matrlGrade;
    /**
     * 储位
     */
    @Schema(description = "储位")
    private String locNo;
    /**
     * 批号
     */
    @Schema(description = "批号")
    private String lotNo;
    /**
     * 尚末发料量
     */
    @Schema(description = "尚末发料量")
    private BigDecimal accuPreTranQty;
    /**
     * 交易数量
     */
    @Schema(description = "交易数量")
    private BigDecimal issueQty;
    /**
     * 交易金额
     */
    @Schema(description = "交易金额")
    private BigDecimal issueAmt;
    /**
     * 销售金额
     */
    @Schema(description = "销售金额")
    private BigDecimal salesAmt;
    /**
     * 移入储区
     */
    @Schema(description = "移入储区")
    private String importLocNo;
    /**
     * 状况码
     */
    @Schema(description = "状况码")
    private String stus;
    /**
     * 验收单
     */
    @Schema(description = "验收单")
    private String tranTallyNo;
    /**
     * 合同编号
     */
    @Schema(description = "合同编号")
    private String contractNo;
    /**
     * 供应商代码
     */
    @Schema(description = "供应商代码")
    private String supplierNo;
    /**
     * 会计科目
     */
    @Schema(description = "会计科目")
    private String acctCode;
    /**
     * 成本科目
     */
    @Schema(description = "成本科目")
    private String costCode;
    /**
     * 会计科目
     */
    @Schema(description = "会计科目")
    private String acctCodeDr;
    /**
     * 是否抛成本系统
     */
    @Schema(description = "是否抛成本系统")
    private String isThrowAc;
    /**
     * 库存量
     */
    @Schema(description = "库存量")
    private BigDecimal inventoryQty;
    /**
     * 库存金额
     */
    @Schema(description = "库存金额")
    private BigDecimal inventoryAmt;
    /**
     * 盘点量
     */
    @Schema(description = "盘点量")
    private BigDecimal checkQty;
    /**
     * 盘点金额
     */
    @Schema(description = "盘点金额")
    private BigDecimal checkAmt;
    /**
     * 库存单位
     */
    @Schema(description = "库存单位")
    private String unitInv;
    /**
     * 调整前料号
     */
    @Schema(description = "调整前料号")
    private String preAdjustMtrlId;
    /**
     * 调整后料号
     */
    @Schema(description = "调整后料号")
    private String aftAdjustMtrlId;
    /**
     * 残值比
     */
    @Schema(description = "残值比")
    private String remnantPer;
    /**
     * 建立人
     */
    @Schema(description = "建立人")
    private String createEmpNo;
    /**
     * 建立日期
     */
    @Schema(description = "建立日期")
    private String createDate;
    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateEmpNo;
    /**
     * 修改日期
     */
    @Schema(description = "修改日期")
    private String updateDate;
    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    @Schema(description = "资料建立时间")
    private String createTime;

    @Schema(description = "创建人名称")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "料号中文名称")
    private String matrlname;

    @Schema(description = "规格型号")
    private String spec;

    @Schema(description = "单位")
    private String unitinv;

}
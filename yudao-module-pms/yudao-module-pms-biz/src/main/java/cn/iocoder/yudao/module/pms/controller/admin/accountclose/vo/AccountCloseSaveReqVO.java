package cn.iocoder.yudao.module.pms.controller.admin.accountclose.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 关账日期新增/修改 Request VO")
@Data
public class AccountCloseSaveReqVO {

    @Schema(description = "账务编号", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "项目号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "项目号不能为空")
    private String project;

    @Schema(description = "公司别", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "公司别不能为空")
    private String compid;

    @Schema(description = "品别", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "品别不能为空")
    private String inventorytype;

    @Schema(description = "年月", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "年月不能为空")
    private String yearmo;

    @Schema(description = "关帐起日")
    private String firstdate;

    @Schema(description = "关帐止日")
    private String enddate;

    @Schema(description = "关帐是否确定")
    private String stus;

    @Schema(description = "关帐人")
    private String closeempno;

    @Schema(description = "关帐日期")
    private String closedate;

    @Schema(description = "关帐时间")
    private String closetime;

}
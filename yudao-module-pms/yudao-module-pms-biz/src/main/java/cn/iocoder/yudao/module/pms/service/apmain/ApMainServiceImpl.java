package cn.iocoder.yudao.module.pms.service.apmain;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.iocoder.yudao.framework.common.customs.util.NCCUtils;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.date.DateUtil;
import cn.iocoder.yudao.module.pms.controller.admin.contractmain.vo.ContractMainSaveReqVO;
import cn.iocoder.yudao.module.pms.dal.dataobject.contractmain.ContractMainDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.epdetail.EpDetailDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.lcregister.LCRegisterDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.lcusage.LcUsageDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.other.PayInfoDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.pomain.PoMainDO;
import cn.iocoder.yudao.module.pms.dal.mysql.epdetail.EpDetailMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.lcregister.LCRegisterMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.lcusage.LcUsageMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.other.PayInfoMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.pomain.PoMainMapper;
import cn.iocoder.yudao.module.pms.dal.redis.no.PmsNoRedisDAO;
import cn.iocoder.yudao.module.srm.api.ExternalUnitApi;
import cn.iocoder.yudao.module.system.api.dept.DeptApi;
import cn.iocoder.yudao.module.system.api.exchangerate.ExchangeRateApi;
import cn.iocoder.yudao.module.system.api.exchangerate.vo.ExchangeRateRespDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;

import cn.iocoder.yudao.module.pms.controller.admin.apmain.vo.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.apmain.ApMainDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.apmain.ApDetailDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.apmain.ApDetail2DO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.pms.dal.mysql.apmain.ApMainMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.apmain.ApDetailMapper;
import cn.iocoder.yudao.module.pms.dal.mysql.apmain.ApDetail2Mapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.*;
import static cn.iocoder.yudao.module.pms.enums.ErrorCodeConstants.*;

/**
 * 报支记录主档 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Transactional(rollbackFor = Exception.class)
public class ApMainServiceImpl implements ApMainService {

    @Resource
    private ApMainMapper apMainMapper;
    @Resource
    private ApDetailMapper apDetailMapper;
    @Resource
    private ApDetail2Mapper apDetail2Mapper;
    @Resource
    private PmsNoRedisDAO noRedisDAO;
    @Resource
    private EpDetailMapper epDetailMapper;
    @Resource
    private PayInfoMapper payInfoMapper;
    @Resource
    private PoMainMapper poMainMapper;
    @Resource
    private LCRegisterMapper lCRegisterMapper;
    @Resource
    private LcUsageMapper lcUsageMapper;
    @Resource
    private ExchangeRateApi exchangeRateApi;
    @Resource
    private DeptApi deptApi;
    @Resource
    private ExternalUnitApi externalUnitApi;

    @Override
    public Long createApMain(ApMainSaveReqVO createReqVO) {
        // 插入
        ApMainDO apMain = BeanUtils.toBean(createReqVO, ApMainDO.class);
        String payNo = noRedisDAO.generate(PmsNoRedisDAO.AP_PAY_NO_PREFIX);
        String dateStr = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATE_PATTERN);
        String timeStr = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_TIME_PATTERN);
        apMain.setPayno(payNo);
        apMain.setBillDate(dateStr);
        apMain.setCrtDate(dateStr);
        apMain.setCrtTime(timeStr);
        apMain.setUpdDate(dateStr);
        apMain.setUpdTime(timeStr);
        apMain.setPayvendorno(apMain.getVendorno());
        if (!"Y".equals(createReqVO.getIsScc())) {
            apMain.setCompid(getLoginUserTenantId().toString());
            String userId = getLoginUserId().toString();
            String userName = getLoginUserNickname();
            apMain.setProject(getLoginUserTopDeptId().toString());
            apMain.setCrtemplno(userId);
            apMain.setCreateEmpNo(userName);
            apMain.setUpdemplno(userId);
            apMain.setUpdateEmpNo(userName);
        }
        apMainMapper.insert(apMain);
        // 返回
        return apMain.getId();
    }

    @Override
    public void updateApMain(ApMainSaveReqVO updateReqVO) {
        // 校验存在
        validateApMainExists(updateReqVO.getId());
        // 更新
        ApMainDO updateObj = BeanUtils.toBean(updateReqVO, ApMainDO.class);
        String dateStr = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATE_PATTERN);
        String timeStr = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_TIME_PATTERN);
        updateObj.setUpdDate(dateStr);
        updateObj.setUpdTime(timeStr);
        if (!"Y".equals(updateReqVO.getIsScc())) {
            String userId = getLoginUserId().toString();
            String userName = getLoginUserNickname();
            updateObj.setUpdemplno(userId);
            updateObj.setUpdateEmpNo(userName);
        }
        apMainMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteApMain(Long id) {
        // 校验存在
        validateApMainExists(id);

        List<ApDetailDO> apDetailDOList = apDetailMapper.selectByParentid(id);
        if (CollectionUtil.isNotEmpty(apDetailDOList)) {
            throw exception(AP_DETAIL_EXISTS);
        }
        List<ApDetail2DO> apDetail2DOList = apDetail2Mapper.selectByParentid(id);
        if (CollectionUtil.isNotEmpty(apDetail2DOList)) {
            throw exception(AP_DETAIL2_EXISTS);
        }

        // 删除
        apMainMapper.deleteById(id);

        // 删除子表
//        deleteApDetailByParentid(id);
//        deleteApDetail2ByParentid(id);
    }

    private void validateApMainExists(Long id) {
        if (apMainMapper.selectById(id) == null) {
            throw exception(AP_MAIN_NOT_EXISTS);
        }
    }

    @Override
    public ApMainDO getApMain(Long id) {
        return apMainMapper.selectApMainById(id);
    }

    @Override
    public PageResult<ApMainDO> getApMainPage(ApMainPageReqVO pageReqVO) {
        return apMainMapper.selectPage(pageReqVO);
    }

    // ==================== 子表（报支记录明细档） ====================

    @Override
    public PageResult<ApDetailDO> getApDetailPage(PageParam pageReqVO, Long parentid) {
        return apDetailMapper.selectPage(pageReqVO, parentid);
    }

    @Override
    public Long createApDetail(ApDetailDO apDetail) {
        apDetailMapper.insert(apDetail);
        return apDetail.getId();
    }

    @Override
    public void updateApDetail(ApDetailDO apDetail) {
        // 校验存在
        validateApDetailExists(apDetail.getId());
        // 更新
        apDetailMapper.updateById(apDetail);
    }

    @Override
    public void deleteApDetail(Long id) {
        // 校验存在
        validateApDetailExists(id);
        // 删除
        apDetailMapper.deleteById(id);
    }

    @Override
    public ApDetailDO getApDetail(Long id) {
        return apDetailMapper.selectById(id);
    }

    private void validateApDetailExists(Long id) {
        if (apDetailMapper.selectById(id) == null) {
            throw exception(AP_DETAIL_NOT_EXISTS);
        }
    }

    private void deleteApDetailByParentid(Long parentid) {
        apDetailMapper.deleteByParentid(parentid);
    }

    // ==================== 子表（报支记录凭证档） ====================

    @Override
    public PageResult<ApDetail2DO> getApDetail2Page(PageParam pageReqVO, Long parentid) {
        return apDetail2Mapper.selectPage(pageReqVO, parentid);
    }

    @Override
    public Long createApDetail2(ApDetail2DO apDetail2) {
        apDetail2Mapper.insert(apDetail2);
        return apDetail2.getId();
    }

    @Override
    public void updateApDetail2(ApDetail2DO apDetail2) {
        // 校验存在
        validateApDetail2Exists(apDetail2.getId());
        // 更新
        apDetail2Mapper.updateById(apDetail2);
    }

    @Override
    public void deleteApDetail2(Long id) {
        // 校验存在
        validateApDetail2Exists(id);
        // 删除
        apDetail2Mapper.deleteById(id);
    }

    @Override
    public ApDetail2DO getApDetail2(Long id) {
        return apDetail2Mapper.selectById(id);
    }

    @Override
    public void batchOperateApDetail2(ApMainDetail2BatchOperateReqVO reqVO) {
        List<ApDetail2DO> items = reqVO.getItems();
        if (CollectionUtil.isEmpty(items)) {
            throw exception(AP_DETAIL2_NO_DATA);
        }
        ApMainDO apMainDO = apMainMapper.selectById(reqVO.getParentid());
        if ("Y".equals(apMainDO.getPaystus())) {
            throw exception(AP_MAIN_ALREADY_CONFIRM);
        }
        // 批量删除无需下面的校验，直接删除即可
        if ("delete".equals(reqVO.getType())) {
            apDetail2Mapper.deleteByIds(items);
            return;
        }

        // 校验是否存在相同发票格式和发票号码的组合
        List<ApDetail2DO> detail2DOList = apDetail2Mapper.selectByParentid(reqVO.getParentid());
        StringBuffer stringBuffer = new StringBuffer();
        for (ApDetail2DO item1 : detail2DOList) {
            for (ApDetail2DO item2 : items) {
                if (item1.getBilldoctype().equals(item2.getBilldoctype()) && item1.getInvoiceno().equals(item2.getInvoiceno())) {
                    if (item1.getId() != item2.getId()) {
                        stringBuffer.append(item1.getBilldoctype() + "-" + item1.getInvoiceno() + ",");
                    }
                }
            }
        }
        if (stringBuffer.length() > 0) {
            stringBuffer.deleteCharAt(stringBuffer.length() - 1);
            throw new ServiceException(1_011_400_999, "该报支记录凭证档下，已存在相同发票格式和发票号码的组合为【" + stringBuffer.toString() + "】的项次档，不允许该操作！");
        }
        String dateStr = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATE_PATTERN);
        String timeStr = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_TIME_PATTERN);
        if ("add".equals(reqVO.getType())) {
            List<ApDetail2DO> insertList = new ArrayList<>();
            for (ApDetail2DO item2 : items) {
                item2.setProject(apMainDO.getProject());
                item2.setCompid(apMainDO.getCompid());
                item2.setVendorno(apMainDO.getVendorno());
                if (!"Y".equals(reqVO.getIsScc())) {
                    String userId = getLoginUserId().toString();
                    String userName = getLoginUserNickname();
                    item2.setCrtemplno(userId);
                    item2.setCreateEmpNo(userName);
                    item2.setUpdemplno(userId);
                    item2.setUpdateEmpNo(userName);
                }
                item2.setCrtDate(dateStr);
                item2.setCrtTime(timeStr);
                item2.setUpdDate(dateStr);
                item2.setUpdTime(timeStr);
                item2.setInvoiceindex(noRedisDAO.generate(PmsNoRedisDAO.AP_INVOICE_INDEX_PREFIX));
                insertList.add(item2);
            }
            apDetail2Mapper.insertBatch(insertList);
        } else if ("edit".equals(reqVO.getType())) {
            List<ApDetail2DO> updateList = new ArrayList<>();
            for (ApDetail2DO item2 : items) {
                if (!"Y".equals(reqVO.getIsScc())) {
                    item2.setUpdemplno(getLoginUserId().toString());
                    item2.setUpdateEmpNo(getLoginUserNickname());
                }
                item2.setUpdDate(dateStr);
                item2.setUpdTime(timeStr);
                updateList.add(item2);
            }
            apDetail2Mapper.updateBatch(updateList);
        }
    }

    @Override
    public void batchOperateApDetail1(ApMainDetail1BatchOperateReqVO reqVO) {
        List<ApDetailDO> items = reqVO.getItems();
        if (CollectionUtil.isEmpty(items)) {
            throw exception(AP_DETAIL1_NO_DATA);
        }
        ApMainDO apMainDO = apMainMapper.selectById(reqVO.getParentid());
        if ("Y".equals(apMainDO.getPaystus())) {
            throw exception(AP_MAIN_ALREADY_CONFIRM);
        }
        // 批量删除无需下面的校验，直接删除即可
        if ("delete".equals(reqVO.getType())) {
            List<EpDetailDO> epUpdateList = new ArrayList<>();
            for (ApDetailDO temp : items) {
                EpDetailDO epDetailDO = epDetailMapper.selectBySrlno(temp.getSrlno());
                BigDecimal payinspamt = epDetailDO.getPayinspamt() == null ? BigDecimal.ZERO : epDetailDO.getPayinspamt();
                if (payinspamt.compareTo(BigDecimal.ZERO) == 0) {
                    epDetailDO.setStus("S");
                } else {
                    epDetailDO.setStus("T");
                }
                if (!"Y".equals(reqVO.getIsScc())) {
                    epDetailDO.setUpdateEmpno(getLoginUserNickname());
                } else {
                    epDetailDO.setUpdater(reqVO.getUpdateVendorNo());
                    epDetailDO.setUpdateEmpno(reqVO.getUpdateVendorName());
                }
                epUpdateList.add(epDetailDO);
            }
            apDetailMapper.deleteByIds(items);
            epDetailMapper.updateBatch(epUpdateList);
            return;
        }
        // 校验是否存在相同暂估款序号
        List<ApDetailDO> doList = apDetailMapper.selectByParentid(reqVO.getParentid());
        if (CollectionUtil.isNotEmpty(doList)) {
            StringBuffer stringBuffer = new StringBuffer();
            for (ApDetailDO item1 : doList) {
                for (ApDetailDO item2 : items) {
                    if (item1.getSrlno().equals(item2.getSrlno())) {
                        if (item1.getId() != item2.getId()) {
                            stringBuffer.append(item1.getSrlno() + ",");
                        }
                    }
                }
            }
            if (stringBuffer.length() > 0) {
                stringBuffer.deleteCharAt(stringBuffer.length() - 1);
                throw new ServiceException(1_011_400_999, "该报支记录明细档下，已存在相同暂估款序号为【" + stringBuffer.toString() + "】的项次档，不允许该操作！");
            }
        }
        String dateStr = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATE_PATTERN);
        String timeStr = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_TIME_PATTERN);
        if ("add".equals(reqVO.getType())) {
            List<ApDetailDO> insertList = new ArrayList<>();
            List<EpDetailDO> epUpdateList = new ArrayList<>();
            Integer maxPayItemNo = apDetailMapper.getMaxPayItemNo(reqVO.getParentid());
            for (ApDetailDO item1 : items) {
                BigDecimal inspamt = item1.getInspamt() == null ? BigDecimal.ZERO : item1.getInspamt();
                BigDecimal payamt = item1.getPayamt() == null ? BigDecimal.ZERO : item1.getPayamt();
                inspamt = new BigDecimal(Math.abs(inspamt.doubleValue()));
                payamt = new BigDecimal(Math.abs(payamt.doubleValue()));
                if (inspamt.compareTo(payamt) > 0) {
                    throw new ServiceException(1_011_400_999, "暂估款流水号为【" + item1.getSrlno() + "】的报支记录明细档，应付金额不能大于付款金额！");
                }

                EpDetailDO epDetailDO = epDetailMapper.selectBySrlno(item1.getSrlno());
                if (!"S".equals(epDetailDO.getStus()) && !"T".equals(epDetailDO.getStus())) {
                    throw new ServiceException(1_011_400_999, "暂估款流水号为【" + epDetailDO.getSrlno() + "】的应付帐款暂估档状态不正确！");
                }

                if (!"Y".equals(reqVO.getIsScc())) {
                    String userId = getLoginUserId().toString();
                    String userName = getLoginUserNickname();
                    item1.setCrtemplno(userId);
                    item1.setCreateEmpNo(userName);
                    item1.setUpdemplno(userId);
                    item1.setUpdateEmpNo(userName);
                }
                item1.setProject(apMainDO.getProject());
                item1.setCompid(apMainDO.getCompid());
                item1.setCrtDate(dateStr);
                item1.setCrtTime(timeStr);
                item1.setUpdDate(dateStr);
                item1.setUpdTime(timeStr);
                maxPayItemNo = maxPayItemNo + 1;
                item1.setPayitemno(String.format("%04d", maxPayItemNo));
                insertList.add(item1);

                epDetailDO.setStus("U");
                if (!"Y".equals(reqVO.getIsScc())) {
                    epDetailDO.setUpdateEmpno(getLoginUserNickname());
                } else {
                    epDetailDO.setUpdater(reqVO.getUpdateVendorNo());
                    epDetailDO.setUpdateEmpno(reqVO.getUpdateVendorName());
                }
                epUpdateList.add(epDetailDO);
            }
            apDetailMapper.insertBatch(insertList);
            epDetailMapper.updateBatch(epUpdateList);

        } else if ("edit".equals(reqVO.getType())) {
            List<ApDetailDO> updateList = new ArrayList<>();
            for (ApDetailDO item1 : items) {
                BigDecimal inspamt = item1.getInspamt() == null ? BigDecimal.ZERO : item1.getInspamt();
                BigDecimal payamt = item1.getPayamt() == null ? BigDecimal.ZERO : item1.getPayamt();
                inspamt = new BigDecimal(Math.abs(inspamt.doubleValue()));
                payamt = new BigDecimal(Math.abs(payamt.doubleValue()));
                if (inspamt.compareTo(payamt) > 0) {
                    throw new ServiceException(1_011_400_999, "暂估款流水号为【" + item1.getSrlno() + "】的报支记录明细档，应付金额不能大于付款金额！");
                }

                EpDetailDO epDetailDO = epDetailMapper.selectBySrlno(item1.getSrlno());
                if (!"U".equals(epDetailDO.getStus())) {
                    throw new ServiceException(1_011_400_999, "暂估款流水号为【" + epDetailDO.getSrlno() + "】的应付帐款暂估档状态不正确！");
                }
                if (!"Y".equals(reqVO.getIsScc())) {
                    item1.setUpdemplno(getLoginUserId().toString());
                    item1.setUpdateEmpNo(getLoginUserNickname());
                }
                item1.setUpdDate(dateStr);
                item1.setUpdTime(timeStr);
                updateList.add(item1);
            }
            apDetailMapper.updateBatch(updateList);
        }
    }

    @Override
    public void confirmApMain(Long id) {
        ApMainDO apMainDO = apMainMapper.selectById(id);
        if ("Y".equals(apMainDO.getPaystus())) {
            throw exception(AP_MAIN_EROR_PAYSTUS);
        }
        // 检核 1、校验信息是否都已填写
        List<ApDetailDO> detailDOList = apDetailMapper.selectByParentid(id);
        if (CollectionUtil.isEmpty(detailDOList)) {
            throw exception(AP_DETAIL1_NO_DATA);
        }
        List<ApDetail2DO> detail2DOList = apDetail2Mapper.selectByParentid(id);
        if (CollectionUtil.isEmpty(detail2DOList)) {
            throw exception(AP_DETAIL2_NO_DATA);
        }
        // 检核 2、付款金额（不含保证金）必须等于发票税前金额！
        BigDecimal totalPayamt = BigDecimal.ZERO;
        for (ApDetailDO temp : detailDOList) {
            BigDecimal payamt = temp.getPayamt() == null ? BigDecimal.ZERO : temp.getPayamt();
            totalPayamt = totalPayamt.add(payamt).setScale(4, RoundingMode.HALF_UP);
        }
        BigDecimal totalAmt = BigDecimal.ZERO;
        for (ApDetail2DO temp : detail2DOList) {
            BigDecimal amt = temp.getAmt() == null ? BigDecimal.ZERO : temp.getAmt();
            totalAmt = totalAmt.add(amt).setScale(4, RoundingMode.HALF_UP);
        }
        if (totalPayamt.compareTo(totalAmt) != 0) {
            throw new ServiceException(1_011_400_999, "付款金额（不含保证金）必须等于发票税前金额！");
        }
        // 回写 EpDetailDO 的状态 有余量：T  无余量：X
        List<EpDetailDO> epUpdateList = new ArrayList<>();
        for (ApDetailDO temp : detailDOList) {
            EpDetailDO epDetailDO = epDetailMapper.selectBySrlno(temp.getSrlno());
            if (!"U".equals(epDetailDO.getStus())) {
                throw new ServiceException(1_011_400_999, "暂估款流水号为【" + epDetailDO.getSrlno() + "】的应付帐款暂估档状态不正确！");
            }
            // 检核 3、校验报支记录明细档的付款金额，是否和应付帐款暂估档的应付金额一致
            BigDecimal payamtEp = epDetailDO.getPreNum11() == null ? BigDecimal.ZERO : epDetailDO.getPreNum11();
            BigDecimal payamt = temp.getPayamt() == null ? BigDecimal.ZERO : temp.getPayamt();
            if (payamt.compareTo(payamtEp) != 0) {
                throw new ServiceException(1_011_400_999, "编号为【" + temp.getPayitemno() + "】的报支记录明细档的付款金额," +
                        "和应付帐款暂估档的应付金额不一致！请删除该条后重新做！");
            }
            BigDecimal inspamt = temp.getInspamt() == null ? BigDecimal.ZERO : temp.getInspamt();
            BigDecimal inspamtAbs = new BigDecimal(Math.abs(inspamt.doubleValue()));

            BigDecimal inspamtEpAbs = new BigDecimal(Math.abs(epDetailDO.getInspamt().doubleValue()));
            if (inspamtAbs.compareTo(inspamtEpAbs) > 0) {
                throw new ServiceException(1_011_400_999, "编号为【" + temp.getPayitemno() + "】的报支记录明细档，应付金额不能大于付款金额！");
            }

            // 通过srlno查询所有应付帐款暂估数据中，已确认的钱
            ApDetailDO apDetailDO = apDetailMapper.getSumConfirmInspamtByEpDetailSrlNo(temp.getSrlno());
            // 加上本条的应付金额，得到srlno对应的总共的应付金额
            BigDecimal confirmInspamt = BigDecimal.ZERO;
            if (apDetailDO != null) {
                confirmInspamt = apDetailDO.getInspamt();
            }
            BigDecimal totalInspamt = inspamt.add(confirmInspamt);

            BigDecimal totalInspamtAbs = new BigDecimal(Math.abs(totalInspamt.doubleValue()));
            if (totalInspamtAbs.compareTo(inspamtEpAbs) > 0) {
                throw new ServiceException(1_011_400_999, "编号为【" + temp.getPayitemno() + "】的报支记录明细档，应付金额汇总值不能大于应付帐款暂估档的验收金额！");
            } else if (totalInspamtAbs.compareTo(inspamtEpAbs) == 0) {
                epDetailDO.setStus("X");
            } else {
                epDetailDO.setStus("T");
            }
            epDetailDO.setPayinspamt(totalInspamt);
            epUpdateList.add(epDetailDO);
        }
        epDetailMapper.updateBatch(epUpdateList);
        apMainDO.setPaystus("Y");
        this.linkApMainDO(apMainDO, detail2DOList);
    }

    @Override
    public String getApDetailTotal(Long parentid) {
        ApDetailDO apDetailDO = apDetailMapper.getApDetailTotal(parentid);
        if (apDetailDO == null) {
            return null;
        }
        return "查询成功!总共应付金额:" + apDetailDO.getInspamt() + ",发票调差金额:" + apDetailDO.getVoucheramt() + ",付款金额:" + apDetailDO.getPayamt();
    }

    @Override
    public void batchOperateApDetail1YF(ApMainDetail1BatchOperateReqVO reqVO) {
        List<ApDetailDO> items = reqVO.getItems();
        if (CollectionUtil.isEmpty(items)) {
            throw exception(AP_DETAIL1_NO_DATA);
        }
        ApMainDO apMainDO = apMainMapper.selectById(reqVO.getParentid());
        if ("Y".equals(apMainDO.getPaystus())) {
            throw exception(AP_MAIN_ALREADY_CONFIRM);
        }
        // 批量删除无需下面的校验，直接删除即可
        if ("delete".equals(reqVO.getType())) {
            apDetailMapper.deleteByIds(items);
            return;
        }
        // 校验是否存在相同订购合同
        List<ApDetailDO> doList = apDetailMapper.selectByParentid(reqVO.getParentid());
        if (CollectionUtil.isNotEmpty(doList)) {
            StringBuffer stringBuffer = new StringBuffer();
            for (ApDetailDO item1 : doList) {
                for (ApDetailDO item2 : items) {
                    if (item1.getPono().equals(item2.getPono())) {
                        if (item1.getId() != item2.getId()) {
                            stringBuffer.append(item1.getPono() + ",");
                        }
                    }
                }
            }
            if (stringBuffer.length() > 0) {
                stringBuffer.deleteCharAt(stringBuffer.length() - 1);
                throw new ServiceException(1_011_400_999, "该预付款明细档下，已存在相同订购合同为【" + stringBuffer.toString() + "】的项次档，不允许该操作！");
            }
        }
        String dateStr = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATE_PATTERN);
        String timeStr = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_TIME_PATTERN);
        String userId = getLoginUserId().toString();
        String userName = getLoginUserNickname();
        if ("add".equals(reqVO.getType())) {
            List<ApDetailDO> insertList = new ArrayList<>();
            Integer maxPayItemNo = apDetailMapper.getMaxPayItemNo(reqVO.getParentid());
            for (ApDetailDO item1 : items) {
                item1.setProject(apMainDO.getProject());
                item1.setCompid(apMainDO.getCompid());
                item1.setCrtemplno(userId);
                item1.setCreateEmpNo(userName);
                item1.setCrtDate(dateStr);
                item1.setCrtTime(timeStr);
                item1.setUpdemplno(userId);
                item1.setUpdateEmpNo(userName);
                item1.setUpdDate(dateStr);
                item1.setUpdTime(timeStr);
                maxPayItemNo = maxPayItemNo + 1;
                item1.setPayitemno(String.format("%04d", maxPayItemNo));
                insertList.add(item1);
            }
            apDetailMapper.insertBatch(insertList);
        } else if ("edit".equals(reqVO.getType())) {
            List<ApDetailDO> updateList = new ArrayList<>();
            for (ApDetailDO item1 : items) {
                item1.setUpdemplno(userId);
                item1.setUpdateEmpNo(userName);
                item1.setUpdDate(dateStr);
                item1.setUpdTime(timeStr);
                updateList.add(item1);
            }
            apDetailMapper.updateBatch(updateList);
        }
    }

    @Override
    public void confirmApMainYF(Long id) {
        ApMainDO apMainDO = apMainMapper.selectById(id);
        if ("Y".equals(apMainDO.getPaystus())) {
            throw exception(AP_MAIN_EROR_PAYSTUS);
        }
        // 检核 1、校验信息是否都已填写
        List<ApDetailDO> detailDOList = apDetailMapper.selectByParentid(id);
        if (CollectionUtil.isEmpty(detailDOList)) {
            throw exception(AP_DETAIL1_NO_DATA_YF);
        }
        BigDecimal totalamtAll = BigDecimal.ZERO;
        JSONArray infos = new JSONArray();
        String cncy = "";
        for (ApDetailDO temp : detailDOList) {
            if(cncy.isEmpty()){
                cncy = temp.getCrcy();
            }
            if(!cncy.equals(temp.getCrcy())){
                throw exception(TEADE_COMMON_ERROR,"明细的币别请设定一致");
            }
            BigDecimal totalamt = temp.getPayamt() == null ? BigDecimal.ZERO : temp.getPayamt();
            totalamtAll = totalamtAll.add(totalamt).setScale(4, RoundingMode.HALF_UP);

            JSONObject info = new JSONObject();
            info.put("memo", temp.getMemo());
            info.put("tax", "CN03");//固定无税
            info.put("contractNo", temp.getPono());
            info.put("amt", temp.getPayamt());

            infos.add(info);
        }
        apMainDO.setTotalAmt(totalamtAll);
        String dateStr = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATE_PATTERN);
        String timeStr = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_TIME_PATTERN);
        apMainDO.setUpdemplno(getLoginUserId().toString());
        apMainDO.setUpdateEmpNo(getLoginUserNickname());
        apMainDO.setUpdDate(dateStr);
        apMainDO.setUpdTime(timeStr);
        apMainDO.setPaystus("Y");


        JSONObject obj = new JSONObject();
        obj.put("masterDeptId",deptApi.getDept(Long.parseLong(apMainDO.getBilldept())).getCheckedData().getMasterDeptId());
        obj.put("upMasterDeptId",deptApi.getSuperDept(Long.parseLong(apMainDO.getBilldept())).getCheckedData().getMasterDeptId());
        obj.put("amt",apMainDO.getTotalAmt());
        obj.put("cncy",detailDOList.get(0).getCrcy());//CNY getExchangeRate
        obj.put("rate",getExchangeRate(detailDOList.get(0).getCrcy()));
        obj.put("supplierMasterId",apMainDO.getVendorno());
        obj.put("balatype",apMainDO.getPaytype());
        obj.put("recaccount",externalUnitApi.getUnit(apMainDO.getVendorno()).getCheckedData().getSupplierBankAccount());
        String billDate = apMainDO.getBillDate();
        if(!billDate.contains("-")){
            billDate = billDate.substring(0,4)+"-"+billDate.substring(4,6)+"-"+billDate.substring(6);
        }
        obj.put("billDate", billDate);
        obj.put("billNo",apMainDO.getPayno());
        obj.put("prepay",apMainDO.getBilltype().equals("A1")?"预付款":"应付单");
//        JSONArray infos = new JSONArray();
//
//        JSONObject info = new JSONObject();
//        info.put("memo", "付进口蒙古矿石款");
//        info.put("tax", "CN03");
//        info.put("contractNo", "2025-TZA-0097");
//        info.put("amt", 4110540.00);
//
//        infos.add(info);
        obj.put("infos",infos);
        JSONObject result = NCCUtils._paybill(obj);
        //{"flag":"Y","payApplicId":"1001AF1000000002NBCH","payApplicNo":"D3202509090000000018","billNo":"***********"}
        if (!"Y".equals(result.getString("flag"))) {
            throw exception(TEADE_COMMON_ERROR,result.getString("msg"));
        }
        apMainDO.setBillno(result.getString("payApplicNo"));
        apMainDO.setPreText1(result.getString("payApplicId"));
        apMainMapper.updateById(apMainDO);


    }
    private BigDecimal getExchangeRate(String cncy) {
        BigDecimal rate = BigDecimal.ONE;
        if (!"CNY".equals(cncy)) {
            CommonResult<List<ExchangeRateRespDTO>> rateList = exchangeRateApi.getRateList("B", cncy, DateUtil.getDate());
            if (rateList.getCode() != 0 || rateList.getData().isEmpty()) {
                throw exception(INSP_EXCHANGERATE_NOT_EXISTS);
            }
            ExchangeRateRespDTO rateDTO = rateList.getData().get(0);
            rate= rateDTO.getRateValue();
        }
        return rate;
    }

    @Override
    public void cancelConfirmApMain(Long id) {
        ApMainDO apMainDO = apMainMapper.selectById(id);
        // 校验，取消确认只能取消已经确认的主档
        if (!"Y".equals(apMainDO.getPaystus())) {
            throw exception(AP_MAIN_ERROR_PAY_STUS);
        }
        // 回写 EpDetailDO 的状态 有余量：T  无余量：X
        List<ApDetailDO> detailDOList = apDetailMapper.selectByParentid(id);
        if (CollectionUtil.isEmpty(detailDOList)) {
            throw exception(AP_DETAIL1_NO_DATA);
        }
        List<EpDetailDO> epUpdateList = new ArrayList<>();
        for (ApDetailDO temp : detailDOList) {
            EpDetailDO epDetailDO = epDetailMapper.selectBySrlno(temp.getSrlno());
            if ("U".equals(epDetailDO.getStus())) {
                throw new ServiceException(1_011_400_999, "编号为【" + temp.getPayitemno() + "】的应付帐款暂估档已被挑选，请删除对应报支记录明细档数据后再取消确认！");
            }
            // 通过srlno查询所有应付帐款暂估数据中，已确认的钱
            ApDetailDO apDetailDO = apDetailMapper.getSumConfirmInspamtByEpDetailSrlNo(temp.getSrlno());
            BigDecimal confirmInspamt = apDetailDO.getInspamt();
            // 减去本条的应付金额，得到srlno对应的，取消后的汇总的应付金额
            BigDecimal inspamt = temp.getInspamt() == null ? BigDecimal.ZERO : temp.getInspamt();
            BigDecimal remainInspamt = confirmInspamt.subtract(inspamt);
            BigDecimal remainInspamtAbs = new BigDecimal(Math.abs(remainInspamt.doubleValue()));

            BigDecimal payamt = temp.getPayamt() == null ? BigDecimal.ZERO : temp.getPayamt();
            BigDecimal payamtAbs = new BigDecimal(Math.abs(payamt.doubleValue()));

            if (remainInspamtAbs.compareTo(payamtAbs) > 0) {
                throw new ServiceException(1_011_400_999, "编号为【" + temp.getPayitemno() + "】的报支记录明细档，对应应付账款暂估档的应付金额汇总值不能大于付款金额！");
            } else {
                epDetailDO.setStus("U");
            }
            epDetailDO.setPayinspamt(remainInspamt);
            epUpdateList.add(epDetailDO);
        }
        epDetailMapper.updateBatch(epUpdateList);
        apMainDO.setPaystus("N");

        apMainDO.setTotalAmt(BigDecimal.ZERO);
        apMainDO.setTotalTaxamt(BigDecimal.ZERO);
        apMainDO.setTotalAfteramt(BigDecimal.ZERO);
        String dateStr = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATE_PATTERN);
        String timeStr = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_TIME_PATTERN);
        apMainDO.setUpdemplno(getLoginUserId().toString());
        apMainDO.setUpdateEmpNo(getLoginUserNickname());
        apMainDO.setUpdDate(dateStr);
        apMainDO.setUpdTime(timeStr);
        apMainMapper.updateById(apMainDO);

    }

    @Override
    public void cancelConfirmApMainYF(Long id) {
        ApMainDO apMainDO = apMainMapper.selectById(id);
        // 校验，取消确认只能取消已经确认的主档
        if (!"Y".equals(apMainDO.getPaystus())) {
            throw exception(AP_MAIN_ERROR_PAY_STUS);
        }
        // 检核 1、校验信息是否都已填写
        List<ApDetailDO> detailDOList = apDetailMapper.selectByParentid(id);
        if (CollectionUtil.isEmpty(detailDOList)) {
            throw exception(AP_DETAIL1_NO_DATA_YF);
        }
        apMainDO.setTotalAmt(BigDecimal.ZERO);
        String dateStr = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATE_PATTERN);
        String timeStr = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_TIME_PATTERN);
        apMainDO.setUpdemplno(getLoginUserId().toString());
        apMainDO.setUpdateEmpNo(getLoginUserNickname());
        apMainDO.setUpdDate(dateStr);
        apMainDO.setUpdTime(timeStr);
        apMainDO.setPaystus("N");
        apMainMapper.updateById(apMainDO);
    }

    // 回写主档数据
    private void linkApMainDO(ApMainDO apMainDO, List<ApDetail2DO> doList) {
        BigDecimal totalamtAll = BigDecimal.ZERO;
        BigDecimal taxamtAll = BigDecimal.ZERO;
        BigDecimal amtAll = BigDecimal.ZERO;
        for (ApDetail2DO temp : doList) {
            // 税后 totalamt
            // 税额 taxamt
            // 税前 amt
            BigDecimal totalamt = temp.getTotalAmt() == null ? BigDecimal.ZERO : temp.getTotalAmt();
            totalamtAll = totalamtAll.add(totalamt).setScale(4, RoundingMode.HALF_UP);
            BigDecimal amt = temp.getAmt() == null ? BigDecimal.ZERO : temp.getAmt();
            amtAll = amtAll.add(amt).setScale(4, RoundingMode.HALF_UP);
            BigDecimal taxamt = temp.getTaxamt() == null ? BigDecimal.ZERO : temp.getTaxamt();
            taxamtAll = taxamtAll.add(taxamt).setScale(4, RoundingMode.HALF_UP);
        }
        apMainDO.setTotalAmt(amtAll);
        apMainDO.setTotalTaxamt(taxamtAll);
        apMainDO.setTotalAfteramt(totalamtAll);
        String dateStr = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATE_PATTERN);
        String timeStr = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_TIME_PATTERN);
        apMainDO.setUpdemplno(getLoginUserId().toString());
        apMainDO.setUpdateEmpNo(getLoginUserNickname());
        apMainDO.setUpdDate(dateStr);
        apMainDO.setUpdTime(timeStr);
        apMainMapper.updateById(apMainDO);
    }

    private void validateApDetail2Exists(Long id) {
        if (apDetail2Mapper.selectById(id) == null) {
            throw exception(AP_DETAIL2_NOT_EXISTS);
        }
    }

    private void deleteApDetail2ByParentid(Long parentid) {
        apDetail2Mapper.deleteByParentid(parentid);
    }


    @Override
    public ApDetailDO getApPayInfo(Long id) {
        return apDetailMapper.getApPayInfo(id);
    }

    @Override
    @Transactional
    public Long createApPayInfo(ApPayInfoSaveReqVO reqVO) {
        if (payInfoMapper.selectOne(PayInfoDO::getParentId, reqVO.getParentId()) != null) {
            throw exception(AP_PAYINFO_NOT_EXISTS);
        }
        // 插入
        PayInfoDO payInfoDO = BeanUtils.toBean(reqVO, PayInfoDO.class);
        payInfoDO.setProject(getLoginUserTopDeptId().toString());
        payInfoDO.setCompid(getLoginUserTenantId().toString());
        payInfoDO.setCreateEmpNo(getLoginUserNickname());
        payInfoDO.setUpdateEmpNo(getLoginUserNickname());
        payInfoDO.setCreateDate(DateUtil.getDate());
        payInfoDO.setUpdateDate(DateUtil.getDate());
        payInfoMapper.insert(payInfoDO);

        EpDetailDO epDetailDO = epDetailMapper.selectBySrlno(reqVO.getSrlno());
        if ("Y".equals(payInfoDO.getPaystus())) {
            epDetailDO.setStus("Y");
            epDetailDO.setPayinspamt(epDetailDO.getInspamt());
        } else {
            epDetailDO.setStus("N");
            epDetailDO.setPayinspamt(new BigDecimal(0));
        }
        epDetailMapper.updateById(epDetailDO);


        PoMainDO poMainDO = poMainMapper.selectByPono(reqVO.getPono());
        if (poMainDO != null) {
            if ("A".equals(poMainDO.getPaytype())) {// 付款方式-信用证
                EpDetailDO epDetailDO2 = epDetailMapper.selectBySrlno(reqVO.getSrlno());
                if ("R".equals(epDetailDO2.getScrapkind())) {// 付款类型-货款
                    LCRegisterDO lcRegisterDO = lCRegisterMapper.selectOne(LCRegisterDO::getPoNo, reqVO.getPono(), LCRegisterDO::getMatrlNo, epDetailDO2.getMatrlno());
                    if (lcRegisterDO == null) {
                        throw exception(LC_REGISTER_NOT_EXISTS2);
                    } else {
                        if ("Y".equals(payInfoDO.getPaystus())) {
                            LcUsageDO lcUsageDO = lcUsageMapper.selectOne(LcUsageDO::getBankCode, lcRegisterDO.getIssuingBankCode());
                            lcUsageDO.setRemainingLimit(lcUsageDO.getRemainingLimit().add(epDetailDO2.getInspamt()));
                            lcUsageDO.setCurrentUsage(lcUsageDO.getCurrentUsage().subtract(epDetailDO2.getInspamt()));
                            lcUsageDO.setUpdateEmpNo(getLoginUserNickname());
                            lcUsageDO.setUpdater(String.valueOf(getLoginUserId()));
                            lcUsageDO.setUpdateDate(DateUtil.getDate());
                            lcUsageMapper.updateById(lcUsageDO);

                            lcRegisterDO.setPaidAmount(lcRegisterDO.getPaidAmount().add(epDetailDO2.getInspamt()));
                            lcRegisterDO.setRemainingAmount(lcUsageDO.getRemainingLimit());
                            lcRegisterDO.setUpdateEmpNo(getLoginUserNickname());
                            lcRegisterDO.setUpdater(String.valueOf(getLoginUserId()));
                            lcRegisterDO.setUpdateDate(DateUtil.getDate());
                            lCRegisterMapper.updateById(lcRegisterDO);
                        }
                    }
                }

            }
        }
        return payInfoDO.getId();
    }

    @Override
    @Transactional
    public void updateApPayInfo(ApPayInfoSaveReqVO reqVO) {
        PayInfoDO oldPayInfoDO = payInfoMapper.selectById(reqVO.getId());
        if (oldPayInfoDO == null) {
            throw exception(AP_PAYINFO_NOT_EXISTS);
        }
        PayInfoDO payInfoDO = BeanUtils.toBean(reqVO, PayInfoDO.class);
        payInfoDO.setUpdateEmpNo(getLoginUserNickname());
        payInfoDO.setUpdateDate(DateUtil.getDate());
        payInfoMapper.updateById(payInfoDO);
        EpDetailDO epDetailDO = epDetailMapper.selectBySrlno(reqVO.getSrlno());
        if ("Y".equals(payInfoDO.getPaystus())) {
            epDetailDO.setStus("Y");
            epDetailDO.setPayinspamt(epDetailDO.getInspamt());
        } else {
            epDetailDO.setStus("N");
            epDetailDO.setPayinspamt(new BigDecimal(0));
        }
        epDetailMapper.updateById(epDetailDO);

        PoMainDO poMainDO = poMainMapper.selectByPono(reqVO.getPono());
        if (poMainDO != null) {
            if ("A".equals(poMainDO.getPaytype())) {// 付款方式-信用证
                EpDetailDO epDetailDO2 = epDetailMapper.selectBySrlno(reqVO.getSrlno());
                if ("R".equals(epDetailDO.getScrapkind())) {// 付款类型-货款
                    LCRegisterDO lcRegisterDO = lCRegisterMapper.selectOne(LCRegisterDO::getPoNo, reqVO.getPono(), LCRegisterDO::getMatrlNo, epDetailDO2.getMatrlno());
                    if (lcRegisterDO == null) {
                        throw exception(LC_REGISTER_NOT_EXISTS2);
                    } else {
                        if ("Y".equals(payInfoDO.getPaystus()) && "N".equals(oldPayInfoDO.getPaystus())) {// 由N变Y 释放占用额度
                            LcUsageDO lcUsageDO = lcUsageMapper.selectOne(LcUsageDO::getBankCode, lcRegisterDO.getIssuingBankCode());
                            lcUsageDO.setRemainingLimit(lcUsageDO.getRemainingLimit().add(epDetailDO2.getInspamt()));
                            lcUsageDO.setCurrentUsage(lcUsageDO.getCurrentUsage().subtract(epDetailDO2.getInspamt()));
                            lcUsageDO.setUpdateEmpNo(getLoginUserNickname());
                            lcUsageDO.setUpdater(String.valueOf(getLoginUserId()));
                            lcUsageDO.setUpdateDate(DateUtil.getDate());
                            lcUsageMapper.updateById(lcUsageDO);

                            lcRegisterDO.setPaidAmount(lcRegisterDO.getPaidAmount().add(epDetailDO2.getInspamt()));
                            lcRegisterDO.setRemainingAmount(lcUsageDO.getRemainingLimit());
                            lcRegisterDO.setUpdateEmpNo(getLoginUserNickname());
                            lcRegisterDO.setUpdater(String.valueOf(getLoginUserId()));
                            lcRegisterDO.setUpdateDate(DateUtil.getDate());
                            lCRegisterMapper.updateById(lcRegisterDO);
                        } else if ("N".equals(payInfoDO.getPaystus()) && "Y".equals(oldPayInfoDO.getPaystus())) {// 由Y变N 占用额度
                            LcUsageDO lcUsageDO = lcUsageMapper.selectOne(LcUsageDO::getBankCode, lcRegisterDO.getIssuingBankCode());
                            lcUsageDO.setRemainingLimit(lcUsageDO.getRemainingLimit().subtract(epDetailDO2.getInspamt()));
                            lcUsageDO.setCurrentUsage(lcUsageDO.getCurrentUsage().add(epDetailDO2.getInspamt()));
                            lcUsageDO.setUpdateEmpNo(getLoginUserNickname());
                            lcUsageDO.setUpdater(String.valueOf(getLoginUserId()));
                            lcUsageDO.setUpdateDate(DateUtil.getDate());
                            lcUsageMapper.updateById(lcUsageDO);

                            lcRegisterDO.setPaidAmount(lcRegisterDO.getPaidAmount().subtract(epDetailDO2.getInspamt()));
                            lcRegisterDO.setRemainingAmount(lcUsageDO.getRemainingLimit());
                            lcRegisterDO.setUpdateEmpNo(getLoginUserNickname());
                            lcRegisterDO.setUpdater(String.valueOf(getLoginUserId()));
                            lcRegisterDO.setUpdateDate(DateUtil.getDate());
                            lCRegisterMapper.updateById(lcRegisterDO);

                        }
                    }
                }

            }
        }
    }
}

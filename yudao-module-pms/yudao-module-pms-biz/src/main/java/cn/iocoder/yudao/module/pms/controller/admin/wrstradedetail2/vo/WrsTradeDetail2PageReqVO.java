package cn.iocoder.yudao.module.pms.controller.admin.wrstradedetail2.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 原料待结算明细分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WrsTradeDetail2PageReqVO extends PageParam {

    @Schema(description = "申请单号")
    private String issueTallyNo;

    @Schema(description = "流水序号")
    private String seqNo;

    @Schema(description = "储位")
    private String stgNo;

    @Schema(description = "交易种类")
    private String issueType;

    @Schema(description = "供应商代码")
    private String supplierNo;

    @Schema(description = "合同编号")
    private String contractNo;

    @Schema(description = "原料料号")
    private String matrlNo;

//    @Schema(description = "用途别")
//    private String purposeId;
//
//    @Schema(description = "系统别")
//    private String systemId;
//
//    @Schema(description = "作业代码")
//    private String appId;
//
//    @Schema(description = "状态")
//    private String stus;


    @Schema(description = "是否贸易")
    private String isMy;

}
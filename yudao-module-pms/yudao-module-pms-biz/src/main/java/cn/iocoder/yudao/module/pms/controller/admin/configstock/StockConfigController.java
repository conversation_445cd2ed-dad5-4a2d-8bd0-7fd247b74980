package cn.iocoder.yudao.module.pms.controller.admin.configstock;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;
import cn.iocoder.yudao.module.pms.controller.admin.configstock.vo.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.configstock.StockConfigDO;
import cn.iocoder.yudao.module.pms.service.configstock.StockConfigService;

@Tag(name = "管理后台 - 库存配置管理")
@RestController
@RequestMapping("/pms/stock-config")
@Validated
public class StockConfigController {

    @Resource
    private StockConfigService stockConfigService;

    @PostMapping("/create")
    @Operation(summary = "创建库存配置管理")
    @PreAuthorize("@ss.hasPermission('pms:stock-config:create')")
    public CommonResult<Long> createStockConfig(@Valid @RequestBody StockConfigSaveReqVO createReqVO) {
        return success(stockConfigService.createStockConfig(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新库存配置管理")
    @PreAuthorize("@ss.hasPermission('pms:stock-config:update')")
    public CommonResult<Boolean> updateStockConfig(@Valid @RequestBody StockConfigSaveReqVO updateReqVO) {
        stockConfigService.updateStockConfig(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除库存配置管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:stock-config:delete')")
    public CommonResult<Boolean> deleteStockConfig(@RequestParam("id") Long id) {
        stockConfigService.deleteStockConfig(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得库存配置管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:stock-config:query')")
    public CommonResult<StockConfigRespVO> getStockConfig(@RequestParam("id") Long id) {
        StockConfigDO stockConfig = stockConfigService.getStockConfig(id);
        return success(BeanUtils.toBean(stockConfig, StockConfigRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得库存配置管理分页")
    @PreAuthorize("@ss.hasPermission('pms:stock-config:query')")
    public CommonResult<PageResult<StockConfigRespVO>> getStockConfigPage(@Valid StockConfigPageReqVO pageReqVO) {
        PageResult<StockConfigDO> pageResult = stockConfigService.getStockConfigPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StockConfigRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出库存配置管理 Excel")
    @PreAuthorize("@ss.hasPermission('pms:stock-config:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportStockConfigExcel(@Valid StockConfigPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<StockConfigDO> list = stockConfigService.getStockConfigPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "库存配置管理.xls", "数据", StockConfigRespVO.class,
                        BeanUtils.toBean(list, StockConfigRespVO.class));
    }

}
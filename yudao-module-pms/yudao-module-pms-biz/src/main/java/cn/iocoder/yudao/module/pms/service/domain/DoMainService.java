package cn.iocoder.yudao.module.pms.service.domain;

import java.util.*;
import javax.validation.*;

import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.yudao.module.pms.api.pomain.dto.DoMainRespDTO;
import cn.iocoder.yudao.module.pms.api.pomain.dto.DoMainSaveReqDTO;
import cn.iocoder.yudao.module.pms.api.pomain.dto.PoMainReqDTO;
import cn.iocoder.yudao.module.pms.controller.admin.domain.vo.*;
import cn.iocoder.yudao.module.pms.controller.app.domain.vo.AppGrDoShSaveVO;
import cn.iocoder.yudao.module.pms.dal.dataobject.domain.DoMainDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.domain.DoDetailDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 交货单主档 Service 接口
 *
 * <AUTHOR>
 */
public interface DoMainService {

    /**
     * 创建交货单主档
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDoMain(@Valid DoMainSaveReqVO createReqVO);

    /**
     * 更新交货单主档
     *
     * @param updateReqVO 更新信息
     */
    void updateDoMain(@Valid DoMainSaveReqVO updateReqVO);

    /**
     * 删除交货单主档
     *
     * @param id 编号
     */
    void deleteDoMain(Long id);

    /**
     * 获得交货单主档
     *
     * @param id 编号
     * @return 交货单主档
     */
    DoMainRespVO getDoMain(Long id);


    /**
     * 根据收货单号获得交货单主档
     *
     * @param receiveno 收货单号
     * @return 交货单主档
     */
    DoMainDO getDoMainByReceiveno(String receiveno);
    /**
     * 获得交货单主档分页
     *
     * @param pageReqVO 分页查询
     * @return 交货单主档分页
     */
    PageResult<DoMainRespVO> getDoMainPage(DoMainPageReqVO pageReqVO);

    // ==================== 子表（交货单明细） ====================

    /**
     * 获得交货单明细分页
     *
     * @param pageReqVO 分页查询
     * @param parentId 父节点编号
     * @return 交货单明细分页
     */
    PageResult<DoDetailRespVO> getDoDetailPage(DoDetailPageReqVO pageReqVO);

    /**
     * 创建交货单明细
     *
     * @param doDetail 创建信息
     * @return 编号
     */
    Long createDoDetail(@Valid DoDetailDO doDetail);

    /**
     * 更新交货单明细
     *
     * @param doDetail 更新信息
     */
    void updateDoDetail(@Valid DoDetailDO doDetail);

    /**
     * 删除交货单明细
     *
     * @param id 编号
     */
    void deleteDoDetail(Long id);

	/**
	 * 获得交货单明细
	 *
	 * @param id 编号
     * @return 交货单明细
	 */
    DoDetailRespVO getDoDetail(Long id);

    void confirmDoMain(AppGrDoShSaveVO confirmVO);

    void cancelConfirmDoMain(Long id);

    PageResult<PoDoMainRespVO> getPoDoMainPage(DoMainPageReqVO pageReqVO);

    List<DoMainRespDTO> getDoMainListByVendor(PoMainReqDTO reqDTO);

    String createDoInfo(DoMainSaveReqDTO reqDT);
    Map<Long,Long> countDetailByPoDoMainId(List<Long> grMainIds);

    void batchInsertDoDetails(DoDetailBatchInsertReqVO reqVO);
}

package cn.iocoder.yudao.module.pms.controller.admin.potoccontent.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 合同条款内容-按行保存新增/修改 Request VO")
@Data
public class PoTocContentSaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "4243")
    private Long id;

    @Schema(description = "合同id - pms_po_main", requiredMode = Schema.RequiredMode.REQUIRED, example = "18198")
    @NotNull(message = "合同id - pms_po_main不能为空")
    private Long parentId;

    @Schema(description = "序号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "序号不能为空")
    private Integer srlno;

    @Schema(description = "条款内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "条款内容不能为空")
    private String content;

    @Schema(description = "预留字段1 ")
    private String preText1;

    @Schema(description = "预留字段2 ")
    private String preText2;

    @Schema(description = "预留字段3 ")
    private String preText3;

    @Schema(description = "预留字段4 ")
    private String preText4;

    @Schema(description = "预留字段5 ")
    private String preText5;

    @Schema(description = "预留字段6 ")
    private String preText6;

    @Schema(description = "预留字段7 ")
    private String preText7;

    @Schema(description = "预留字段8 ")
    private String preText8;

    @Schema(description = "预留字段9 ")
    private String preText9;

    @Schema(description = "预留字段10 ")
    private String preText10;

    @Schema(description = "预留字段11 ")
    private BigDecimal preNum11;

    @Schema(description = "预留字段12 ")
    private BigDecimal preNum12;

    @Schema(description = "预留字段13 ")
    private BigDecimal preNum13;

    @Schema(description = "预留字段14 ")
    private BigDecimal preNum14;

    @Schema(description = "预留字段15 ")
    private BigDecimal preNum15;

    @Schema(description = "类型 termContent:合同条款  termSupplementContent:合同补增条款", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "类型 termContent:合同条款  termSupplementContent:合同补增条款不能为空")
    private String type;

}
package cn.iocoder.yudao.module.pms.controller.admin.policyinfo.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;
import java.math.BigDecimal;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 保单基本信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PolicyInfoPageReqVO extends PageParam {

    @Schema(description = "父id")
    private Long parentId;

    @Schema(description = "申请单号")
    private String poNo;

    @Schema(description = "发票号")
    private String voucherNo;

    @Schema(description = "发票金额")
    private BigDecimal voucherAmount;

    @Schema(description = "投保加成")
    private BigDecimal plus;

    @Schema(description = "货物类型")
    private String goodsCategory;

    @Schema(description = "湿重数量")
    private BigDecimal quantity;

    @Schema(description = "品名")
    private String matrlName;

    @Schema(description = "保险金额")
    private BigDecimal policyAmt;

    @Schema(description = "币别")
    private String crcy;

    @Schema(description = "发港日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] actlArivDate;

    @Schema(description = "发港日期（英文）")
    private String actlArivDateEn;

    @Schema(description = "船名")
    private String shipName;

    @Schema(description = "船名（英文）")
    private String shipNameEn;

    @Schema(description = "发港名称")
    private String loadPort;

    @Schema(description = "发港名称（英文）")
    private String loadPortEn;

    @Schema(description = "中转港名称")
    private String viaPort;

    @Schema(description = "中转港名称（英文）")
    private String viaPortEn;

    @Schema(description = "卸港名称")
    private String relsPort;

    @Schema(description = "卸港名称（英文）")
    private String relsPortEn;

    @Schema(description = "提单号")
    private String blNo;

    @Schema(description = "赔款偿付地点")
    private String claimPayableAt;

    @Schema(description = "赔款偿付地点（英文）")
    private String claimPayableAtEn;

    @Schema(description = "投保险别")
    private String insuranceConditions;

    @Schema(description = "集装箱种类")
    private String container;

    @Schema(description = "船籍")
    private String shipRegistry;

    @Schema(description = "船龄")
    private String shipAge;

    @Schema(description = "合同创建人")
    private String poCreateEmp;

    @Schema(description = "投保日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] policyDate;

    @Schema(description = "电话")
    private String tel;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "费率")
    private BigDecimal rate;

    @Schema(description = "保费")
    private BigDecimal premium;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "经办人")
    private String operator;

    @Schema(description = "核保人")
    private String underwriter;

    @Schema(description = "负责人")
    private String manager;

}
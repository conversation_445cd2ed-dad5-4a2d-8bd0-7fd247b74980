package cn.iocoder.yudao.module.pms.controller.admin.tradedetail;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.pms.controller.admin.tradedetail.vo.TradeDetailPageReqVO;
import cn.iocoder.yudao.module.pms.controller.admin.tradedetail.vo.TradeDetailRespVO;
import cn.iocoder.yudao.module.pms.controller.admin.tradedetail.vo.TradeDetailSaveReqVO;
import cn.iocoder.yudao.module.pms.dal.dataobject.tradedetail.TradeDetailDO;
import cn.iocoder.yudao.module.pms.service.tradedetail.TradeDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 交易申请明细")
@RestController
@RequestMapping("/pms/trade-detail")
@Validated
public class TradeDetailController {

    @Resource
    private TradeDetailService tradeDetailService;

    @PostMapping("/create")
    @Operation(summary = "创建交易申请明细")
    @PreAuthorize("@ss.hasPermission('wms:trade-detail:create')")
    public CommonResult<Long> createTradeDetail(@Valid @RequestBody TradeDetailSaveReqVO createReqVO) {
        return success(tradeDetailService.createTradeDetail(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新交易申请明细")
    @PreAuthorize("@ss.hasPermission('wms:trade-detail:update')")
    public CommonResult<Boolean> updateTradeDetail(@Valid @RequestBody TradeDetailSaveReqVO updateReqVO) {
        tradeDetailService.updateTradeDetail(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除交易申请明细")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('wms:trade-detail:delete')")
    public CommonResult<Boolean> deleteTradeDetail(@RequestParam("id") Long id) {
        tradeDetailService.deleteTradeDetail(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得交易申请明细")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('wms:trade-detail:query')")
    public CommonResult<TradeDetailRespVO> getTradeDetail(@RequestParam("id") Long id) {
        TradeDetailDO tradeDetail = tradeDetailService.getTradeDetail(id);
        return success(BeanUtils.toBean(tradeDetail, TradeDetailRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得交易申请明细分页")
    @PreAuthorize("@ss.hasPermission('wms:trade-detail:query')")
    public CommonResult<PageResult<TradeDetailRespVO>> getTradeDetailPage(@Valid TradeDetailPageReqVO pageReqVO) {
        PageResult<TradeDetailDO> pageResult = tradeDetailService.getTradeDetailPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TradeDetailRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出交易申请明细 Excel")
    @PreAuthorize("@ss.hasPermission('wms:trade-detail:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTradeDetailExcel(@Valid TradeDetailPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TradeDetailDO> list = tradeDetailService.getTradeDetailPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "交易申请明细.xls", "数据", TradeDetailRespVO.class,
                        BeanUtils.toBean(list, TradeDetailRespVO.class));
    }

}
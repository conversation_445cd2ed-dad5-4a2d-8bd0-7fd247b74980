package cn.iocoder.yudao.module.pms.service.mrs;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.redis.lock.RedisDistributedLock;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.basic.api.BasicApi;
import cn.iocoder.yudao.module.basic.api.dto.*;
import cn.iocoder.yudao.module.pms.controller.admin.inspectionbatch.vo.InspectionBatchSaveReqVO;
import cn.iocoder.yudao.module.pms.controller.admin.wmstrade.vo.WmsTradeSaveReqVO;
import cn.iocoder.yudao.module.pms.dal.dataobject.inspectionbatch.InspectionBatchDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.inspectionbatch.InspectionBatchPhysicalDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.pomain.PoDetailDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.wmstrade.WmsTradeDO;
import cn.iocoder.yudao.module.pms.dal.mysql.pomain.PoDetailMapper;
import cn.iocoder.yudao.module.pms.service.inspectionbatch.InspectionBatchService;
import cn.iocoder.yudao.module.pms.service.wmstrade.WmsTradeService;
import cn.iocoder.yudao.module.system.api.dept.DeptApi;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.flowable.cmmn.model.Case;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Supplier;

import cn.iocoder.yudao.module.pms.controller.admin.mrs.vo.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.mrs.MaterialReceiptStorageDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.pms.dal.mysql.mrs.MaterialReceiptStorageMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.*;
import static cn.iocoder.yudao.module.pms.enums.ErrorCodeConstants.*;

/**
 * 验收入储 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MaterialReceiptStorageServiceImpl implements MaterialReceiptStorageService {

    @Resource
    private MaterialReceiptStorageMapper materialReceiptStorageMapper;
    @Resource
    private PoDetailMapper poDetailMapper;
    @Resource
    private BasicApi basicApi;

    @Override
    public Long createMaterialReceiptStorage(MaterialReceiptStorageSaveReqVO createReqVO) {
        // 插入
        MaterialReceiptStorageDO materialReceiptStorage = BeanUtils.toBean(createReqVO, MaterialReceiptStorageDO.class);

        materialReceiptStorage.setProject(String.valueOf(getLoginUserTopDeptId()));
        materialReceiptStorage.setCompid(String.valueOf(TenantContextHolder.getTenantId()));
        materialReceiptStorage.setIsmasacpt("N");
        if(materialReceiptStorage.getImpurityweight()==null){
            materialReceiptStorage.setImpurityweight(BigDecimal.ZERO);
        }
        check(materialReceiptStorage);
        materialReceiptStorageMapper.insert(materialReceiptStorage);
        // 返回
        return materialReceiptStorage.getId();
    }

    private void check(MaterialReceiptStorageDO materialReceiptStorage) {
        List<PoDetailDO> poDetailDOS = poDetailMapper.selectListByPonoMatrlno(materialReceiptStorage.getContractno(), materialReceiptStorage.getMatrlno());
        if(poDetailDOS==null ||poDetailDOS.isEmpty()){
            throw exception(TEADE_COMMON_ERROR,"没有找到合同【"+ materialReceiptStorage.getContractno()+"】和料号【"+ materialReceiptStorage.getMatrlno()+"】的对应关系");
        }
        StorageRespVO storageRespVO = new StorageRespVO();
        storageRespVO.setLayerid(materialReceiptStorage.getStgno());
        storageRespVO = basicApi.getStorage(storageRespVO).getCheckedData();
        if (storageRespVO==null) {
            throw exception(TEADE_COMMON_ERROR,"储位代码【"+ materialReceiptStorage.getStgno()+"】不存在");
        }
    }

    @Override
    public void updateMaterialReceiptStorage(MaterialReceiptStorageSaveReqVO updateReqVO) {
        // 校验存在
        MaterialReceiptStorageDO materialReceiptStorageDO = validateMaterialReceiptStorageExists(updateReqVO.getId());
        if("Y".equals(materialReceiptStorageDO.getIsmasacpt())){
            throw exception(TEADE_COMMON_ERROR,"已经入储不允许操作");
        }

        // 更新
        MaterialReceiptStorageDO updateObj = BeanUtils.toBean(updateReqVO, MaterialReceiptStorageDO.class);
        check(updateObj);
        updateObj.setUpdater(String.valueOf(getLoginUserId()));
        updateObj.setUpdateTime(LocalDateTime.now());
        if(!materialReceiptStorageDO.getStgno().equals(updateObj.getStgno())){
            updateObj.setUpdempnod(String.valueOf(getLoginUserId()));
            updateObj.setUpddated(LocalDateTime.now());
        }
        materialReceiptStorageMapper.updateById(updateObj);
    }

    @Override
    public void deleteMaterialReceiptStorage(Long id) {
        // 校验存在
        MaterialReceiptStorageDO materialReceiptStorageDO = validateMaterialReceiptStorageExists(id);
        if("Y".equals(materialReceiptStorageDO.getIsmasacpt())){
            throw exception(TEADE_COMMON_ERROR,"已经入储不允许操作");
        }
        // 删除
        materialReceiptStorageMapper.deleteById(id);
    }
    @Resource
    private RedisDistributedLock redisDistributedLock;
    public static final String MRS_ISMASACPT="MRS_ISMASACPT:";
    @Resource
    private InspectionBatchService inspectionBatchService;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String batchProcessing(Long[] ids, String chkno){
        AtomicReference<String> matrlno = new AtomicReference<>("");
        AtomicReference<String> senddepttype = new AtomicReference<>("");
        AtomicReference<String> contractno = new AtomicReference<>("");
        AtomicReference<String> sendstation = new AtomicReference<>("");

        AtomicReference<String> manualchkno = new AtomicReference<>("");
        BigDecimal water = BigDecimal.ZERO;
        InspectionBatchSaveReqVO createReqVO = new InspectionBatchSaveReqVO();
        if(StringUtils.isEmpty(chkno)){
//            inspectionBatchService.createInspectionBatch(createReqVO);
            chkno="";
            manualchkno.set("");
        } else {
            InspectionBatchDO inspectionBatch = inspectionBatchService.getInspectionBatch(chkno);
            if (inspectionBatch==null) {
                throw exception(TEADE_COMMON_ERROR, "组批号【"+chkno+"】不存在");
            }
            manualchkno.set(inspectionBatch.getManualchkno());
//            MaterialReceiptStorageDO storageDO = this.getMaterialReceiptStorageByChkno(chkno);
//            if(storageDO!=null){
                matrlno.set(inspectionBatch.getMatrlno());
                senddepttype.set(inspectionBatch.getType());
                contractno.set(inspectionBatch.getContractno());
                sendstation.set(inspectionBatch.getSendstation());
//            }
        }
        for (Long id : ids) {
            try {
                if (!redisDistributedLock.tryLock(MRS_ISMASACPT + id)) {
                    throw exception(TEADE_COMMON_ERROR,"操作失败");
                }
                {
                    MaterialReceiptStorageDO storageDO = validateMaterialReceiptStorageExists(id);
                    if (StringUtils.isNotEmpty(storageDO.getChkno())) {
                        throw exception(TEADE_COMMON_ERROR, "车号【"+storageDO.getCarno()+"】已经组批不允许操作");
                    }
                    if ("Y".equals(storageDO.getIsmasacpt())){
                        throw exception(TEADE_COMMON_ERROR, "车号【"+storageDO.getCarno()+"】先取消入储");
                    }
                    if(matrlno.get().isEmpty()){
                        matrlno.set(storageDO.getMatrlno());
                        senddepttype.set(storageDO.getSenddepttype());
                        contractno.set(storageDO.getContractno());
                        sendstation.set(storageDO.getSendstation());


                        createReqVO.setMatrlno(matrlno.get());
                        createReqVO.setType(storageDO.getSenddepttype());
                        createReqVO.setContractno(storageDO.getContractno());
                        createReqVO.setSendstation(storageDO.getSendstation());

                        createReqVO.setSupplierno(storageDO.getSupplierno());
                        createReqVO.setChktype(storageDO.getCarriertype());
                    }
                    if(!matrlno.get().equals(storageDO.getMatrlno())
                            || !senddepttype.get().equals(storageDO.getSenddepttype())
                            || !contractno.get().equals(storageDO.getContractno())
                            || !sendstation.get().equals(storageDO.getSendstation())
                    ){
                        throw exception(TEADE_COMMON_ERROR,"相同料号、发货类别、发货单位、发站、合同号 方可归为一批");
                    }
//                    if(manualchkno.get().isEmpty()){
//                        manualchkno.set(inspectionBatchService.genManualchkno());
//                    }
                    storageDO.setChkno(chkno);
                    storageDO.setManualchkno(manualchkno.get());
                    storageDO.setChkwater(water);
                    storageDO.setUpdempnoa(String.valueOf(getLoginUserId()));
                    storageDO.setUpddatea(LocalDateTime.now());
                    materialReceiptStorageMapper.updateById(storageDO);
                    if (!"Y".equals(storageDO.getIsmasacpt())){
                        continue;
                    }
                    //todo Y
                }
            } finally {
                redisDistributedLock.unlock(MRS_ISMASACPT + id);
            }
        }
        if(chkno.isEmpty()){//第一次组批 没有组批号
            inspectionBatchService.createInspectionBatch(createReqVO);
            chkno= createReqVO.getChkno();
            materialReceiptStorageMapper.updateChknoById(ids,chkno,createReqVO.getManualchkno());
        }
        return chkno;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unBatchProcessing(Long[] ids){
        AtomicReference<String> chkno = new AtomicReference<>("0");
        for (Long id : ids) {
            try {
                if (!redisDistributedLock.tryLock(MRS_ISMASACPT + id)) {
                    throw exception(TEADE_COMMON_ERROR,"操作失败");
                }
                {
                    MaterialReceiptStorageDO storageDO = validateMaterialReceiptStorageExists(id);
                    if (StringUtils.isEmpty(storageDO.getChkno())) {
                        throw exception(TEADE_COMMON_ERROR, "车号【"+storageDO.getCarno()+"】已经取消组批");
                    }
                    if ("Y".equals(storageDO.getIsmasacpt())){
                        throw exception(TEADE_COMMON_ERROR, "车号【"+storageDO.getCarno()+"】先取消入储");
                    }
                    if (!chkno.get().contains(storageDO.getChkno())) {
                        chkno.set(chkno.get()+","+storageDO.getChkno());
                    }
                    storageDO.setChkno("");
                    storageDO.setManualchkno("");
                    storageDO.setChkwater(BigDecimal.ZERO);
                    storageDO.setUpdempnoa(String.valueOf(getLoginUserId()));
                    storageDO.setUpddatea(LocalDateTime.now());
                    materialReceiptStorageMapper.updateById(storageDO);

                    if (!"Y".equals(storageDO.getIsmasacpt())){
                        continue;
                    }
                    //todo Y
                }
            } finally {
                redisDistributedLock.unlock(MRS_ISMASACPT + id);
            }
        }
        for (String chk : chkno.get().split(",")) {
            MaterialReceiptStorageDO storageDO = this.getMaterialReceiptStorageByChkno(chk);
            if (storageDO==null) {
                inspectionBatchService.deleteInspectionBatch(chk);
            }
        }

    }
    @Resource
    private WmsTradeService wmsTradeService;
    private final static BigDecimal hnd = new BigDecimal("100");
    @Override
    public void confirm(Long[] ids){
        Map<String,BigDecimal> chknoWater= new HashMap<>();
        for (Long id : ids) {
            try{
                if(!redisDistributedLock.tryLock(MRS_ISMASACPT+id)){
                    throw exception(TEADE_COMMON_ERROR,"操作失败");
                }
                MaterialReceiptStorageDO storageDO = validateMaterialReceiptStorageExists(id);
                if ("Y".equals(storageDO.getIsmasacpt())) {
                    throw exception(TEADE_COMMON_ERROR, "已经入储不允许操作");
                }
                if(StringUtils.isEmpty(storageDO.getIssuetallyno())) {
                    WmsTradeSaveReqVO reqVO = new WmsTradeSaveReqVO();
                    reqVO.setSystemId("WRS");
                    reqVO.setInventoryType("R");
                    reqVO.setAppId("wmstradea");//验收入库 wmstradeWRS todo
                    reqVO.setInspType("A");
                    reqVO.setIssueType("A");
                    reqVO.setPurposeId("10A");
                    reqVO.setIssueDate(storageDO.getRecvdate());
                    reqVO.setIssueTime(storageDO.getRecvtime());
                    reqVO.setContractNo(storageDO.getContractno());
                    reqVO.setSupplierNo(storageDO.getSupplierno());
                    reqVO.setMatrlNo(storageDO.getMatrlno());
                    reqVO.setStgNo(storageDO.getStgno());
                    reqVO.setAcptDeptNo(storageDO.getRecvdept());
                    reqVO.setAcptDate(storageDO.getRecvdate());
                    reqVO.setScaleNum(storageDO.getScalenum());
                    doCountTransNumAmt(storageDO);
                    reqVO.setTransNum(storageDO.getTransnum());

                    storageDO.setRemark1(wmsTradeService.createWmsTrade(reqVO).toString());
                    storageDO.setIssuetallyno(reqVO.getIssueTallyNo());
                } else {
                    WmsTradeSaveReqVO reqVO = new WmsTradeSaveReqVO();
                    reqVO.setId(Long.parseLong(storageDO.getRemark1()));
                    doCountTransNumAmt(storageDO);
                    reqVO.setTransNum(storageDO.getTransnum());
                    wmsTradeService.updateWmsTrade(reqVO);
                }
                JSONObject result = wmsTradeService.confirmTrade(Long.parseLong(storageDO.getRemark1()));

                storageDO.setPover(result.getString("pover"));
                storageDO.setCtrtunit(result.getBigDecimal("unitPrice"));
                storageDO.setSettleamt(result.getBigDecimal("transAmt"));
                storageDO.setIsmasacpt("Y");
                storageDO.setUpdempnod(String.valueOf(getLoginUserId()));
                storageDO.setUpddated(LocalDateTime.now());

                materialReceiptStorageMapper.updateById(storageDO);
            }finally {
                redisDistributedLock.unlock(MRS_ISMASACPT+id);
            }
        }

    }

    private void doCountTransNumAmt(MaterialReceiptStorageDO storageDO) {
        BigDecimal water = BigDecimal.ZERO;
        MaterialMainDto materialMainDto = basicApi.getMaterialByMaterialNo(storageDO.getMatrlno()).getCheckedData();
        if (materialMainDto ==null) {
            throw exception(TEADE_COMMON_ERROR, "料号【"+storageDO.getMatrlno()+"】不存在");
        }
        String waterSymbol = materialMainDto.getWaterFlag();
        String stowaway = materialMainDto.getStockway();
        if(stowaway==null){
            throw exception(TEADE_COMMON_ERROR, "请维护料号【"+storageDO.getMatrlno()+"】的入储重量依据");
        }
        MaterialDetailDto materialDetailDto = basicApi.getMaterialDetailByMaterialNo(storageDO.getMatrlno()).getCheckedData();
        if(materialDetailDto==null){
            throw exception(TEADE_COMMON_ERROR, "请维护料号【"+storageDO.getMatrlno()+"】管理属性");
        }
        String isCheckSet = materialDetailDto.getIfQualityCheck();//是否需要质检
        BigDecimal stardWaterRate = new BigDecimal(materialDetailDto.getStardwaterrate());//标准水分
        if(!"1".equals(stowaway)&&StringUtils.isEmpty(waterSymbol)){
            throw exception(TEADE_COMMON_ERROR, "料号【"+storageDO.getMatrlno()+"】的水分标识符不存在");
        }
        storageDO.setJointag("C");
        if(!"N".equals(isCheckSet)){
            water=stardWaterRate;
            storageDO.setJointag("B");
            InspectionBatchPhysicalDO physical = inspectionBatchService.getInspectionBatchPhysical(storageDO.getChkno(), waterSymbol);
            if (physical != null) {
                water = physical.getCospec();
                storageDO.setJointag("C");
            }
        }

        storageDO.setSettlenum(storageDO.getScalenum().subtract(storageDO.getImpurityweight()));
        switch (stowaway){
            case "2"://干重
                storageDO.setTransnum(storageDO.getSettlenum().multiply(hnd.subtract(water).divide(hnd,3, RoundingMode.HALF_UP)));
                storageDO.setSettlenum(storageDO.getTransnum());
                break;
            case "3"://标重
                if(water.compareTo(stardWaterRate)>0){
                    storageDO.setTransnum(storageDO.getSettlenum().multiply(hnd.subtract(water).divide(hnd.subtract(stardWaterRate),3, RoundingMode.HALF_UP)));
                    storageDO.setSettlenum(storageDO.getTransnum());
                    break;
                }
            case "1"://湿重
            default:
                storageDO.setTransnum(storageDO.getSettlenum());
        }
        storageDO.setChkwater(water);
    }

    @Override
    public void cancel(Long[] ids){
        for (Long id : ids) {
            try{
                if(!redisDistributedLock.tryLock(MRS_ISMASACPT+id)){
                    throw exception(TEADE_COMMON_ERROR,"操作失败");
                }
                MaterialReceiptStorageDO storageDO = validateMaterialReceiptStorageExists(id);
                if ("N".equals(storageDO.getIsmasacpt())) {
                    throw exception(TEADE_COMMON_ERROR, "已经入储不允许操作");
                }
                wmsTradeService.cancelConfirmTrade(Long.parseLong(storageDO.getRemark1()));
                storageDO.setIsmasacpt("N");
                storageDO.setUpdempnod(String.valueOf(getLoginUserId()));
                storageDO.setUpddated(LocalDateTime.now());
                materialReceiptStorageMapper.updateById(storageDO);
            }finally {
                redisDistributedLock.unlock(MRS_ISMASACPT+id);
            }
        }
    }

    private MaterialReceiptStorageDO validateMaterialReceiptStorageExists(Long id) {
        MaterialReceiptStorageDO materialReceiptStorageDO = materialReceiptStorageMapper.selectById(id);
        if (materialReceiptStorageDO == null) {
            throw exception(MATERIAL_RECEIPT_STORAGE_NOT_EXISTS);
        }
        return materialReceiptStorageDO;
    }

    @Override
    public MaterialReceiptStorageDO getMaterialReceiptStorage(Long id) {
        return materialReceiptStorageMapper.selectById(id);
    }
    @Override
    public MaterialReceiptStorageDO getMaterialReceiptStorageByChkno(String chkno) {
        return materialReceiptStorageMapper.selectByChkno(chkno);
    }

    @Override
    public PageResult<MaterialReceiptStorageDO> getMaterialReceiptStoragePage(MaterialReceiptStoragePageReqVO pageReqVO) {
        return materialReceiptStorageMapper.selectPage(pageReqVO);
    }
    @Resource
    private DeptApi deptApi;
    @Override
    public MrsImportRespVO importMrsList(List<MaterialReceiptStorageRespVO> list, Boolean updateSupport) {
        // 1 参数校验
        if (CollUtil.isEmpty(list)) {
            throw exception(MRP_IMPORT_LIST_IS_EMPTY);
        }
        // 2. 遍历，逐个创建 or 更新
        MrsImportRespVO respVO = MrsImportRespVO.builder()
                .createDetails(new ArrayList<>()).updateDetails(new ArrayList<>())
                .failureDetails(new LinkedHashMap<>()).build();
        list.forEach(detail -> {
            String seq = String.valueOf(detail.getSeq());
            if(detail.getSenddate()==null||detail.getSenddate().isEmpty()||!detail.getSenddate().matches("\\d{4}-\\d{2}-\\d{2}")){
                respVO.getFailureDetails().put(seq,"发货日期不能为空或格式不为yyyy-MM-dd");
                return;
            }
            if(detail.getScalenum()==null){
                respVO.getFailureDetails().put(seq,"检斤量不能为空");
                return;
            }
            if(detail.getRecvdate()==null||detail.getRecvdate().isEmpty()||!detail.getRecvdate().matches("\\d{4}-\\d{2}-\\d{2}")){
                respVO.getFailureDetails().put(seq,"到站日期不能为空或格式不为yyyy-MM-dd");
                return;
            }
            if(detail.getRecvtime()==null||detail.getRecvtime().isEmpty()||!detail.getRecvtime().matches("\\d{2}:\\d{2}:\\d{2}")){
                respVO.getFailureDetails().put(seq,"到站时间不能为空或格式不为HH:mm:ss");
                return;
            }
            if(detail.getContractno()==null||detail.getContractno().isEmpty()){
                respVO.getFailureDetails().put(seq,"合同号不能为空");
                return;
            }
            if(detail.getMatrlno()==null||detail.getMatrlno().isEmpty()){
                respVO.getFailureDetails().put(seq,"料号不能为空");
                return;
            }
            if(detail.getStgno()==null||detail.getStgno().isEmpty()){
                respVO.getFailureDetails().put(seq,"储位不能为空");
                return;
            }
            if(detail.getVchrdate()==null||detail.getVchrdate().isEmpty()||!detail.getVchrdate().matches("\\d{4}-\\d{2}-\\d{2}")){
                respVO.getFailureDetails().put(seq,"入账日期不能为空或格式不为yyyy-MM-dd");
                return;
            }
            if(detail.getInstgdate()!=null&&!detail.getInstgdate().matches("\\d{4}-\\d{2}-\\d{2}")){
                respVO.getFailureDetails().put(seq,"入储日期格式不为yyyy-MM-dd");
                return;
            }
            try {
                MaterialReceiptStorageSaveReqVO materialReceiptStorageSaveReqVO = BeanUtils.toBean(detail, MaterialReceiptStorageSaveReqVO.class);
                if (materialReceiptStorageSaveReqVO.getInnerrecvdept()==null||deptApi.getDept(Long.parseLong(materialReceiptStorageSaveReqVO.getInnerrecvdept())).getCheckedData()==null) {
                    materialReceiptStorageSaveReqVO.setInnerrecvdept("");
                }
                createMaterialReceiptStorage(materialReceiptStorageSaveReqVO);
                respVO.getCreateDetails().add(seq);
            } catch (Exception e){
                e.printStackTrace();
                respVO.getFailureDetails().put(seq,e.getMessage());
            }

        });
        return respVO;
    }

}
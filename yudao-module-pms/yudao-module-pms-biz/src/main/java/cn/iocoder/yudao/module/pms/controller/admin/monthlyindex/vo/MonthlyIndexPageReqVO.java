package cn.iocoder.yudao.module.pms.controller.admin.monthlyindex.vo;

import lombok.*;

import java.time.LocalDate;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 日月指数分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MonthlyIndexPageReqVO extends PageParam {

    @Schema(description = "记录日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate[] recordDate;

    @Schema(description = "指数类型(1:日指数,2:月指数)", example = "1")
    private Integer indexType;

    @Schema(description = "指数类型名称", example = "赵六")
    private String typeName;

    @Schema(description = "日期开始")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate[] startDate;

    @Schema(description = "日期结束")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate[] endDate;

    @Schema(description = "指数值1")
    private BigDecimal indexValue1;

    @Schema(description = "指数值2")
    private BigDecimal indexValue2;

    @Schema(description = "指数值3")
    private BigDecimal indexValue3;

    @Schema(description = "指数值4")
    private BigDecimal indexValue4;

    @Schema(description = "指数值5")
    private BigDecimal indexValue5;

    @Schema(description = "指数值6")
    private BigDecimal indexValue6;

    @Schema(description = "指数值7")
    private BigDecimal indexValue7;

    @Schema(description = "指数值8")
    private BigDecimal indexValue8;

    @Schema(description = "指数值9")
    private BigDecimal indexValue9;

    @Schema(description = "指数值10")
    private BigDecimal indexValue10;

    @Schema(description = "指数值11")
    private BigDecimal indexValue11;

    @Schema(description = "指数值12")
    private BigDecimal indexValue12;

    @Schema(description = "指数值13")
    private BigDecimal indexValue13;

    @Schema(description = "指数值14")
    private BigDecimal indexValue14;

    @Schema(description = "指数值15")
    private BigDecimal indexValue15;

    @Schema(description = "指数值16")
    private BigDecimal indexValue16;

    @Schema(description = "指数值17")
    private BigDecimal indexValue17;

    @Schema(description = "指数值18")
    private BigDecimal indexValue18;

    @Schema(description = "指数值19")
    private BigDecimal indexValue19;

    @Schema(description = "指数值20")
    private BigDecimal indexValue20;

    @Schema(description = "备用字段1")
    private String backup1;

    @Schema(description = "备用字段2")
    private String backup2;

    @Schema(description = "备用字段3")
    private String backup3;

    @Schema(description = "备用字段4")
    private String backup4;

    @Schema(description = "备用字段5")
    private String backup5;

    @Schema(description = "备用字段6")
    private String backup6;

    @Schema(description = "备用字段7")
    private String backup7;

    @Schema(description = "备用字段8")
    private String backup8;

    @Schema(description = "备用字段9")
    private String backup9;

    @Schema(description = "备用字段10")
    private String backup10;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDateTime[] createTime;

    private String isAvg;

}
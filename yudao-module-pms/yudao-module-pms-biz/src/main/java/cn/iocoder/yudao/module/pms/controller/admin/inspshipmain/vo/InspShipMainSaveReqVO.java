package cn.iocoder.yudao.module.pms.controller.admin.inspshipmain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 船运验收入储主档新增/修改 Request VO")
@Data
public class InspShipMainSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "项目号")
    private String project;

    @Schema(description = "公司别")
    private String compId;

    @Schema(description = "检验批号")
    private String chkNo;

    @Schema(description = "入储日期")
    private String inStgDate;

    @Schema(description = "重量验收状态")
    private String isMasAcpt;

    @Schema(description = "原料料号")
    private String matrlNo;

    @Schema(description = "品名")
    private String matrlName;

    @Schema(description = "合同号")
    private String contractNo;

    @Schema(description = "供应商代码")
    private String supplierNo;

    @Schema(description = "供应商")
    private String supplyName;

    @Schema(description = "合同数量")
    private BigDecimal ctrtNum;

    @Schema(description = "合同单价")
    private BigDecimal ctrtUnit;

    @Schema(description = "合同水")
    private BigDecimal ctrtWater;

    @Schema(description = "交运条件")
    private String delvType;

    @Schema(description = "合同种类")
    private String ctrtType;

    @Schema(description = "船运公司")
    private String shipCom;

    @Schema(description = "船名/航次")
    private String shipName;

    @Schema(description = "船运合同")
    private String shipCtrt;

    @Schema(description = "船运类别")
    private String shipType;

    @Schema(description = "装运港名称")
    private String loadPort;

    @Schema(description = "装运港名称")
    private String loadPortOut;

    @Schema(description = "预计装载港离港日期")
    private String exptLdPtLvDate;

    @Schema(description = "实际装载港离港日期")
    private String actlLdPtLvDate;

    @Schema(description = "卸港码头名称")
    private String relsPort;

    @Schema(description = "预计到达日期")
    private String exptArivDate;

    @Schema(description = "实际到达日期")
    private String actlArivDate;

    @Schema(description = "卸空日期时间")
    private String finishJobDate;

    @Schema(description = "卸空时间")
    private String finishJobTime;

    @Schema(description = "卸船系统")
    private String dumpNo;

    @Schema(description = "下抛计量和质检的状态")
    private String isSend;

    @Schema(description = "秤量室")
    private String scaleRoom;

    @Schema(description = "水量基准")
    private BigDecimal waterBase;

    @Schema(description = "当前层数")
    private String lwCurrent;

    @Schema(description = "分层状态")
    private String lwStus;

    @Schema(description = "商检局名")
    private String tradeAgent;

    @Schema(description = "验收重量依据")
    private String masAcptBase;

    @Schema(description = "化验室")
    private String testRoom;

    @Schema(description = "化验水")
    private BigDecimal chkWater;

    @Schema(description = "装港数量[湿]")
    private BigDecimal loadNum;

    @Schema(description = "储存损耗[干]")
    private BigDecimal impurityNum;

    @Schema(description = "途耗-移出途耗库[干]")
    private BigDecimal impurityOutNum;

    @Schema(description = "途耗-移入途耗库[干]")
    private BigDecimal impurityDryNum;

    @Schema(description = "移出量[干]")
    private BigDecimal issueNum;

    @Schema(description = "报港数量[湿]")
    private BigDecimal dumpSettleNum;

    @Schema(description = "商检湿重[湿]")
    private BigDecimal scaleWetNum;

    @Schema(description = "商检干重[干]")
    private BigDecimal scaleDryNum;

    @Schema(description = "入储数量")
    private BigDecimal transNum;

    @Schema(description = "入储储位")
    private String stgNo;

    @Schema(description = "车数")
    private BigDecimal carNum;

    @Schema(description = "运杂费暂估单价")
    private BigDecimal blendUnitAmt;

    @Schema(description = "途耗金额")
    private BigDecimal blendAmt;

    @Schema(description = "验收数量")
    private BigDecimal settleNum;

    @Schema(description = "结算金额")
    private BigDecimal settleAmt;

    @Schema(description = "MP结算金额")
    private BigDecimal mpSettleAmt;

    @Schema(description = "MP结算数量")
    private BigDecimal mpSettleNum;

    @Schema(description = "入帐日期")
    private String vchrDate;

    @Schema(description = "申请单号")
    private String issueTallyNo;

    @Schema(description = "水运类别")
    private String shipReportType;

    @Schema(description = "关联批号")
    private String extraChkNo;

    @Schema(description = "签证地(废钢)")
    private String visaLoc;

    @Schema(description = "运输公司编码(船转车)")
    private String carTeamNo;

    @Schema(description = "运输公司说明")
    private String carTeamDesc;

    @Schema(description = "计划起运日期")
    private String transSDate;

    @Schema(description = "计划完成日期")
    private String transEDate;

    @Schema(description = "订购性质")
    private String poType;

    @Schema(description = "合同版次")
    private String poVer;

    @Schema(description = "GPS运输计划")
    private String planListNo;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "关单号")
    private String customsNo;

    @Schema(description = "是否缴税")
    private String isCustoms;

    @Schema(description = "缴税日期")
    private String customsDate;

    @Schema(description = "缴税金额")
    private BigDecimal customsAmt;

    @Schema(description = "结关日期起")
    private String customsEndSDate;

    @Schema(description = "结关日期讫")
    private String customsEndEDate;

    @Schema(description = "创建人")
    private String createEmpNo;

    @Schema(description = "创建日期")
    private String createDate;

    @Schema(description = "更新人A")
    private String updateEmpNoA;

    @Schema(description = "更新日期A")
    private String updateDateA;

    @Schema(description = "更新人B")
    private String updateEmpNoB;

    @Schema(description = "更新日期B")
    private String updateDateB;

    @Schema(description = "更新人")
    private String updateEmpNo;

    @Schema(description = "更新日期")
    private String updateDate;

}
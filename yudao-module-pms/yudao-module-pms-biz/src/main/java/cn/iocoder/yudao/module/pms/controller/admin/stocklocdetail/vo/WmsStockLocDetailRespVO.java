package cn.iocoder.yudao.module.pms.controller.admin.stocklocdetail.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 实时库存明细 Response VO")
@Data
@ExcelIgnoreUnannotated
public class WmsStockLocDetailRespVO {

    @Schema(description = "库存id")
    @ExcelProperty("库存id")
    private Long id;
    @Schema(description = "项目号")
    @ExcelProperty("项目号")
    private String project;

    @Schema(description = "料号")
    @ExcelProperty("料号")
    private String matrlno;

    @Schema(description = "品级（备件属性）")
    @ExcelProperty("品级（备件属性）")
    private String matrlgrade;

    @Schema(description = "品别")
    @ExcelProperty("品别")
    private String inventorytype;

    @Schema(description = "储位")
    @ExcelProperty("储位")
    private String locno;

    @Schema(description = "批次")
    @ExcelProperty("批次")
    private String batch;

    @Schema(description = "期末库存量")
    @ExcelProperty("期末库存量")
    private BigDecimal endqty;

    @Schema(description = "期末库存金额")
    @ExcelProperty("期末库存金额")
    private BigDecimal endamt;

    @Schema(description = "单价")
    @ExcelProperty("单价")
    private BigDecimal unitprice;

    @Schema(description = "已申请数量")
    @ExcelProperty("已申请数量")
    private BigDecimal issueqty;

    @Schema(description = "在途量")
    @ExcelProperty("在途量")
    private BigDecimal routeqty;

    @Schema(description = "计划单号")
    @ExcelProperty("计划单号")
    private String plantallyno;

    @Schema(description = "厂商编号")
    @ExcelProperty("厂商编号")
    private String supplierno;

    @Schema(description = "合同编号")
    @ExcelProperty("合同编号")
    private String contractno;

    @Schema(description = "帐龄天数")
    @ExcelProperty("帐龄天数")
    private String debtagedays;

    @Schema(description = "帐龄类别")
    @ExcelProperty("帐龄类别")
    private String debtagetype;

    @Schema(description = "报警天数")
    @ExcelProperty("报警天数")
    private String prewarnmonth;

    @Schema(description = "批号")
    @ExcelProperty("批号")
    private String lotno;

    @Schema(description = "仓库")
    @ExcelProperty("仓库")
    private String storageno;

    @Schema(description = "仓库保管员")
    @ExcelProperty("仓库保管员")
    private String stgempno;

    @Schema(description = "入库日期")
    @ExcelProperty("入库日期")
    private LocalDateTime indate;

    @Schema(description = "入库操作人")
    @ExcelProperty("入库操作人")
    private String inempno;

    @Schema(description = "品名")
    @ExcelProperty("品名")
    private String matrlname;

    @Schema(description = "型号")
    @ExcelProperty("型号")
    private String spec;

    @Schema(description = "品别")
    @ExcelProperty("品别")
    private String torytype;

    @Schema(description = "库存单位")
    @ExcelProperty("库存单位")
    private String unit;

    @Schema(description = "备注")
    @ExcelProperty("备注")
    private String remark;

}
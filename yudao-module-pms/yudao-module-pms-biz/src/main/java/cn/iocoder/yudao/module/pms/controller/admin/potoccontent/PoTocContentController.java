package cn.iocoder.yudao.module.pms.controller.admin.potoccontent;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.pms.controller.admin.potoccontent.vo.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.potoccontent.PoTocContentDO;
import cn.iocoder.yudao.module.pms.service.potoccontent.PoTocContentService;

@Tag(name = "管理后台 - 合同条款内容-按行保存")
@RestController
@RequestMapping("/pms/po-toc-content")
@Validated
public class PoTocContentController {

    @Resource
    private PoTocContentService poTocContentService;

    @PostMapping("/create")
    @Operation(summary = "创建合同条款内容-按行保存")
    @PreAuthorize("@ss.hasPermission('pms:po-toc-content:create')")
    public CommonResult<Long> createPoTocContent(@Valid @RequestBody PoTocContentSaveReqVO createReqVO) {
        return success(poTocContentService.createPoTocContent(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新合同条款内容-按行保存")
    @PreAuthorize("@ss.hasPermission('pms:po-toc-content:update')")
    public CommonResult<Boolean> updatePoTocContent(@Valid @RequestBody PoTocContentSaveReqVO updateReqVO) {
        poTocContentService.updatePoTocContent(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除合同条款内容-按行保存")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:po-toc-content:delete')")
    public CommonResult<Boolean> deletePoTocContent(@RequestParam("id") Long id) {
        poTocContentService.deletePoTocContent(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得合同条款内容-按行保存")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:po-toc-content:query')")
    public CommonResult<PoTocContentRespVO> getPoTocContent(@RequestParam("id") Long id) {
        PoTocContentDO poTocContent = poTocContentService.getPoTocContent(id);
        return success(BeanUtils.toBean(poTocContent, PoTocContentRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得合同条款内容-按行保存分页")
    @PreAuthorize("@ss.hasPermission('pms:po-toc-content:query')")
    public CommonResult<PageResult<PoTocContentRespVO>> getPoTocContentPage(@Valid PoTocContentPageReqVO pageReqVO) {
        PageResult<PoTocContentDO> pageResult = poTocContentService.getPoTocContentPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PoTocContentRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出合同条款内容-按行保存 Excel")
    @PreAuthorize("@ss.hasPermission('pms:po-toc-content:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportPoTocContentExcel(@Valid PoTocContentPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<PoTocContentDO> list = poTocContentService.getPoTocContentPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "合同条款内容-按行保存.xls", "数据", PoTocContentRespVO.class,
                        BeanUtils.toBean(list, PoTocContentRespVO.class));
    }

}
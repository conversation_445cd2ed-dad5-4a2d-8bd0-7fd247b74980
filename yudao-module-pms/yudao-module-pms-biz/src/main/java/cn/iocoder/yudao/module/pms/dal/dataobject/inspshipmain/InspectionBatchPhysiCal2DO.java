package cn.iocoder.yudao.module.pms.dal.dataobject.inspshipmain;

import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * CIQ指标 DO
 *
 * <AUTHOR>
 */
@TableName("pms_inspection_batch_physi_cal2")
@KeySequence("pms_inspection_batch_physi_cal2_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InspectionBatchPhysiCal2DO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 项目
     */
    private String project;
    /**
     * 公司别
     */
    private String compId;
    /**
     * 船运代码
     */
    private String chkNo;
    /**
     * 合同号
     */
    private String contractNo;
    /**
     * 指标1
     */
    private BigDecimal chkItem0;
    /**
     * 指标1
     */
    private BigDecimal chkItem1;
    /**
     * 指标1
     */
    private BigDecimal chkItem2;
    /**
     * 指标1
     */
    private BigDecimal chkItem3;
    /**
     * 指标1
     */
    private BigDecimal chkItem4;
    /**
     * 指标1
     */
    private BigDecimal chkItem5;
    /**
     * 指标1
     */
    private BigDecimal chkItem6;
    /**
     * 指标1
     */
    private BigDecimal chkItem7;
    /**
     * 指标1
     */
    private BigDecimal chkItem8;
    /**
     * 指标1
     */
    private BigDecimal chkItem9;
    /**
     * 指标类型
     */
    private String itemType;
    /**
     * 创建人
     */
    private String createEmpNo;
    /**
     * 创建日期
     */
    private String createDate;
    /**
     * 更新人
     */
    private String updateEmpNo;
    /**
     * 更新日期
     */
    private String updateDate;
    /**
     * 父id
     */
    private Long parentId;

}
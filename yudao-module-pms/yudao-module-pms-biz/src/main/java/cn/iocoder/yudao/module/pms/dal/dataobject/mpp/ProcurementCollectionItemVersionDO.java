package cn.iocoder.yudao.module.pms.dal.dataobject.mpp;

import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 集采物料明细版本记录表 DO
 *
 * <AUTHOR>
 */
@TableName("PMS_MPP_procurement_collection_item_version")
@KeySequence("PMS_MPP_procurement_collection_item_version_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcurementCollectionItemVersionDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 集采主表ID
     */
    private Long collectionId;

    /**
     * 子表记录ID
     */
    private Long itemId;

    /**
     * 版本号
     */
    private String versionNo;

    /**
     * 料号
     */
    private String materialNo;

    /**
     * 品别代码
     */
    private String categoryCode;

    /**
     * 中文品名
     */
    private String materialNameCn;

    /**
     * 型号规格
     */
    private String modelSpec;

    /**
     * 物料描述
     */
    private String materialDesc;

    /**
     * 材质
     */
    private String materialType;

    /**
     * 图号
     */
    private String drawingNo;

    /**
     * 图档文件路径
     */
    private String drawingFile;

    /**
     * 库存单位
     */
    private String stockUnit;

    /**
     * 状况码
     */
    private String statusCode;

    /**
     * 变更类型：CREATE-新增，UPDATE-修改，DELETE-删除
     */
    private String changeType;

    /**
     * 变更原因
     */
    private String changeReason;

    /**
     * 排序号
     */
    private Integer sortOrder;
}

package cn.iocoder.yudao.module.pms.controller.admin.wmstrademain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 用户 Excel 导入 VO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免用户导入有问题
public class WmsTradeOImportExcelVO {

    @ExcelProperty("序号")
    private String serialNo;

    @ExcelProperty("申请日期")
    @NotBlank(message = "申请日期不能为空")
    private String issueDate;

    @ExcelProperty("传票日期")
    @NotBlank(message = "传票日期不能为空")
    private String vchrDate;

    @ExcelProperty("计划交易日期")
    @NotBlank(message = "计划交易不能为空")
    private String planTranDate;

    @ExcelProperty("料号")
    @NotBlank(message = "料号不能为空")
    private String matrlNo;

    @ExcelProperty("储位代码")
    @NotBlank(message = "储位代码不能为空")
    private String stgNo;

    @ExcelProperty("批号")
    private String lotNo;

    @ExcelProperty("交易数量")
    @NotNull(message = "交易数量不能为空")
    private BigDecimal transNum;

    @ExcelProperty("交易金额")
    @NotNull(message = "交易数量不能为空")
    private BigDecimal transAmt;

    @ExcelProperty("备注")
    private String remark;

}

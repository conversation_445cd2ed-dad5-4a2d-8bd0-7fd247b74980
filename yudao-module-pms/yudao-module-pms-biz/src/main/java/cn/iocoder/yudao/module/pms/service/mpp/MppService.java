package cn.iocoder.yudao.module.pms.service.mpp;

import java.util.*;
import javax.validation.*;

import cn.iocoder.yudao.module.pms.api.mpp.audit.dto.MppDTO;
import cn.iocoder.yudao.module.pms.controller.admin.mpp.vo.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.mpp.MppDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.mpp.MppDetailDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 采购计划主档 Service 接口
 *
 * <AUTHOR>
 */
public interface MppService {

    /**
     * 创建采购计划主档
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMpp(@Valid MppSaveReqVO createReqVO);

    /**
     * 更新采购计划主档
     *
     * @param updateReqVO 更新信息
     */
    void updateMpp(@Valid MppSaveReqVO updateReqVO);

    /**
     * 删除采购计划主档
     *
     * @param id 编号
     */
    void deleteMpp(Long id);

    /**
     * 获得采购计划主档
     *
     * @param id 编号
     * @return 采购计划主档
     */
    MppDO getMpp(Long id);

    /**
     * 获得采购计划主档分页
     *
     * @param pageReqVO 分页查询
     * @return 采购计划主档分页
     */
    PageResult<MppDO> getMppPage(MppPageReqVO pageReqVO);

    // ==================== 子表（采购计划明细） ====================

    Map<String,Object> calcCale(String reqno);

    /**
     * 获得采购计划明细分页
     *
     * @param pageReqVO 分页查询
     * @param mainId 主表id
     * @return 采购计划明细分页
     */
    PageResult<MppDetailDO> getMppDetailPage(PageParam pageReqVO, Long mainId);

    /**
     * 创建采购计划明细
     *
     * @param mppDetail 创建信息
     * @return 编号
     */
    Long createMppDetail(@Valid MppDetailDO mppDetail);

    /**
     * 更新采购计划明细
     *
     * @param mppDetail 更新信息
     */
    void updateMppDetail(@Valid MppDetailDO mppDetail);

    /**
     * 删除采购计划明细
     *
     * @param id 编号
     */
    void deleteMppDetail(Long id);

	/**
	 * 获得采购计划明细
	 *
	 * @param id 编号
     * @return 采购计划明细
	 */
    MppDetailDO getMppDetail(Long id);

    Long createFlow(Long loginUserId, MppSaveReqVO createReqVO);

    void updateMppMsg(MppSaveReqVO updateReqVO);

    void updateMppStatus(MppDTO mppDTO);

    MppDetailImportRespVO importUserList(List<MppDetailImportVO> list,Long mainId, Boolean updateSupport);
    List<MppDetailDO> selectMppDetailByMainId(HashMap map);
    MppDO selectMppByProcessInstanceId(HashMap map);

    PageResult<MppDetailDO> getMppDetailPage2(MppDetailPageReqVO pageReqVO);

    Long cancelFlow(MppSaveReqVO createReqVO);
}

package cn.iocoder.yudao.module.pms.controller.admin.paramsinfo;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.pms.controller.admin.paramsinfo.vo.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.paramsinfo.ParamsInfoDO;
import cn.iocoder.yudao.module.pms.service.paramsinfo.ParamsInfoService;

@Tag(name = "管理后台 - 配置信息")
@RestController
@RequestMapping("/pms/params-info")
@Validated
public class ParamsInfoController {

    @Resource
    private ParamsInfoService paramsInfoService;

    @PostMapping("/create")
    @Operation(summary = "创建配置信息")
    @PreAuthorize("@ss.hasPermission('pms:params-info:create')")
    public CommonResult<Long> createParamsInfo(@Valid @RequestBody ParamsInfoSaveReqVO createReqVO) {
        return success(paramsInfoService.createParamsInfo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新配置信息")
    @PreAuthorize("@ss.hasPermission('pms:params-info:update')")
    public CommonResult<Boolean> updateParamsInfo(@Valid @RequestBody ParamsInfoSaveReqVO updateReqVO) {
        paramsInfoService.updateParamsInfo(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除配置信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:params-info:delete')")
    public CommonResult<Boolean> deleteParamsInfo(@RequestParam("id") Long id) {
        paramsInfoService.deleteParamsInfo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得配置信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:params-info:query')")
    public CommonResult<ParamsInfoRespVO> getParamsInfo(@RequestParam("id") Long id) {
        ParamsInfoDO paramsInfo = paramsInfoService.getParamsInfo(id);
        return success(BeanUtils.toBean(paramsInfo, ParamsInfoRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得配置信息分页")
    @PreAuthorize("@ss.hasPermission('pms:params-info:query')")
    public CommonResult<PageResult<ParamsInfoRespVO>> getParamsInfoPage(@Valid ParamsInfoPageReqVO pageReqVO) {
        PageResult<ParamsInfoDO> pageResult = paramsInfoService.getParamsInfoPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ParamsInfoRespVO.class));
    }
    @GetMapping("/list")
    @Operation(summary = "获得配置信息分页")
    @PreAuthorize("@ss.hasPermission('pms:params-info:query')")
    public CommonResult<List<ParamsInfoRespVO>> getParamsInfoList(@Valid ParamsInfoPageReqVO pageReqVO) {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ParamsInfoDO> pageResult = paramsInfoService.getParamsInfoList(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ParamsInfoRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出配置信息 Excel")
    @PreAuthorize("@ss.hasPermission('pms:params-info:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportParamsInfoExcel(@Valid ParamsInfoPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ParamsInfoDO> list = paramsInfoService.getParamsInfoPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "配置信息.xls", "数据", ParamsInfoRespVO.class,
                        BeanUtils.toBean(list, ParamsInfoRespVO.class));
    }

}
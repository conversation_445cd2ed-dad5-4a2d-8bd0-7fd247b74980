package cn.iocoder.yudao.module.pms.controller.admin.inspectionbatch;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.pms.controller.admin.inspectionbatch.vo.*;
import cn.iocoder.yudao.module.pms.dal.dataobject.inspectionbatch.InspectionBatchDO;
import cn.iocoder.yudao.module.pms.dal.dataobject.inspectionbatch.InspectionBatchPhysicalDO;
import cn.iocoder.yudao.module.pms.service.inspectionbatch.InspectionBatchService;

@Tag(name = "管理后台 - 验收批次档")
@RestController
@RequestMapping("/pms/inspection-batch")
@Validated
public class InspectionBatchController {

    @Resource
    private InspectionBatchService inspectionBatchService;

    @PostMapping("/create")
    @Operation(summary = "创建验收批次档")
    @PreAuthorize("@ss.hasPermission('pms:inspection-batch:create')")
    public CommonResult<Long> createInspectionBatch(@Valid @RequestBody InspectionBatchSaveReqVO createReqVO) {
        return success(inspectionBatchService.createInspectionBatch(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新验收批次档")
    @PreAuthorize("@ss.hasPermission('pms:inspection-batch:update')")
    public CommonResult<Boolean> updateInspectionBatch(@Valid @RequestBody InspectionBatchSaveReqVO updateReqVO) {
        inspectionBatchService.updateInspectionBatch(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除验收批次档")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:inspection-batch:delete')")
    public CommonResult<Boolean> deleteInspectionBatch(@RequestParam("id") Long id) {
        inspectionBatchService.deleteInspectionBatch(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得验收批次档")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:inspection-batch:query')")
    public CommonResult<InspectionBatchRespVO> getInspectionBatch(@RequestParam("id") Long id) {
        InspectionBatchDO inspectionBatch = inspectionBatchService.getInspectionBatch(id);
        return success(BeanUtils.toBean(inspectionBatch, InspectionBatchRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得验收批次档分页")
    @PreAuthorize("@ss.hasPermission('pms:inspection-batch:query')")
    public CommonResult<PageResult<InspectionBatchRespVO>> getInspectionBatchPage(@Valid InspectionBatchPageReqVO pageReqVO) {
        PageResult<InspectionBatchDO> pageResult = inspectionBatchService.getInspectionBatchPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InspectionBatchRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出验收批次档 Excel")
    @PreAuthorize("@ss.hasPermission('pms:inspection-batch:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInspectionBatchExcel(@Valid InspectionBatchPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InspectionBatchDO> list = inspectionBatchService.getInspectionBatchPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "验收批次档.xls", "数据", InspectionBatchRespVO.class,
                        BeanUtils.toBean(list, InspectionBatchRespVO.class));
    }

    // ==================== 子表（验收批次化物性档） ====================

    @GetMapping("/inspection-batch-physical/page")
    @Operation(summary = "获得验收批次化物性档分页")
    @Parameter(name = "mainId", description = "主档id")
    @PreAuthorize("@ss.hasPermission('pms:inspection-batch:query')")
    public CommonResult<PageResult<InspectionBatchPhysicalDO>> getInspectionBatchPhysicalPage(PageParam pageReqVO,
                                                                                        @RequestParam("mainId") Long mainId) {
        return success(inspectionBatchService.getInspectionBatchPhysicalPage(pageReqVO, mainId));
    }

    @PostMapping("/inspection-batch-physical/create")
    @Operation(summary = "创建验收批次化物性档")
    @PreAuthorize("@ss.hasPermission('pms:inspection-batch:create')")
    public CommonResult<Long> createInspectionBatchPhysical(@Valid @RequestBody InspectionBatchPhysicalDO inspectionBatchPhysical) {
        return success(inspectionBatchService.createInspectionBatchPhysical(inspectionBatchPhysical));
    }

    @PutMapping("/inspection-batch-physical/update")
    @Operation(summary = "更新验收批次化物性档")
    @PreAuthorize("@ss.hasPermission('pms:inspection-batch:update')")
    public CommonResult<Boolean> updateInspectionBatchPhysical(@Valid @RequestBody InspectionBatchPhysicalDO inspectionBatchPhysical) {
        inspectionBatchService.updateInspectionBatchPhysical(inspectionBatchPhysical);
        return success(true);
    }
    @PutMapping("/inspection-batch-physical/loadAllChkitem")
    @Operation(summary = "更新成分")
    @PreAuthorize("@ss.hasPermission('pms:inspection-batch:update')")
    public CommonResult<Boolean> loadAllChkitem(@RequestParam("id") Long id) {
        inspectionBatchService.loadAllChkitem(id);
        return success(true);
    }

    @DeleteMapping("/inspection-batch-physical/delete")
    @Parameter(name = "id", description = "编号", required = true)
    @Operation(summary = "删除验收批次化物性档")
    @PreAuthorize("@ss.hasPermission('pms:inspection-batch:delete')")
    public CommonResult<Boolean> deleteInspectionBatchPhysical(@RequestParam("id") Long id) {
        inspectionBatchService.deleteInspectionBatchPhysical(id);
        return success(true);
    }

	@GetMapping("/inspection-batch-physical/get")
	@Operation(summary = "获得验收批次化物性档")
	@Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:inspection-batch:query')")
	public CommonResult<InspectionBatchPhysicalDO> getInspectionBatchPhysical(@RequestParam("id") Long id) {
	    return success(inspectionBatchService.getInspectionBatchPhysical(id));
	}

}
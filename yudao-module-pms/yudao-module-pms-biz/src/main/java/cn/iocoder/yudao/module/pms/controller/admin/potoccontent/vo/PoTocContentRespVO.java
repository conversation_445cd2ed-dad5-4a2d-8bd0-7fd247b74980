package cn.iocoder.yudao.module.pms.controller.admin.potoccontent.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 合同条款内容-按行保存 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PoTocContentRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "4243")
    @ExcelProperty("ID")
    private Long id;

    @Schema(description = "合同id - pms_po_main", requiredMode = Schema.RequiredMode.REQUIRED, example = "18198")
    @ExcelProperty("合同id - pms_po_main")
    private Long parentId;

    @Schema(description = "序号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("序号")
    private Integer srlno;

    @Schema(description = "条款内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("条款内容")
    private String content;

    @Schema(description = "新增时间")
    @ExcelProperty("新增时间")
    private LocalDateTime createTime;

    @Schema(description = "预留字段1 ")
    @ExcelProperty("预留字段1 ")
    private String preText1;

    @Schema(description = "预留字段2 ")
    @ExcelProperty("预留字段2 ")
    private String preText2;

    @Schema(description = "预留字段3 ")
    @ExcelProperty("预留字段3 ")
    private String preText3;

    @Schema(description = "预留字段4 ")
    @ExcelProperty("预留字段4 ")
    private String preText4;

    @Schema(description = "预留字段5 ")
    @ExcelProperty("预留字段5 ")
    private String preText5;

    @Schema(description = "预留字段6 ")
    @ExcelProperty("预留字段6 ")
    private String preText6;

    @Schema(description = "预留字段7 ")
    @ExcelProperty("预留字段7 ")
    private String preText7;

    @Schema(description = "预留字段8 ")
    @ExcelProperty("预留字段8 ")
    private String preText8;

    @Schema(description = "预留字段9 ")
    @ExcelProperty("预留字段9 ")
    private String preText9;

    @Schema(description = "预留字段10 ")
    @ExcelProperty("预留字段10 ")
    private String preText10;

    @Schema(description = "预留字段11 ")
    @ExcelProperty("预留字段11 ")
    private BigDecimal preNum11;

    @Schema(description = "预留字段12 ")
    @ExcelProperty("预留字段12 ")
    private BigDecimal preNum12;

    @Schema(description = "预留字段13 ")
    @ExcelProperty("预留字段13 ")
    private BigDecimal preNum13;

    @Schema(description = "预留字段14 ")
    @ExcelProperty("预留字段14 ")
    private BigDecimal preNum14;

    @Schema(description = "预留字段15 ")
    @ExcelProperty("预留字段15 ")
    private BigDecimal preNum15;

    @Schema(description = "类型 termContent:合同条款  termSupplementContent:合同补增条款", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("类型 termContent:合同条款  termSupplementContent:合同补增条款")
    private String type;

}